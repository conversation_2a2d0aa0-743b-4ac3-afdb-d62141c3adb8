#!/usr/bin/env python3
"""
Test script to verify Drive Cleanup functionality
"""

import asyncio
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

import requests
import json

def test_drive_cleanup_api():
    """Test the drive cleanup API endpoint"""
    print("🔍 TESTING DRIVE CLEANUP API ENDPOINT")
    print("=" * 50)
    
    # Test data - using dummy file IDs for testing
    test_data = {
        "file_ids": ["test_file_id_1", "test_file_id_2"],
        "drive_type": "both",
        "permanent_delete": True
    }
    
    try:
        response = requests.delete(
            "http://localhost:8001/api/gdrive/cleanup",
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response: {json.dumps(data, indent=2)}")
            print("✅ Drive cleanup API endpoint is accessible")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

def test_drive_list_api():
    """Test the drive list API endpoint"""
    print("\n🔍 TESTING DRIVE LIST API ENDPOINT")
    print("=" * 50)
    
    try:
        response = requests.get(
            "http://localhost:8001/api/gdrive/list/primary",
            timeout=30
        )
        
        print(f"Response Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response: {json.dumps(data, indent=2)}")
            print("✅ Drive list API endpoint is accessible")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

if __name__ == "__main__":
    print("Running Drive Cleanup Tests...")
    
    # Test list API first
    list_success = test_drive_list_api()
    
    # Test cleanup API
    cleanup_success = test_drive_cleanup_api()
    
    overall_success = list_success and cleanup_success
    exit(0 if overall_success else 1)