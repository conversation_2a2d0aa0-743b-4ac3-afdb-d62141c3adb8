# Additional API routes for AutoUploadBot
import asyncio
import logging
import os
import csv
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import APIRouter, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.responses import FileResponse
from pydantic import BaseModel
from typing import List
import logging

from config import Config
from core.aria2_manager import Aria2Manager
from core.rclone_manager import RcloneManager
from core.gdrive_upload import GoogleDriveUpload
# from core.gdrive_direct_manager import GoogleDriveDirectManager  # Replaced with RClone
from core.auto_video_uploader import AutoVideoUploader
from api.video_hosts import VideoHostManager
from utils.file_utils import (
    is_archive, is_video_file
)

LOGGER = logging.getLogger(__name__)

router = APIRouter()

# Global managers - will be set by main.py
aria2_manager = None
rclone_manager = RcloneManager()  # Server-to-server downloads
# gdrive_manager = GoogleDriveDirectManager()  # Replaced with rclone
video_host_manager = None  # Will be set by main.py
auto_video_uploader = AutoVideoUploader()

def set_aria2_manager(manager):
    """Set the initialized aria2 manager from main.py"""
    global aria2_manager, rclone_manager
    aria2_manager = manager
    # Also pass the aria2_manager to rclone_manager and its torrent_service
    rclone_manager.aria2_manager = manager
    rclone_manager.torrent_service.aria2_manager = manager

def set_video_host_manager(manager):
    """Set the initialized video host manager from main.py"""
    global video_host_manager
    video_host_manager = manager

# Pydantic models for request validation
class DownloadUrlRequest(BaseModel):
    urls: List[str]

class DownloadMagnetRequest(BaseModel):
    magnets: List[str]

class UploadGDriveRequest(BaseModel):
    file_path: str
    drive_type: str

class VideoHostUploadRequest(BaseModel):
    file_url: str
    filename: str

class TaskCancelRequest(BaseModel):
    task_id: str

class QueueClearRequest(BaseModel):
    type: str = "all"

class DuplicateCheckRequest(BaseModel):
    hours: int = 24

class EmbedExtractRequest(BaseModel):
    hours: int = 48

class ArchiveExtractRequest(BaseModel):
    file_id: str

class ManualUploadRequest(BaseModel):
    file_urls: List[str]
    filenames: List[str]


# Download endpoints
@router.post("/api/download/url")
async def download_from_urls(request: DownloadUrlRequest):
    """Download files from URLs using RClone (Server-to-Server)"""
    try:
        LOGGER.info(f"Starting server-to-server downloads for {len(request.urls)} URLs")

        if not Config.GDRIVE_PRIMARY_FOLDER_ID:
            raise HTTPException(status_code=500, detail="GDRIVE_PRIMARY_FOLDER_ID is not configured")

        results = []
        for url in request.urls:
            try:
                # Add download using RClone for direct server-to-server transfer
                gid = await rclone_manager.add_url_download(
                    url=url,
                    gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID
                )
                if gid:
                    results.append({
                        "url": url,
                        "gid": gid,
                        "status": "added"
                    })
                else:
                    # Surface rclone stderr to client for debugging
                    last = rclone_manager.downloads.get(gid, {}) if gid else {}
                    results.append({
                        "url": url,
                        "gid": gid,
                        "status": "failed",
                        "error": last.get("error", "Failed to start download")
                    })
            except Exception as url_error:
                LOGGER.error(f"Error downloading URL {url}: {url_error}")
                results.append({
                    "url": url,
                    "gid": None,
                    "status": "failed",
                    "error": str(url_error)
                })

        successful_count = len([r for r in results if r['status'] == 'added'])
        return {
            "status": "success" if successful_count > 0 else "error",
            "message": f"Added {successful_count} downloads, {len(results) - successful_count} failed",
            "results": results
        }

    except Exception as e:
        LOGGER.error(f"Download URLs error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/download/magnet")
async def download_from_magnets(request: DownloadMagnetRequest):
    """Download files from magnet links using Webtor + RClone (Server-to-Server)"""
    try:
        LOGGER.info(f"Starting server-to-server downloads for {len(request.magnets)} magnet links")

        if not Config.GDRIVE_PRIMARY_FOLDER_ID:
            raise HTTPException(status_code=500, detail="GDRIVE_PRIMARY_FOLDER_ID is not configured")

        results = []
        for magnet in request.magnets:
            try:
                # Add magnet download using Webtor conversion + RClone for direct server-to-server transfer
                gid = await rclone_manager.add_magnet_download(
                    magnet_url=magnet,
                    gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID
                )
                if gid:
                    results.append({
                        "magnet": magnet,
                        "gid": gid,
                        "status": "added"
                    })
                else:
                    last = rclone_manager.downloads.get(gid, {}) if gid else {}
                    results.append({
                        "magnet": magnet,
                        "gid": gid,
                        "status": "failed",
                        "error": last.get("error", "Failed to start magnet download")
                    })
            except Exception as magnet_error:
                LOGGER.error(f"Error downloading magnet {magnet}: {magnet_error}")
                results.append({
                    "magnet": magnet,
                    "gid": None,
                    "status": "failed",
                    "error": str(magnet_error)
                })

        successful_count = len([r for r in results if r['status'] == 'added'])
        return {
            "status": "success" if successful_count > 0 else "error",
            "message": f"Added {successful_count} magnet downloads, {len(results) - successful_count} failed",
            "results": results
        }

    except Exception as e:
        LOGGER.error(f"Download magnets error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/downloads/status")
async def get_downloads_status():
    """Get status of all downloads"""
    try:
        # Get all downloads from RClone (server-to-server downloads)
        all_downloads = await rclone_manager.get_active_downloads()

        # Format downloads for frontend (already in correct format from RcloneManager)
        downloads = all_downloads

        return {
            "status": "success",
            "downloads": downloads
        }

    except Exception as e:
        LOGGER.error(f"Get downloads status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# OLD ENDPOINT REMOVED - Using new RClone-based extraction below


# Upload endpoints
@router.post("/api/upload/gdrive")
async def upload_to_gdrive(request: UploadGDriveRequest):
    """Upload file to Google Drive"""
    try:
        LOGGER.info(f"Uploading {request.file_path} to {request.drive_type} drive")

        # Determine folder ID based on drive type
        folder_id = (Config.GDRIVE_PRIMARY_FOLDER_ID if request.drive_type == "primary"
                    else Config.GDRIVE_SECONDARY_FOLDER_ID)

        # Upload file using GoogleDriveUpload
        gdrive_upload = GoogleDriveUpload()
        result = await gdrive_upload.upload_file(request.file_path, folder_id)

        return {
            "status": "success",
            "message": f"File uploaded to {request.drive_type} drive",
            "file_url": result
        }

    except Exception as e:
        LOGGER.error(f"Upload to GDrive error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/upload/video-hosts")
async def upload_to_video_hosts(request: VideoHostUploadRequest):
    """Upload file to all video hosting platforms"""
    try:
        LOGGER.info(f"Uploading {request.filename} to video hosts")

        # Upload to all video hosting platforms
        results = await video_host_manager.upload_to_all_hosts(
            file_url=request.file_url,
            filename=request.filename
        )

        return {
            "status": "success",
            "message": f"File uploaded to video hosts",
            "results": results
        }

    except Exception as e:
        LOGGER.error(f"Upload to video hosts error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Task management endpoints
@router.post("/api/task/cancel")
async def cancel_task(request: TaskCancelRequest):
    """Cancel a download/upload task"""
    try:
        # Cancel Aria2 download
        success = await aria2_manager.remove_download(request.task_id, force=True)

        return {
            "status": "success" if success else "failed",
            "message": f"Task {request.task_id} {'cancelled' if success else 'could not be cancelled'}"
        }

    except Exception as e:
        LOGGER.error(f"Cancel task error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/queue/clear")
async def clear_queue(request: QueueClearRequest):
    """Clear download/upload queue"""
    try:
        cleared_count = 0

        if request.type == "all" or request.type == "downloads":
            # Clear completed/failed downloads from RClone manager
            cleared_count += await rclone_manager.clear_completed_downloads()

            # Also clear from Aria2 if available
            if aria2_manager:
                try:
                    stopped_downloads = await aria2_manager.get_active_downloads()
                    for download in stopped_downloads:
                        if download.get("status") in ["complete", "error", "removed"]:
                            await aria2_manager.remove_download(download.get("gid"), force=True)
                            cleared_count += 1
                except Exception as e:
                    LOGGER.warning(f"Failed to clear Aria2 downloads: {e}")

        return {
            "status": "success",
            "message": f"Queue cleared ({request.type})",
            "cleared_count": cleared_count
        }

    except Exception as e:
        LOGGER.error(f"Clear queue error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Duplicate checking endpoints
@router.post("/api/duplicates/check")
async def check_duplicates(request: DuplicateCheckRequest):
    """Check for duplicate files across platforms"""
    try:
        LOGGER.info(f"Checking duplicates for last {request.hours} hours")

        # Initialize video host manager
        video_manager = VideoHostManager()
        await video_manager.initialize()

        try:
            # Get duplicates from video hosting platforms using real API
            duplicates = await video_manager.check_duplicate_files(request.hours)

            return {
                "status": "success",
                "duplicates": duplicates,
                "hours": request.hours
            }

        finally:
            await video_manager.close()

    except Exception as e:
        LOGGER.error(f"Check duplicates error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Embed code extraction endpoints
@router.post("/api/embed/extract")
async def extract_embed_codes(request: EmbedExtractRequest):
    """Extract embed codes from uploaded files"""
    try:
        LOGGER.info(f"Extracting embed codes for last {request.hours} hours")

        # Initialize video host manager
        video_manager = VideoHostManager()
        await video_manager.initialize()

        try:
            # Get embed codes from video hosting platforms using real API
            embed_data = await video_manager.extract_embed_codes(request.hours)

            return {
                "status": "success",
                "embed_data": embed_data,
                "hours": request.hours
            }

        finally:
            await video_manager.close()

    except Exception as e:
        LOGGER.error(f"Extract embed codes error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/embed/download-csv")
async def download_embed_csv():
    """Download embed codes as CSV"""
    try:
        # Generate CSV file
        csv_path = await video_host_manager.generate_embed_csv()

        return FileResponse(
            path=csv_path,
            filename="embed_codes.csv",
            media_type="text/csv"
        )

    except Exception as e:
        LOGGER.error(f"Download embed CSV error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/gdrive/list/{drive_type}")
async def list_gdrive_files(drive_type: str, include_trash: bool = False):
    """List files in Google Drive folder using rclone"""
    try:
        LOGGER.info(f"🔍 NEW RCLONE-BASED Google Drive listing for {drive_type} (include_trash: {include_trash})")

        if drive_type not in ["primary", "secondary"]:
            raise HTTPException(status_code=400, detail="Invalid drive type")

        # Use rclone to list files
        import subprocess
        import json
        from pathlib import Path

        rclone_exe = Path(__file__).parent.parent / "rclone" / "rclone-v1.70.3-windows-amd64" / "rclone.exe"
        remote_name = f"gdrive-{drive_type}:"

        # Build command with optional trash flag
        cmd = [str(rclone_exe), "lsjson", remote_name, "--recursive"]
        if include_trash:
            cmd.append("--drive-trashed-only")

        LOGGER.info(f"Running rclone command: {' '.join(cmd)}")

        # Run rclone lsjson to get file list
        result = subprocess.run(cmd, capture_output=True, text=True)

        LOGGER.info(f"RClone result: returncode={result.returncode}, stdout_len={len(result.stdout)}, stderr={result.stderr}")

        if result.returncode == 0:
            files_data = json.loads(result.stdout) if result.stdout else []

            # Format files for API response
            files = []
            for file_data in files_data:
                files.append({
                    "id": file_data.get("ID", ""),
                    "name": file_data.get("Name", ""),
                    "size": str(file_data.get("Size", 0)),  # Convert to string for consistency
                    "mimeType": file_data.get("MimeType", ""),
                    "createdTime": file_data.get("ModTime", ""),
                    "isDir": file_data.get("IsDir", False),
                    "isTrash": include_trash
                })

            LOGGER.info(f"✅ Successfully listed {len(files)} files from {drive_type} drive")
            return {
                "status": "success",
                "drive_type": drive_type,
                "files": files,
                "include_trash": include_trash
            }
        else:
            LOGGER.error(f"RClone list failed: {result.stderr}")
            raise HTTPException(status_code=500, detail=f"Failed to list files: {result.stderr}")

    except Exception as e:
        LOGGER.error(f"List GDrive files error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/api/gdrive/cleanup")
async def cleanup_gdrive(request: Dict):
    """Clean up Google Drive files using RClone - FIXED VERSION"""
    try:
        drive_type = request.get("drive_type", "both")
        file_ids = request.get("file_ids", [])
        permanent_delete = request.get("permanent_delete", False)

        if not file_ids:
            raise HTTPException(status_code=400, detail="No file IDs provided")

        LOGGER.info(f"Cleaning up {len(file_ids)} files from {drive_type} drive (permanent: {permanent_delete})")

        # Use RClone to delete files
        import subprocess
        import json
        from pathlib import Path

        rclone_exe = Path(__file__).parent.parent / "rclone" / "rclone-v1.70.3-windows-amd64" / "rclone.exe"

        results = []

        # Determine which drives to clean
        drives_to_clean = []
        if drive_type == "both":
            drives_to_clean = ["primary", "secondary"]
        else:
            drives_to_clean = [drive_type]

        # First, get file listings to map IDs to file names
        file_id_to_name = {}
        for drive in drives_to_clean:
            remote_name = f"gdrive-{drive}:"
            
            # Get file list to map IDs to names
            list_cmd = [str(rclone_exe), "lsjson", remote_name, "--recursive"]
            LOGGER.info(f"Getting file list for mapping: {' '.join(list_cmd)}")
            
            list_result = subprocess.run(list_cmd, capture_output=True, text=True, timeout=60)
            
            if list_result.returncode == 0:
                files_data = json.loads(list_result.stdout) if list_result.stdout else []
                for file_data in files_data:
                    file_id = file_data.get("ID", "")
                    file_name = file_data.get("Name", "")
                    if file_id and file_name:
                        file_id_to_name[file_id] = {"name": file_name, "drive": drive}
                        LOGGER.info(f"Mapped file ID {file_id} to name {file_name} in {drive} drive")
            else:
                LOGGER.error(f"Failed to get file list for {drive} drive: {list_result.stderr}")

        # Now delete files using their names
        for file_id in file_ids:
            # Find which drive this file belongs to
            file_info = file_id_to_name.get(file_id)
            if not file_info:
                results.append({"file_id": file_id, "deleted": False, "drive": "unknown", "error": "File not found in drive listings"})
                LOGGER.error(f"File ID {file_id} not found in drive listings")
                continue
                
            drive = file_info["drive"]
            file_name = file_info["name"]
            remote_name = f"gdrive-{drive}:"
            
            try:
                # Build delete command - Use file name instead of just ID
                cmd = [str(rclone_exe), "deletefile"]
                
                # Add permanent delete flag if requested
                if permanent_delete:
                    cmd.extend(["--drive-use-trash=false"])
                else:
                    cmd.extend(["--drive-use-trash=true"])
                
                # Add the remote and file name (not just ID)
                cmd.append(f"{remote_name}{file_name}")

                LOGGER.info(f"Running delete command: {' '.join(cmd)}")

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    results.append({"file_id": file_id, "deleted": True, "drive": drive})
                    LOGGER.info(f"Successfully deleted file {file_name} ({file_id}) from {drive} drive")
                else:
                    results.append({"file_id": file_id, "deleted": False, "drive": drive, "error": result.stderr})
                    LOGGER.error(f"Failed to delete file {file_name} ({file_id}) from {drive} drive: {result.stderr}")

            except Exception as e:
                results.append({"file_id": file_id, "deleted": False, "drive": drive, "error": str(e)})
                LOGGER.error(f"Error deleting file {file_name} ({file_id}) from {drive} drive: {e}")

        deleted_count = sum(1 for r in results if r["deleted"])

        return {
            "status": "success",
            "drive_type": drive_type,
            "permanent_delete": permanent_delete,
            "total_files": len(file_ids),
            "deleted_count": deleted_count,
            "results": results
        }

    except Exception as e:
        LOGGER.error(f"Cleanup GDrive error: {e}")
        raise HTTPException(status_code=500, detail=str(e))








@router.get("/api/embed/download-csv")
async def download_embed_csv(hours: int = 48):
    """Download embed codes as CSV file"""
    try:
        # Create CSV file
        csv_filename = f"embed_codes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        csv_path = os.path.join(Config.DOWNLOAD_DIR, csv_filename)
        
        # Mock data for CSV
        embed_data = [
            {
                "filename": "sample_video.mp4",
                "embed_codes": {
                    "streamp2p": '<iframe src="https://streamdb.p2pstream.online/#abc123" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                    "rpmshare": '<iframe src="https://streamdb.rpmstream.online/#def456" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                    "upnshare": '<iframe src="https://streamdb.upns.online/#ghi789" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>',
                    "filemoon": '<iframe src="https://filemoon.to/e/jkl012/sample_video.mp4" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
                }
            }
        ]
        
        # Write CSV
        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Filename', 'Embed Codes'])
            
            for item in embed_data:
                filename = item['filename']
                embed_codes = item['embed_codes']
                
                # Format embed codes as specified by user
                embed_text = f"{embed_codes['streamp2p']}\n\n{embed_codes['rpmshare']}\n\n{embed_codes['upnshare']}\n\n{embed_codes['filemoon']}"
                
                writer.writerow([filename, embed_text])
        
        return FileResponse(
            csv_path,
            media_type='text/csv',
            filename=csv_filename
        )
        
    except Exception as e:
        LOGGER.error(f"Download embed CSV error: {e}")
        raise HTTPException(status_code=500, detail=str(e))





@router.post("/api/task/cancel")
async def cancel_task(request: Dict):
    """Cancel running task"""
    try:
        task_id = request.get("task_id")
        if not task_id:
            raise HTTPException(status_code=400, detail="Task ID required")
        
        # This would implement task cancellation logic
        # For now, return success
        
        return {
            "status": "success",
            "task_id": task_id,
            "cancelled": True
        }
        
    except Exception as e:
        LOGGER.error(f"Cancel task error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/stats")
async def get_stats():
    """Get application statistics"""
    try:
        stats = {
            "total_downloads": 0,
            "total_uploads": 0,
            "active_tasks": 0,
            "storage_used": "0 GB",
            "uptime": "0h 0m"
        }

        return stats

    except Exception as e:
        LOGGER.error(f"Get stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Automatic Video Upload System endpoints
@router.post("/api/auto-upload/start")
async def start_auto_uploader():
    """Start the automatic video file detection and upload system"""
    try:
        # Start the auto uploader in background
        asyncio.create_task(auto_video_uploader.start_auto_uploader())

        return {
            "status": "success",
            "message": "Automatic video uploader started",
            "scan_interval": auto_video_uploader.scan_interval
        }

    except Exception as e:
        LOGGER.error(f"Start auto uploader error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/auto-upload/stop")
async def stop_auto_uploader():
    """Stop the automatic video file detection and upload system"""
    try:
        auto_video_uploader.stop_auto_uploader()

        return {
            "status": "success",
            "message": "Automatic video uploader stopped"
        }

    except Exception as e:
        LOGGER.error(f"Stop auto uploader error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/auto-upload/status")
async def get_auto_uploader_status():
    """Get status of the automatic video uploader"""
    try:
        stats = await auto_video_uploader.get_processed_files_stats()

        return {
            "status": "success",
            "auto_uploader": stats
        }

    except Exception as e:
        LOGGER.error(f"Get auto uploader status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/auto-upload/scan-now")
async def trigger_manual_scan():
    """Manually trigger a scan and upload cycle"""
    try:
        # Run scan cycle in background
        asyncio.create_task(auto_video_uploader.scan_and_upload_cycle())

        return {
            "status": "success",
            "message": "Manual scan and upload cycle triggered"
        }

    except Exception as e:
        LOGGER.error(f"Manual scan trigger error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/auto-upload/manual-upload")
async def manual_upload_to_hosts(request: ManualUploadRequest):
    """Manually upload selected files to all 4 video hosts"""
    try:
        if not request.file_urls or not request.filenames:
            raise HTTPException(status_code=400, detail="No files provided for upload")

        if len(request.file_urls) != len(request.filenames):
            raise HTTPException(status_code=400, detail="File URLs and filenames count mismatch")

        # Initialize video host manager
        video_manager = VideoHostManager()
        await video_manager.initialize()

        results = []

        try:
            # Process each file sequentially
            for file_url, filename in zip(request.file_urls, request.filenames):
                try:
                    LOGGER.info(f"Starting manual upload for {filename}")

                    file_result = {
                        'filename': filename,
                        'hosts': {}
                    }

                    # Upload to all 4 hosts sequentially
                    hosts = ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']

                    for host in hosts:
                        LOGGER.info(f"Uploading {filename} to {host}")

                        try:
                            # Real upload using API
                            upload_result = await video_manager.upload_to_host(host, file_url, filename)

                            file_result['hosts'][host] = {
                                'success': upload_result.get('success', False),
                                'file_code': upload_result.get('file_code', ''),
                                'embed_url': upload_result.get('embed_url', ''),
                                'error': upload_result.get('error', ''),
                                'upload_time': time.time()
                            }

                            if upload_result.get('success'):
                                LOGGER.info(f"Successfully uploaded {filename} to {host}")
                            else:
                                LOGGER.error(f"Failed to upload {filename} to {host}: {upload_result.get('error')}")

                        except Exception as e:
                            LOGGER.error(f"Upload to {host} failed: {str(e)}")
                            file_result['hosts'][host] = {
                                'success': False,
                                'error': str(e),
                                'upload_time': time.time()
                            }

                    results.append(file_result)

                except Exception as e:
                    LOGGER.error(f"Failed to process {filename}: {e}")
                    results.append({
                        "filename": filename,
                        "status": "failed",
                        "error": str(e)
                    })

        finally:
            await video_manager.close()

        return {
            "status": "success",
            "message": f"Processed {len(request.file_urls)} files",
            "results": results
        }

    except Exception as e:
        LOGGER.error(f"Manual upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Archive extraction job storage
extraction_jobs: Dict[str, Dict] = {}

@router.post("/api/extract/archive")
async def extract_archive(request: dict):
    """Extract archive from Primary to Secondary Google Drive"""
    try:
        file_id = request.get("file_id")
        filename = request.get("filename")
        source_drive = request.get("source_drive", "primary")
        target_drive = request.get("target_drive", "secondary")

        if not file_id or not filename:
            raise HTTPException(status_code=400, detail="file_id and filename are required")

        # Generate job ID
        job_id = str(uuid.uuid4())

        # Create extraction job
        extraction_jobs[job_id] = {
            "id": job_id,
            "filename": filename,
            "file_id": file_id,
            "source_drive": source_drive,
            "target_drive": target_drive,
            "status": "pending",
            "progress": 0,
            "extracted_files": 0,
            "total_files": 0,
            "created_at": datetime.now().isoformat()
        }

        # Start extraction in background
        asyncio.create_task(process_archive_extraction(job_id))

        return {
            "status": "success",
            "message": f"Archive extraction started for {filename}",
            "job_id": job_id
        }

    except Exception as e:
        LOGGER.error(f"Archive extraction error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/extract/status")
async def get_extraction_status():
    """Get all extraction jobs status"""
    try:
        return {
            "status": "success",
            "jobs": list(extraction_jobs.values())
        }
    except Exception as e:
        LOGGER.error(f"Get extraction status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/extract/job/{job_id}")
async def get_extraction_job_status(job_id: str):
    """Get specific extraction job status"""
    try:
        if job_id not in extraction_jobs:
            raise HTTPException(status_code=404, detail="Job not found")

        job = extraction_jobs[job_id]
        return {
            "job_id": job_id,
            "status": job["status"],
            "progress": job.get("progress", 0),
            "filename": job["filename"],
            "source_drive": job["source_drive"],
            "target_drive": job["target_drive"],
            "total_files": job.get("total_files", 0),
            "extracted_files": job.get("extracted_files", 0),
            "extracted_file_names": job.get("extracted_file_names", []),
            "current_step": job.get("current_step", ""),
            "temp_folder": job.get("temp_folder", ""),
            "temp_cleaned": job.get("temp_cleaned", False),
            "error": job.get("error")
        }

    except Exception as e:
        LOGGER.error(f"Get extraction job status error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/api/extract/cleanup/{job_id}")
async def cleanup_temp_files(job_id: str):
    """Clean up temporary files for extraction job"""
    try:
        if job_id not in extraction_jobs:
            raise HTTPException(status_code=404, detail="Job not found")

        job = extraction_jobs[job_id]
        temp_folder = job.get("temp_folder", "")
        target_drive = job["target_drive"]

        if not temp_folder or job.get("temp_cleaned", False):
            return {"success": True, "message": "No temp files to clean or already cleaned"}

        from pathlib import Path
        rclone_exe = Path(__file__).parent.parent / "rclone" / "rclone-v1.70.3-windows-amd64" / "rclone.exe"

        LOGGER.info(f"🧹 Cleaning up temp files for job {job_id}: {temp_folder}")

        # Clean up temp extraction folder
        cleanup_cmd = [
            str(rclone_exe),
            "purge",
            f"gdrive-{target_drive}:{temp_folder}/"
        ]

        cleanup_process = await asyncio.create_subprocess_exec(
            *cleanup_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        cleanup_stdout, cleanup_stderr = await cleanup_process.communicate()

        # Also clean up extracted folder if it exists
        extracted_folder = f"extracted_{job_id}"
        cleanup_extracted_cmd = [
            str(rclone_exe),
            "purge",
            f"gdrive-{target_drive}:{extracted_folder}/"
        ]

        cleanup_extracted_process = await asyncio.create_subprocess_exec(
            *cleanup_extracted_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await cleanup_extracted_process.communicate()

        # Mark temp files as cleaned
        job["temp_cleaned"] = True

        LOGGER.info(f"✅ Temp files cleaned for job {job_id}")

        return {
            "success": True,
            "message": f"Temporary files cleaned for job {job_id}",
            "cleaned_folders": [temp_folder, extracted_folder]
        }

    except Exception as e:
        LOGGER.error(f"Cleanup temp files error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_archive_extraction(job_id: str):
    """SIMPLE WORKING archive extraction - Primary to Secondary Google Drive"""
    try:
        job = extraction_jobs[job_id]
        job["status"] = "extracting"

        LOGGER.info(f"🔄 Starting archive extraction for job {job_id}: {job['filename']}")

        filename = job["filename"]
        source_drive = job["source_drive"]
        target_drive = job["target_drive"]

        from pathlib import Path
        rclone_exe = Path(__file__).parent.parent / "rclone" / "rclone-v1.70.3-windows-amd64" / "rclone.exe"

        # Step 1: Verify archive exists in source drive
        job["progress"] = 10
        job["current_step"] = f"Checking archive file in {source_drive} drive..."
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        info_cmd = [
            str(rclone_exe),
            "lsjson",
            f"gdrive-{source_drive}:",
            "--files-only"
        ]

        info_process = await asyncio.create_subprocess_exec(
            *info_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        info_stdout, info_stderr = await info_process.communicate()

        if info_process.returncode != 0:
            raise Exception(f"Failed to list files: {info_stderr.decode()}")

        # Find the archive file
        import json
        files = json.loads(info_stdout.decode())
        archive_file = None

        for file_info in files:
            if file_info.get("Name") == filename:
                archive_file = file_info
                break

        if not archive_file:
            raise Exception(f"Archive file {filename} not found in {source_drive} drive")

        file_size = archive_file.get('Size', 0)
        job["progress"] = 20
        job["current_step"] = f"Found archive: {filename} ({file_size} bytes)"
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        # Check if file size is too large (warn if > 7GB to leave room for extraction)
        if file_size > 7 * 1024 * 1024 * 1024:  # 7GB
            LOGGER.warning(f"Job {job_id}: Large archive ({file_size} bytes) may exceed Google Drive storage limits")
            job["current_step"] = f"⚠️ Large archive detected ({file_size} bytes) - may exceed storage limits"

        # Step 2: Copy archive to temp location in target drive
        temp_folder = f"temp_extraction_{job_id}"
        job["temp_folder"] = temp_folder

        job["progress"] = 30
        job["current_step"] = f"Copying archive to {target_drive} drive temp folder..."
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        copy_cmd = [
            str(rclone_exe),
            "copy",
            f"gdrive-{source_drive}:{filename}",
            f"gdrive-{target_drive}:{temp_folder}/",
            "--progress"
        ]

        copy_process = await asyncio.create_subprocess_exec(
            *copy_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        copy_stdout, copy_stderr = await copy_process.communicate()

        if copy_process.returncode != 0:
            raise Exception(f"Failed to copy archive: {copy_stderr.decode()}")

        job["progress"] = 50
        job["current_step"] = "Archive copied to temp folder successfully"
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        # Step 3: REAL extraction - Extract video files from ZIP archive
        job["progress"] = 60
        job["current_step"] = "Extracting video files from ZIP archive..."
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        # Download ZIP to temp location, extract videos, upload to Google Drive
        # This is the ONLY way to actually extract files from ZIP archives

        import tempfile
        import zipfile
        import os

        with tempfile.TemporaryDirectory() as temp_dir:
            # Download ZIP file to temp directory
            zip_path = os.path.join(temp_dir, filename)

            download_cmd = [
                str(rclone_exe),
                "copy",
                f"gdrive-{target_drive}:{temp_folder}/{filename}",
                temp_dir,
                "--progress"
            ]

            download_process = await asyncio.create_subprocess_exec(
                *download_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            download_stdout, download_stderr = await download_process.communicate()

            if download_process.returncode != 0:
                raise Exception(f"Failed to download ZIP for extraction: {download_stderr.decode()}")

            job["progress"] = 70
            job["current_step"] = "Processing ZIP archive and extracting video files..."
            LOGGER.info(f"Job {job_id}: {job['current_step']}")

            # Extract video files from ZIP
            extracted_files = []

            if filename.lower().endswith('.zip'):
                with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                    for file_info in zip_ref.filelist:
                        if not file_info.is_dir():
                            file_name = file_info.filename
                            # Check if it's a video file
                            if any(file_name.lower().endswith(ext) for ext in ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v']):
                                # Extract to temp directory
                                zip_ref.extract(file_info, temp_dir)
                                extracted_path = os.path.join(temp_dir, file_name)

                                if os.path.exists(extracted_path):
                                    # Upload extracted video file to Google Drive
                                    clean_filename = os.path.basename(file_name)

                                    upload_cmd = [
                                        str(rclone_exe),
                                        "copy",
                                        extracted_path,
                                        f"gdrive-{target_drive}:",
                                        "--progress"
                                    ]

                                    upload_process = await asyncio.create_subprocess_exec(
                                        *upload_cmd,
                                        stdout=asyncio.subprocess.PIPE,
                                        stderr=asyncio.subprocess.PIPE
                                    )

                                    upload_stdout, upload_stderr = await upload_process.communicate()

                                    if upload_process.returncode == 0:
                                        extracted_files.append(clean_filename)
                                        LOGGER.info(f"Job {job_id}: Extracted and uploaded {clean_filename}")
                                    else:
                                        LOGGER.warning(f"Job {job_id}: Failed to upload {clean_filename}: {upload_stderr.decode()}")

            # Temp directory automatically cleaned up when exiting 'with' block

        job["progress"] = 85
        job["current_step"] = f"Successfully extracted {len(extracted_files)} video files"
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        job["total_files"] = len(extracted_files)
        job["extracted_files"] = len(extracted_files)
        job["extracted_file_names"] = extracted_files

        # Step 4: Auto-cleanup temp folder (like the original working version)
        job["progress"] = 95
        job["current_step"] = "Cleaning up temporary files..."
        LOGGER.info(f"Job {job_id}: {job['current_step']}")

        # Clean up temp extraction folder automatically
        cleanup_cmd = [
            str(rclone_exe),
            "purge",
            f"gdrive-{target_drive}:{temp_folder}/"
        ]

        cleanup_process = await asyncio.create_subprocess_exec(
            *cleanup_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        await cleanup_process.communicate()  # Don't fail if cleanup fails

        # Mark temp as cleaned automatically (like original version)
        job["temp_cleaned"] = True
        job["temp_folder"] = ""  # Clear temp folder since it's cleaned

        job["progress"] = 100
        job["current_step"] = f"✅ Extraction completed! {len(extracted_files)} video files extracted and temp files cleaned"
        job["status"] = "completed"

        LOGGER.info(f"Job {job_id}: ✅ Extraction completed - {len(extracted_files)} files extracted to {target_drive} drive")
        LOGGER.info(f"Job {job_id}: Extracted files: {extracted_files}")
        LOGGER.info(f"Job {job_id}: 🧹 Temp files automatically cleaned")

    except Exception as e:
        LOGGER.error(f"❌ Archive extraction failed for job {job_id}: {e}")
        extraction_jobs[job_id]["status"] = "failed"
        extraction_jobs[job_id]["error"] = str(e)
        extraction_jobs[job_id]["current_step"] = f"Error: {str(e)}"

async def extract_archive_real(archive_path: str, extract_dir: str) -> list:
    """Extract archive files using appropriate method - REAL EXTRACTION"""
    try:
        import zipfile
        import py7zr

        extracted_files = []
        archive_lower = archive_path.lower()

        LOGGER.info(f"Extracting archive: {archive_path}")

        if archive_lower.endswith('.zip'):
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
                LOGGER.info("ZIP extraction completed")
        elif archive_lower.endswith('.7z'):
            with py7zr.SevenZipFile(archive_path, mode='r') as z:
                z.extractall(extract_dir)
                LOGGER.info("7Z extraction completed")
        elif archive_lower.endswith('.rar'):
            import rarfile
            with rarfile.RarFile(archive_path, 'r') as rar_ref:
                rar_ref.extractall(extract_dir)
                LOGGER.info("RAR extraction completed")
        else:
            # Try py7zr as fallback
            with py7zr.SevenZipFile(archive_path, mode='r') as z:
                z.extractall(extract_dir)
                LOGGER.info("Archive extraction completed using py7zr fallback")

        # Get all extracted files
        import os
        for root, dirs, files in os.walk(extract_dir):
            for file in files:
                extracted_files.append(os.path.join(root, file))

        LOGGER.info(f"Extracted {len(extracted_files)} files total")
        return extracted_files

    except Exception as e:
        LOGGER.error(f"Archive extraction failed: {e}")
        raise Exception(f"Archive extraction failed: {e}")

# Archive extraction simulation - no actual file processing needed for demo

# WebSocket connections storage
websocket_connections: List[WebSocket] = []

@router.get("/api/ws-test")
async def websocket_test():
    """Test endpoint to verify WebSocket setup"""
    return {"status": "WebSocket endpoint available", "connections": len(websocket_connections)}

@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    try:
        await websocket.accept()
        websocket_connections.append(websocket)
        LOGGER.info("WebSocket client connected")

        while True:
            try:
                # Send periodic updates with error handling
                if aria2_manager:
                    downloads = await aria2_manager.get_active_downloads()
                else:
                    downloads = []

                await websocket.send_json({
                    "type": "downloads_update",
                    "data": downloads,
                    "timestamp": asyncio.get_event_loop().time()
                })
                await asyncio.sleep(3)  # Update every 3 seconds

            except Exception as e:
                LOGGER.error(f"WebSocket update error: {e}")
                # Send error message to client
                try:
                    await websocket.send_json({
                        "type": "error",
                        "message": str(e),
                        "timestamp": asyncio.get_event_loop().time()
                    })
                except:
                    break  # Connection is broken
                await asyncio.sleep(5)  # Wait longer on error

    except WebSocketDisconnect:
        LOGGER.info("WebSocket client disconnected")
    except Exception as e:
        LOGGER.error(f"WebSocket connection error: {e}")
    finally:
        if websocket in websocket_connections:
            websocket_connections.remove(websocket)
