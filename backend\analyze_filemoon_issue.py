#!/usr/bin/env python3
"""
Analyze FileMoon duplicate issue in detail
The ALL Time filter shows 149 files but should be 107-110
This suggests FileMoon is contributing extra files somehow
"""

import asyncio
import logging
import sys
from pathlib import Path
from collections import defaultdict

sys.path.insert(0, str(Path(__file__).parent))
from api.video_hosts import VideoHostManager

async def analyze_filemoon_issue():
    """Analyze why FileMoon is contributing extra files"""
    print("🌙 DETAILED FILEMOON ANALYSIS")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Get raw files from each platform
        print("Getting raw files from each platform:")
        
        platform_files = {}
        for host in ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']:
            if not vm.api_keys[host]:
                continue
                
            files = await vm.list_recent_uploads(host, 999999)
            platform_files[host] = files
            print(f"  {host}: {len(files)} raw files")
        
        # Analyze FileMoon files specifically
        filemoon_files = platform_files.get('filemoon', [])
        print(f"\n🔍 ANALYZING {len(filemoon_files)} FILEMOON FILES:")
        
        # Check for obvious duplicates in FileMoon
        filemoon_by_normalized = defaultdict(list)
        for file_info in filemoon_files:
            filename = file_info.get('filename', '')
            normalized = vm._normalize_filename(filename)
            filemoon_by_normalized[normalized].append(filename)
        
        filemoon_duplicates = 0
        for normalized, filenames in filemoon_by_normalized.items():
            if len(filenames) > 1:
                filemoon_duplicates += len(filenames) - 1
                print(f"  FILEMOON DUPLICATE GROUP: '{normalized}'")
                for fname in filenames:
                    print(f"    - {fname}")
        
        print(f"  FileMoon internal duplicates: {filemoon_duplicates}")
        
        # Compare FileMoon files with other platforms
        print(f"\n🔄 CROSS-PLATFORM DUPLICATE ANALYSIS:")
        
        # Get normalized filenames from other platforms
        other_platform_normalized = set()
        for host in ['streamp2p', 'rpmshare', 'upnshare']:
            if host in platform_files:
                for file_info in platform_files[host]:
                    filename = file_info.get('filename', '')
                    normalized = vm._normalize_filename(filename)
                    other_platform_normalized.add(normalized)
        
        print(f"  Other platforms have {len(other_platform_normalized)} unique normalized files")
        
        # Check how many FileMoon files are NOT in other platforms
        filemoon_only = []
        filemoon_shared = []
        
        for file_info in filemoon_files:
            filename = file_info.get('filename', '')
            normalized = vm._normalize_filename(filename)
            
            if normalized in other_platform_normalized:
                filemoon_shared.append(filename)
            else:
                filemoon_only.append(filename)
        
        print(f"  FileMoon files also on other platforms: {len(filemoon_shared)}")
        print(f"  FileMoon-only files: {len(filemoon_only)}")
        
        # Show some FileMoon-only files to understand the pattern
        print(f"\n📋 SAMPLE FILEMOON-ONLY FILES:")
        for i, filename in enumerate(filemoon_only[:10]):
            print(f"  {i+1}. {filename}")
        
        # Check if these are legitimate additional files or problematic ones
        print(f"\n🧪 PATTERN ANALYSIS OF FILEMOON-ONLY FILES:")
        patterns = defaultdict(int)
        
        for filename in filemoon_only:
            filename_lower = filename.lower()
            
            # Check for specific patterns that might indicate issues
            if '&#40;' in filename or '&#41;' in filename:
                patterns['html_encoded'] += 1
            elif filename.endswith('WEB-DL'):
                patterns['no_extension'] += 1
            elif len(filename.split()) < 3:
                patterns['too_short'] += 1
            elif '2025' in filename and 'WEB-DL' in filename:
                patterns['2025_webdl'] += 1
            elif 'Hindi' in filename or 'English' in filename:
                patterns['language_tagged'] += 1
            else:
                patterns['normal'] += 1
        
        print("  Pattern distribution:")
        for pattern, count in patterns.items():
            print(f"    {pattern}: {count} files")
        
        # Now test if removing problematic patterns helps
        print(f"\n🔧 TESTING FILTERED EXTRACTION:")
        
        # Test extraction with FileMoon filtering
        embed_data = await vm.extract_embed_codes(999999)
        current_count = len(embed_data)
        
        print(f"  Current extraction: {current_count} files")
        
        # Count how many came from FileMoon only
        filemoon_only_in_results = 0
        for file_data in embed_data:
            embed_codes = file_data.get('embed_codes', {})
            if 'filemoon' in embed_codes and len(embed_codes) == 1:
                # This file only exists on FileMoon
                filename = file_data['filename']
                normalized = vm._normalize_filename(filename)
                if normalized not in other_platform_normalized:
                    filemoon_only_in_results += 1
        
        print(f"  Files only from FileMoon in results: {filemoon_only_in_results}")
        print(f"  Expected total: 107-110 files")
        print(f"  Excess files: {current_count - 110}")
        
        return filemoon_only_in_results, current_count
        
    finally:
        await vm.close()

if __name__ == "__main__":
    asyncio.run(analyze_filemoon_issue())