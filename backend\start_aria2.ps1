# PowerShell script to start Aria2
Write-Host "Starting Aria2 RPC server..." -ForegroundColor Green
Set-Location $PSScriptRoot

# Check if aria2 exists
$aria2Path = "aria2\aria2-1.37.0-win-64bit-build1\aria2c.exe"
if (-not (Test-Path $aria2Path)) {
    Write-Host "ERROR: Aria2 not found at $aria2Path" -ForegroundColor Red
    Write-Host "Please run: python setup_aria2.py" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if config exists
$configPath = "aria2\aria2.conf"
if (-not (Test-Path $configPath)) {
    Write-Host "ERROR: Aria2 config not found at $configPath" -ForegroundColor Red
    Write-Host "Please run: python setup_aria2.py" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Starting Aria2 with config: $configPath" -ForegroundColor Cyan
Write-Host "Aria2 will be available at: http://localhost:6800/jsonrpc" -ForegroundColor Cyan
Write-Host "Keep this window open while using the bot!" -ForegroundColor Yellow
Write-Host ""

# Start aria2
& $aria2Path --conf-path=$configPath

Write-Host ""
Write-Host "Aria2 has stopped." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
