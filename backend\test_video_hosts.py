#!/usr/bin/env python3
"""Test video hosting platforms with real API keys"""
import asyncio
import aiohttp
import json

async def test_video_hosts():
    async with aiohttp.ClientSession() as session:
        
        # Test video hosting platforms
        print("Testing video hosting platforms with real API keys...")
        payload = {
            "file_url": "https://httpbin.org/uuid",
            "filename": "test.mp4"
        }
        
        async with session.post("http://localhost:8001/api/upload/video-hosts", json=payload) as response:
            print(f"Status: {response.status}")
            if response.status == 200:
                data = await response.json()
                platforms = list(data.get("upload_results", {}).keys())
                print(f"✅ Platforms: {platforms}")
                print(f"✅ Upload results: {data.get('upload_results', {})}")
                print(f"✅ Embed codes: {data.get('embed_codes', {})}")
                
                # Check only 4 platforms
                if len(platforms) == 4 and "lulustream" not in platforms:
                    print("✅ Correct 4 platforms (no Lulustream)")
                else:
                    print(f"❌ Wrong platforms: {platforms}")
            else:
                error_text = await response.text()
                print(f"❌ Failed: {response.status} - {error_text}")

if __name__ == "__main__":
    asyncio.run(test_video_hosts())
