#!/usr/bin/env python3
"""
Custom Uvicorn server that forces Proactor<PERSON>ventLoop on Windows
This fixes the subprocess issue with Uvicorn --reload on Windows
"""
import asyncio
import platform
import logging
from uvicorn import Config, Server

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

class ProactorServer(Server):
    """Custom Uvicorn server that forces ProactorEventLoop on Windows"""
    
    def run(self, sockets=None):
        """Override run method to set ProactorEventLoop before starting"""
        if platform.system() == "Windows":
            try:
                # Force ProactorEventLoop on Windows for subprocess support
                from asyncio.windows_events import ProactorEventLoop
                loop = ProactorEventLoop()
                asyncio.set_event_loop(loop)
                LOGGER.info("✅ FORCED ProactorEventLoop for Windows subprocess support")
                LOGGER.info(f"✅ Event Loop Type: {type(asyncio.get_event_loop())}")
            except Exception as e:
                LOGGER.error(f"❌ Failed to set ProactorEventLoop: {e}")
        
        # Run the server with the correct event loop
        asyncio.run(self.serve(sockets=sockets))

def start_server():
    """Start the FastAPI server with ProactorEventLoop support"""
    LOGGER.info("🚀 Starting AutoUploadBot Backend Server...")
    LOGGER.info(f"🖥️  Platform: {platform.system()}")
    
    # Import the FastAPI app
    from main import app
    
    # Create Uvicorn config
    config = Config(
        app=app,
        host="localhost",
        port=8001,  # Use current backend port
        reload=True,
        reload_dirs=["backend"],
        log_level="info"
    )
    
    # Create and start the custom server
    if platform.system() == "Windows":
        LOGGER.info("🔧 Using ProactorServer for Windows subprocess support")
        server = ProactorServer(config=config)
    else:
        LOGGER.info("🔧 Using standard Uvicorn server")
        server = Server(config=config)
    
    try:
        server.run()
    except KeyboardInterrupt:
        LOGGER.info("🛑 Server stopped by user")
    except Exception as e:
        LOGGER.error(f"❌ Server error: {e}")
        raise

if __name__ == "__main__":
    start_server()
