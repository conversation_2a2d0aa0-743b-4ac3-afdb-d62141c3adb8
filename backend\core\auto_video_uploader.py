# Automatic Video File Detection and Upload System
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set
import json
import os

from config import Config
from core.rclone_manager import <PERSON><PERSON>loneManager
from api.video_hosts import VideoHostManager
from utils.file_utils import is_video_file

LOGGER = logging.getLogger(__name__)


class AutoVideoUploader:
    """
    Automatic Video File Detection and Upload System
    Scans both Google Drive folders for video files and uploads to video hosts
    """
    
    def __init__(self):
        self.rclone_manager = RcloneManager()
        self.video_host_manager = VideoHostManager()
        self.processed_files_cache = "processed_files.json"
        self.processed_files: Set[str] = self._load_processed_files()
        self.is_running = False
        self.scan_interval = 300  # 5 minutes
        
    def _load_processed_files(self) -> Set[str]:
        """Load list of already processed files"""
        try:
            if os.path.exists(self.processed_files_cache):
                with open(self.processed_files_cache, 'r') as f:
                    data = json.load(f)
                    return set(data.get('processed_files', []))
        except Exception as e:
            LOGGER.error(f"Error loading processed files cache: {e}")
        return set()
    
    def _save_processed_files(self):
        """Save list of processed files"""
        try:
            data = {
                'processed_files': list(self.processed_files),
                'last_updated': datetime.now().isoformat()
            }
            with open(self.processed_files_cache, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            LOGGER.error(f"Error saving processed files cache: {e}")
    
    async def scan_drive_for_videos(self, drive_type: str, folder_id: str) -> List[Dict]:
        """Scan a Google Drive folder for video files"""
        try:
            LOGGER.info(f"Scanning {drive_type} drive for video files...")
            
            # Get all files from the drive
            files = await self.gdrive_manager.list_files_in_folder(folder_id)
            
            # Filter for video files that haven't been processed
            video_files = []
            for file in files:
                file_id = file.get('id')
                filename = file.get('name', '')
                mime_type = file.get('mimeType', '')
                
                # Check if it's a video file and not already processed
                if (is_video_file(filename) and 
                    file_id not in self.processed_files and
                    'video' in mime_type.lower()):
                    
                    video_files.append({
                        'id': file_id,
                        'name': filename,
                        'size': file.get('size', '0'),
                        'drive_type': drive_type,
                        'folder_id': folder_id,
                        'created_time': file.get('createdTime'),
                        'download_url': f"https://drive.google.com/uc?id={file_id}&export=download"
                    })
            
            LOGGER.info(f"Found {len(video_files)} new video files in {drive_type} drive")
            return video_files
            
        except Exception as e:
            LOGGER.error(f"Error scanning {drive_type} drive: {e}")
            return []
    
    async def upload_video_to_hosts(self, video_file: Dict) -> Dict:
        """Upload a video file to all 4 video hosting platforms"""
        try:
            filename = video_file['name']
            file_url = video_file['download_url']
            file_id = video_file['id']
            drive_type = video_file['drive_type']
            
            LOGGER.info(f"Starting upload of {filename} from {drive_type} drive to video hosts")
            
            # Upload to all video hosting platforms
            upload_results = await self.video_host_manager.upload_file_to_all_hosts(
                file_url=file_url,
                filename=filename
            )
            
            # Generate embed codes
            embed_codes = await self.video_host_manager.get_embed_codes(
                upload_results, filename
            )
            
            # Mark file as processed
            self.processed_files.add(file_id)
            self._save_processed_files()
            
            result = {
                'status': 'success',
                'filename': filename,
                'drive_type': drive_type,
                'file_id': file_id,
                'upload_results': upload_results,
                'embed_codes': embed_codes,
                'processed_at': datetime.now().isoformat()
            }
            
            LOGGER.info(f"Successfully uploaded {filename} to video hosts")
            return result
            
        except Exception as e:
            LOGGER.error(f"Error uploading {video_file['name']} to video hosts: {e}")
            return {
                'status': 'error',
                'filename': video_file['name'],
                'error': str(e),
                'processed_at': datetime.now().isoformat()
            }
    
    async def scan_and_upload_cycle(self):
        """Single scan and upload cycle for both drives"""
        try:
            LOGGER.info("Starting automatic video file scan and upload cycle")
            
            # Scan both drives for video files
            primary_videos = await self.scan_drive_for_videos(
                "primary", Config.GDRIVE_PRIMARY_FOLDER_ID
            )
            secondary_videos = await self.scan_drive_for_videos(
                "secondary", Config.GDRIVE_SECONDARY_FOLDER_ID
            )
            
            # Combine all video files
            all_videos = primary_videos + secondary_videos
            
            if not all_videos:
                LOGGER.info("No new video files found in either drive")
                return
            
            LOGGER.info(f"Found {len(all_videos)} total video files to upload")
            
            # Upload each video file to all hosts
            upload_results = []
            for video_file in all_videos:
                try:
                    result = await self.upload_video_to_hosts(video_file)
                    upload_results.append(result)
                    
                    # Small delay between uploads to prevent overwhelming the system
                    await asyncio.sleep(10)
                    
                except Exception as e:
                    LOGGER.error(f"Error processing {video_file['name']}: {e}")
                    upload_results.append({
                        'status': 'error',
                        'filename': video_file['name'],
                        'error': str(e)
                    })
            
            # Log summary
            successful_uploads = len([r for r in upload_results if r['status'] == 'success'])
            failed_uploads = len([r for r in upload_results if r['status'] == 'error'])
            
            LOGGER.info(f"Upload cycle completed: {successful_uploads} successful, {failed_uploads} failed")
            
        except Exception as e:
            LOGGER.error(f"Error in scan and upload cycle: {e}")
    
    async def start_auto_uploader(self):
        """Start the automatic video file detection and upload system"""
        if self.is_running:
            LOGGER.warning("Auto uploader is already running")
            return
        
        self.is_running = True
        LOGGER.info(f"Starting automatic video uploader (scan interval: {self.scan_interval}s)")
        
        try:
            while self.is_running:
                await self.scan_and_upload_cycle()
                
                # Wait for next scan cycle
                await asyncio.sleep(self.scan_interval)
                
        except Exception as e:
            LOGGER.error(f"Auto uploader error: {e}")
        finally:
            self.is_running = False
            LOGGER.info("Automatic video uploader stopped")
    
    def stop_auto_uploader(self):
        """Stop the automatic video file detection and upload system"""
        LOGGER.info("Stopping automatic video uploader...")
        self.is_running = False
    
    async def get_processed_files_stats(self) -> Dict:
        """Get statistics about processed files"""
        return {
            'total_processed': len(self.processed_files),
            'last_updated': datetime.now().isoformat(),
            'cache_file': self.processed_files_cache,
            'scan_interval': self.scan_interval,
            'is_running': self.is_running
        }
