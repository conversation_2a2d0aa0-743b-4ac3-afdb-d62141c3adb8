#!/usr/bin/env python3
"""
Comprehensive endpoint verification for all 4 video hosting platforms:
Filemoon, StreamP2P, RPMShare, UPnShare

Verifies that all required API endpoints are accessible and properly configured.
"""

import asyncio
import aiohttp
import logging
import sys
from pathlib import Path
from typing import Dict, Any

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

class VideoHostEndpointVerifier:
    """Verify endpoints for all video hosting platforms"""
    
    def __init__(self):
        self.session = None
        self.results = {}
        
    async def initialize(self):
        """Initialize HTTP session"""
        if not self.session:
            self.session = aiohttp.ClientSession()
            
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            
    async def verify_filemoon_endpoints(self) -> Dict[str, Any]:
        """Verify Filemoon API endpoints"""
        try:
            api_key = Config.FILEMOON_API_KEY
            base_url = Config.FILEMOON_API_URL.rstrip('/')
            
            if not api_key:
                return {
                    'status': 'error',
                    'error': 'API key not configured'
                }
                
            endpoints = {
                'remote_upload': f'{base_url}/remote/add',
                'file_list': f'{base_url}/file/list',
                'file_status': f'{base_url}/remote/status'
            }
            
            endpoint_results = {}
            
            # Test each endpoint
            for name, url in endpoints.items():
                try:
                    # For remote upload, we'll just check if endpoint is accessible
                    if name == 'remote_upload':
                        # Test with minimal params to check accessibility
                        params = {'key': api_key, 'url': 'https://httpbin.org/uuid'}
                        async with self.session.get(url, params=params, timeout=10) as response:
                            endpoint_results[name] = {
                                'status': 'accessible' if response.status < 500 else 'error',
                                'status_code': response.status,
                                'response_preview': (await response.text())[:200]
                            }
                    else:
                        # For other endpoints, test with API key
                        params = {'key': api_key}
                        async with self.session.get(url, params=params, timeout=10) as response:
                            endpoint_results[name] = {
                                'status': 'accessible' if response.status < 500 else 'error',
                                'status_code': response.status,
                                'response_preview': (await response.text())[:200]
                            }
                except Exception as e:
                    endpoint_results[name] = {
                        'status': 'error',
                        'error': str(e)
                    }
            
            return {
                'status': 'success',
                'base_url': base_url,
                'endpoints': endpoint_results
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
            
    async def verify_p2p_endpoints(self, host: str) -> Dict[str, Any]:
        """Verify P2P host API endpoints (StreamP2P, RPMShare, UPnShare)"""
        try:
            # Get configuration based on host
            if host == 'streamp2p':
                api_key = Config.STREAMP2P_API_KEY
                base_url = Config.STREAMP2P_API_URL
            elif host == 'rpmshare':
                api_key = Config.RPMSHARE_API_KEY
                base_url = Config.RPMSHARE_API_URL
            elif host == 'upnshare':
                api_key = Config.UPNSHARE_API_KEY
                base_url = Config.UPNSHARE_API_URL
            else:
                return {
                    'status': 'error',
                    'error': f'Unsupported host: {host}'
                }
                
            if not api_key:
                return {
                    'status': 'error',
                    'error': f'{host.upper()} API key not configured'
                }
                
            endpoints = {
                'upload': f'{base_url}/video/advance-upload',
                'manage': f'{base_url}/video/manage',
                'status': f'{base_url}/video/advance-upload/{{task_id}}'  # Placeholder
            }
            
            headers = {
                'api-token': api_key,
                'Content-Type': 'application/json'
            }
            
            endpoint_results = {}
            
            # Test upload endpoint (POST)
            try:
                async with self.session.post(endpoints['upload'], headers=headers, timeout=10) as response:
                    endpoint_results['upload'] = {
                        'status': 'accessible' if response.status < 500 else 'error',
                        'status_code': response.status,
                        'method': 'POST'
                    }
            except Exception as e:
                endpoint_results['upload'] = {
                    'status': 'error',
                    'error': str(e),
                    'method': 'POST'
                }
                
            # Test manage endpoint (GET)
            try:
                async with self.session.get(endpoints['manage'], headers=headers, timeout=10) as response:
                    endpoint_results['manage'] = {
                        'status': 'accessible' if response.status < 500 else 'error',
                        'status_code': response.status,
                        'method': 'GET'
                    }
            except Exception as e:
                endpoint_results['manage'] = {
                    'status': 'error',
                    'error': str(e),
                    'method': 'GET'
                }
            
            return {
                'status': 'success',
                'base_url': base_url,
                'endpoints': endpoint_results
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
            
    async def verify_all_endpoints(self):
        """Verify endpoints for all video hosting platforms"""
        print("=" * 80)
        print("📡 COMPREHENSIVE VIDEO HOST ENDPOINT VERIFICATION")
        print("=" * 80)
        
        await self.initialize()
        
        try:
            # Verify Filemoon
            print("\n🔍 Verifying Filemoon endpoints...")
            self.results['filemoon'] = await self.verify_filemoon_endpoints()
            
            # Verify StreamP2P
            print("\n🔍 Verifying StreamP2P endpoints...")
            self.results['streamp2p'] = await self.verify_p2p_endpoints('streamp2p')
            
            # Verify RPMShare
            print("\n🔍 Verifying RPMShare endpoints...")
            self.results['rpmshare'] = await self.verify_p2p_endpoints('rpmshare')
            
            # Verify UPnShare
            print("\n🔍 Verifying UPnShare endpoints...")
            self.results['upnshare'] = await self.verify_p2p_endpoints('upnshare')
            
            # Display results
            self.display_results()
            
        finally:
            await self.close()
            
    def display_results(self):
        """Display verification results"""
        print("\n" + "=" * 80)
        print("📊 ENDPOINT VERIFICATION SUMMARY")
        print("=" * 80)
        
        for host, result in self.results.items():
            print(f"\n{host.upper()}:")
            
            if result['status'] == 'error':
                print(f"  ❌ Configuration Error: {result['error']}")
                continue
                
            print(f"  🌐 Base URL: {result['base_url']}")
            
            endpoints = result.get('endpoints', {})
            accessible_count = 0
            total_endpoints = len(endpoints)
            
            for endpoint_name, endpoint_result in endpoints.items():
                if endpoint_result['status'] == 'accessible':
                    print(f"  ✅ {endpoint_name}: ACCESSIBLE (Status: {endpoint_result['status_code']})")
                    accessible_count += 1
                elif endpoint_result['status'] == 'error':
                    error = endpoint_result.get('error', 'Unknown error')
                    print(f"  ❌ {endpoint_name}: ERROR - {error}")
                else:
                    print(f"  ⚠️  {endpoint_name}: {endpoint_result['status'].upper()}")
            
            if accessible_count == total_endpoints:
                print(f"  🎉 ALL {total_endpoints} ENDPOINTS ACCESSIBLE")
            else:
                print(f"  ⚠️  {accessible_count}/{total_endpoints} ENDPOINTS ACCESSIBLE")
                
        # Overall summary
        print("\n" + "=" * 80)
        print("📈 OVERALL SUMMARY")
        print("=" * 80)
        
        total_hosts = len(self.results)
        fully_working_hosts = 0
        partially_working_hosts = 0
        non_working_hosts = 0
        
        for host, result in self.results.items():
            if result['status'] == 'error':
                non_working_hosts += 1
            else:
                endpoints = result.get('endpoints', {})
                accessible_count = sum(1 for ep in endpoints.values() if ep['status'] == 'accessible')
                total_endpoints = len(endpoints)
                
                if accessible_count == total_endpoints:
                    fully_working_hosts += 1
                elif accessible_count > 0:
                    partially_working_hosts += 1
                else:
                    non_working_hosts += 1
                    
        print(f"✅ Fully Working Hosts: {fully_working_hosts}")
        print(f"⚠️  Partially Working Hosts: {partially_working_hosts}")
        print(f"❌ Non-Working Hosts: {non_working_hosts}")
        print(f"📊 Total Hosts: {total_hosts}")
        
        if fully_working_hosts == total_hosts:
            print("\n🎉 ALL VIDEO HOST ENDPOINTS ARE FULLY CONFIGURED AND ACCESSIBLE!")
        elif fully_working_hosts > 0:
            print(f"\n⚠️  {fully_working_hosts}/{total_hosts} HOSTS ARE FULLY WORKING")
        else:
            print("\n❌ NO HOSTS ARE FULLY WORKING - CHECK CONFIGURATION")

async def main():
    """Main verification function"""
    verifier = VideoHostEndpointVerifier()
    await verifier.verify_all_endpoints()
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)