#!/usr/bin/env python3
"""
Test All Fixes for AutoUploadBot
Tests all the fixes made to address user concerns
"""
import asyncio
import aiohttp
import logging
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8001"

async def test_auto_upload_endpoints():
    """Test auto-upload endpoints that were giving 404 errors"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("🔍 Testing Auto-Upload Endpoints...")
            
            # Test auto-upload status
            async with session.get(f"{API_BASE_URL}/api/auto-upload/status") as response:
                LOGGER.info(f"Auto-upload status: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Auto-upload status working: {data}")
                else:
                    LOGGER.error(f"❌ Auto-upload status failed: {response.status}")
                    return False
            
            # Test manual scan
            async with session.post(f"{API_BASE_URL}/api/auto-upload/scan-now") as response:
                LOGGER.info(f"Manual scan: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Manual scan working: {data}")
                else:
                    LOGGER.error(f"❌ Manual scan failed: {response.status}")
                    return False
            
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ Auto-upload endpoints test failed: {e}")
            return False

async def test_download_endpoints():
    """Test download endpoints that were giving 500 errors"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("📥 Testing Download Endpoints...")
            
            # Test URL download
            payload = {"urls": ["https://httpbin.org/uuid"]}
            async with session.post(f"{API_BASE_URL}/api/download/url", json=payload) as response:
                LOGGER.info(f"URL download: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ URL download working: {data}")
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ URL download failed: {response.status} - {error_text}")
                    return False
            
            # Test magnet download (with a simple magnet)
            test_magnet = "magnet:?xt=urn:btih:c12fe1c06bba254a9dc9f519b335aa7c1367a88a"
            payload = {"magnets": [test_magnet]}
            async with session.post(f"{API_BASE_URL}/api/download/magnet", json=payload) as response:
                LOGGER.info(f"Magnet download: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Magnet download working: {data}")
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Magnet download failed: {response.status} - {error_text}")
                    return False
            
            return True
            
        except Exception as e:
            LOGGER.error(f"❌ Download endpoints test failed: {e}")
            return False

async def test_embed_code_formats():
    """Test embed code formats are correct"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("🔗 Testing Embed Code Formats...")
            
            async with session.post(f"{API_BASE_URL}/api/embed/extract", json={}) as response:
                if response.status == 200:
                    data = await response.json()
                    embed_data = data.get("embed_data", [])
                    
                    if embed_data:
                        sample_embeds = embed_data[0].get("embed_codes", {})
                        
                        # Check formats match user specifications
                        correct_formats = True
                        
                        # Check Streamp2p format
                        if "streamp2p" in sample_embeds:
                            embed = sample_embeds["streamp2p"]
                            if 'streamdb.p2pstream.online/#' in embed and 'width="100%"' in embed:
                                LOGGER.info("✅ Streamp2p format correct")
                            else:
                                LOGGER.error(f"❌ Streamp2p format incorrect: {embed}")
                                correct_formats = False
                        
                        # Check RPMShare format
                        if "rpmshare" in sample_embeds:
                            embed = sample_embeds["rpmshare"]
                            if 'streamdb.rpmstream.online/#' in embed and 'width="100%"' in embed:
                                LOGGER.info("✅ RPMShare format correct")
                            else:
                                LOGGER.error(f"❌ RPMShare format incorrect: {embed}")
                                correct_formats = False
                        
                        # Check Filemoon format
                        if "filemoon" in sample_embeds:
                            embed = sample_embeds["filemoon"]
                            if 'filemoon.to/e/' in embed and 'width="640"' in embed and 'height="360"' in embed:
                                LOGGER.info("✅ Filemoon format correct")
                            else:
                                LOGGER.error(f"❌ Filemoon format incorrect: {embed}")
                                correct_formats = False
                        
                        # Check UpnShare format
                        if "upnshare" in sample_embeds:
                            embed = sample_embeds["upnshare"]
                            if 'streamdb.upns.online/#' in embed and 'width="100%"' in embed:
                                LOGGER.info("✅ UpnShare format correct")
                            else:
                                LOGGER.error(f"❌ UpnShare format incorrect: {embed}")
                                correct_formats = False
                        
                        return correct_formats
                    else:
                        LOGGER.info("✅ Embed extraction working (no data to check formats)")
                        return True
                else:
                    LOGGER.error(f"❌ Embed extraction failed: {response.status}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Embed code format test failed: {e}")
            return False

async def test_video_hosting_platforms():
    """Test only the 4 specified platforms (no Lulustream)"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("🎥 Testing Video Hosting Platforms (4 platforms only)...")
            
            payload = {
                "file_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4",
                "filename": "test_4_platforms.mp4"
            }
            
            async with session.post(f"{API_BASE_URL}/api/upload/video-hosts", json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    upload_results = data.get("upload_results", {})
                    embed_codes = data.get("embed_codes", {})
                    
                    # Check only 4 platforms are present
                    expected_platforms = {"streamp2p", "rpmshare", "upnshare", "filemoon"}
                    actual_platforms = set(upload_results.keys())
                    
                    if actual_platforms == expected_platforms:
                        LOGGER.info("✅ Correct 4 platforms only (no Lulustream)")
                        LOGGER.info(f"Platforms: {list(actual_platforms)}")
                        return True
                    else:
                        LOGGER.error(f"❌ Wrong platforms: Expected {expected_platforms}, Got {actual_platforms}")
                        return False
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Video hosting test failed: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Video hosting platforms test failed: {e}")
            return False

async def main():
    """Run all fix tests"""
    LOGGER.info("🚀 Testing All Fixes")
    
    tests = [
        ("Auto-Upload Endpoints", test_auto_upload_endpoints),
        ("Download Endpoints", test_download_endpoints),
        ("Embed Code Formats", test_embed_code_formats),
        ("Video Hosting Platforms (4 only)", test_video_hosting_platforms),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        LOGGER.info(f"\n{'='*60}")
        LOGGER.info(f"🧪 Testing: {test_name}")
        LOGGER.info(f"{'='*60}")
        
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            LOGGER.info(f"Result: {status}")
        except Exception as e:
            results[test_name] = False
            LOGGER.error(f"❌ FAILED with exception: {e}")
        
        # Wait between tests
        await asyncio.sleep(2)
    
    # Summary
    LOGGER.info(f"\n{'='*60}")
    LOGGER.info("📊 FIX TEST SUMMARY")
    LOGGER.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        LOGGER.info(f"{test_name}: {status}")
    
    LOGGER.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        LOGGER.info("🎉 ALL FIXES WORKING! Issues resolved!")
    else:
        LOGGER.warning(f"⚠️ {total - passed} tests still failing.")

if __name__ == "__main__":
    asyncio.run(main())
