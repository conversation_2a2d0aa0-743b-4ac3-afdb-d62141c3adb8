import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Code, Download, Calendar, RefreshCw, FileText, Trash2 } from "lucide-react";
import { apiService, EmbedData } from "@/services/api";
import { useToast } from "@/hooks/use-toast";

const EmbedExtractor = () => {
  const [timeRange, setTimeRange] = useState("48");
  const [extractionMode, setExtractionMode] = useState<"hours" | "all">("hours");
  const [embedData, setEmbedData] = useState<EmbedData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isExtracting, setIsExtracting] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const [stats, setStats] = useState({
    totalFiles: 0,
    extractedCodes: 0,
    lastExtraction: "Never"
  });
  const { toast } = useToast();

  // Load embed data on component mount
  useEffect(() => {
    loadEmbedData();
  }, [timeRange]);

  const loadEmbedData = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.extractEmbedCodes(parseInt(timeRange));
      setEmbedData(response.embed_data || []);

      setStats({
        totalFiles: response.embed_data?.length || 0,
        extractedCodes: (response.embed_data?.length || 0) * 4, // 4 platforms per file
        lastExtraction: new Date().toLocaleString()
      });

    } catch (error) {
      console.error("Failed to load embed data:", error);
      toast({
        title: "Error",
        description: "Failed to load embed data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtractCodes = async () => {
    setIsExtracting(true);
    try {
      const hours = extractionMode === "all" ? 999999 : parseInt(timeRange);
      const response = await apiService.extractEmbedCodes(hours);
      setEmbedData(response.embed_data || []);

      setStats({
        totalFiles: response.embed_data?.length || 0,
        extractedCodes: (response.embed_data?.length || 0) * 4,
        lastExtraction: new Date().toLocaleString()
      });

      toast({
        title: "Success",
        description: `Extracted embed codes for ${response.embed_data?.length || 0} files`,
      });

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to extract embed codes",
        variant: "destructive",
      });
    } finally {
      setIsExtracting(false);
    }
  };

  const handleClearResults = () => {
    setEmbedData([]);
    setStats({
      totalFiles: 0,
      extractedCodes: 0,
      lastExtraction: "Never"
    });
    toast({
      title: "Results Cleared",
      description: "All embed extraction results have been cleared",
    });
  };

  const handleDownloadCSV = async () => {
    setIsDownloading(true);
    try {
      // Create CSV content with correct embed code formats
      const csvContent = generateCSVContent();

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `embed_codes_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Success",
        description: "CSV file downloaded successfully",
      });

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download CSV",
        variant: "destructive",
      });
    } finally {
      setIsDownloading(false);
    }
  };

  const generateCSVContent = (): string => {
    let csvContent = "Filename,Embed Codes\n";

    embedData.forEach(item => {
      const filename = item.filename;

      // Format embed codes exactly as specified by user with correct formats
      // Extract just the codes from the full embed codes if they exist
      const getCodeFromEmbed = (embedCode: string | undefined, platform: string) => {
        if (!embedCode) return '';

        try {
          // Extract the src attribute if a full iframe string was provided
          const srcMatch = embedCode.match(/src\s*=\s*\"([^\"]+)\"/i);
          const src = srcMatch ? srcMatch[1] : embedCode;

          // For Filemoon the code is the first path segment after /e/
          if (platform === 'filemoon') {
            const fmMatch = src.match(/filemoon\.to\/e\/([^\/\?\#]+)/i);
            return fmMatch ? fmMatch[1] : '';
          }

          // For streamp2p/rpmshare/upnshare the code is after the #
          const hashMatch = src.match(/#([^\/\?\"']+)/);
          return hashMatch ? hashMatch[1] : '';
        } catch (e) {
          return '';
        }
      };

      const embedCodes = [
        `<iframe src="https://streamdb.p2pstream.online/#${getCodeFromEmbed(item.embed_codes.streamp2p, 'streamp2p')}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>`,
        '',
        `<iframe src="https://streamdb.rpmstream.online/#${getCodeFromEmbed(item.embed_codes.rpmshare, 'rpmshare')}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>`,
        '',
  `<iframe src="https://filemoon.to/e/${getCodeFromEmbed(item.embed_codes.filemoon, 'filemoon') || ''}/${filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>`,
        '',
        `<iframe src="https://streamdb.upns.online/#${getCodeFromEmbed(item.embed_codes.upnshare, 'upnshare')}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>`
      ].join('\n');

      // Escape quotes and add to CSV
      const escapedEmbedCodes = `"${embedCodes.replace(/"/g, '""')}"`;
      csvContent += `"${filename}",${escapedEmbedCodes}\n`;
    });

    return csvContent;
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold font-mono mb-2">EMBED CODE EXTRACTOR</h2>
        <p className="text-muted-foreground font-mono">EXTRACT EMBED CODES • CSV EXPORT • TIME-BASED FILTERING</p>
      </div>

      {/* Extraction Settings */}
      <Card className="brutal-card">
        <CardHeader className="bg-purple text-purple-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">EXTRACTION SETTINGS</CardTitle>
        </CardHeader>
        <CardContent className="p-4 space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-start">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-bold mb-2 font-mono">
                  EXTRACTION MODE
                </label>
                <Select value={extractionMode} onValueChange={(value: "hours" | "all") => setExtractionMode(value)}>
                  <SelectTrigger className="brutal-button">
                    <SelectValue placeholder="Select extraction mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hours">Last 48 Hours</SelectItem>
                    <SelectItem value="all">All Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {extractionMode === "hours" && (
                <div>
                  <label className="block text-sm font-bold mb-2 font-mono">
                    TIME RANGE (HOURS)
                  </label>
                  <Input
                    type="number"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="brutal-border font-mono"
                    placeholder="48"
                  />
                  <div className="text-xs text-muted-foreground mt-1 font-mono">
                    Extract embeds from files uploaded in the last {timeRange} hours
                  </div>
                </div>
              )}

              {extractionMode === "all" && (
                <div className="text-xs text-muted-foreground font-mono">
                  Extract embed codes from all files since the beginning
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Button
                onClick={handleExtractCodes}
                disabled={isExtracting}
                className="bg-purple text-purple-foreground brutal-button w-full"
              >
                {isExtracting ? (
                  <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                ) : (
                  <Code className="w-3 h-3 mr-2" />
                )}
                {isExtracting ? "EXTRACTING..." : "EXTRACT EMBED CODES"}
              </Button>
              <Button
                onClick={handleDownloadCSV}
                disabled={isDownloading || embedData.length === 0}
                className="bg-accent text-accent-foreground brutal-button w-full"
              >
                {isDownloading ? (
                  <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                ) : (
                  <Download className="w-3 h-3 mr-2" />
                )}
                {isDownloading ? "DOWNLOADING..." : "EXPORT TO CSV"}
              </Button>
              <Button
                onClick={handleClearResults}
                disabled={embedData.length === 0}
                className="bg-destructive text-destructive-foreground brutal-button w-full"
              >
                <Trash2 className="w-3 h-3 mr-2" />
                CLEAR QUEUE
              </Button>
            </div>
          </div>

          <div className="bg-muted p-4 brutal-border">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-3 h-3" />
              <span className="font-mono font-bold text-sm">SUPPORTED PLATFORMS:</span>
            </div>
            <div className="font-mono text-sm">
              STREAMP2P, RPMSHARE, UPNSHARE, FILEMOON
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      <Card className="brutal-card">
        <CardHeader className="bg-orange text-orange-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">NO EMBED CODES FOUND</CardTitle>
        </CardHeader>
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <Code className="w-14 h-14 mx-auto text-orange" />
            <h3 className="text-2xl font-bold font-mono">NO EMBEDS EXTRACTED</h3>
            <p className="text-muted-foreground font-mono">
              No files with embed codes found in the specified time range
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmbedExtractor;