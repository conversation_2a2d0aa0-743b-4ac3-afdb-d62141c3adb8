#!/usr/bin/env python3
"""
Test Aria2 connection directly
"""
import asyncio
import requests

def test_aria2_rpc():
    """Test Aria2 RPC connection"""
    print("Testing Aria2 RPC connection...")
    
    url = "http://127.0.0.1:6800/jsonrpc"
    payload = {
        "jsonrpc": "2.0",
        "id": "test",
        "method": "aria2.getVersion",
        "params": []
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if "result" in data:
                print("SUCCESS: Aria2 RPC is working!")
                print(f"Aria2 Version: {data['result']['version']}")
                return True
        
        print("FAIL: Aria2 RPC not responding correctly")
        return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

async def test_aria2_websocket():
    """Test Aria2 WebSocket connection"""
    print("\nTesting Aria2 WebSocket connection...")
    
    try:
        from aioaria2 import Aria2WebsocketClient
        
        aria2_url = "http://127.0.0.1:6800/jsonrpc"
        aria2 = await Aria2WebsocketClient.new(aria2_url)
        
        # Test getting version
        version = await aria2.getVersion()
        print(f"SUCCESS: Aria2 WebSocket working!")
        print(f"Aria2 Version: {version['version']}")
        
        await aria2.close()
        return True
        
    except Exception as e:
        print(f"ERROR: {e}")
        return False

async def main():
    """Run all tests"""
    print("Testing Aria2 Connection")
    print("=" * 30)
    
    # Test RPC
    rpc_result = test_aria2_rpc()
    
    # Test WebSocket
    ws_result = await test_aria2_websocket()
    
    print("\n" + "=" * 30)
    print("RESULTS:")
    print(f"RPC Connection: {'PASS' if rpc_result else 'FAIL'}")
    print(f"WebSocket Connection: {'PASS' if ws_result else 'FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
