# Comprehensive dependency installer for AutoUploadBot
import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"\n{'='*50}")
    print(f"STEP: {description}")
    print(f"{'='*50}")
    print(f"Running: {command}")
    
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            check=True
        )
        print("✅ SUCCESS!")
        if result.stdout:
            print("Output:", result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print("❌ FAILED!")
        print("Error:", e.stderr)
        return False
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        return False

def install_requirements():
    """Install requirements with fallback options"""
    print("AutoUploadBot Dependency Installer")
    print("=" * 40)
    
    # Try main requirements first
    print("\n🔄 Attempting to install main requirements...")
    success = run_command(
        "pip install -r requirements.txt",
        "Installing main requirements"
    )
    
    if success:
        print("\n🎉 All dependencies installed successfully!")
        return True
    
    # Fallback to minimal requirements
    print("\n⚠️ Main requirements failed, trying minimal requirements...")
    success = run_command(
        "pip install -r requirements-minimal.txt",
        "Installing minimal requirements"
    )
    
    if success:
        print("\n✅ Minimal dependencies installed successfully!")
        
        # Install MANDATORY core features individually
        mandatory_deps = [
            "aioaria2",
            "py7zr",
            "rarfile"
        ]

        print("\n🚨 Installing MANDATORY core features...")
        failed_mandatory = []

        for dep in mandatory_deps:
            print(f"\n🔄 Installing MANDATORY dependency: {dep}")
            if not run_command(f"pip install {dep}", f"Installing {dep}"):
                failed_mandatory.append(dep)

        if failed_mandatory:
            print(f"\n❌ CRITICAL ERROR: Mandatory dependencies failed: {failed_mandatory}")
            print("These are CORE FEATURES and must work!")
            return False
        
        return True
    
    # Final fallback - install core dependencies one by one
    print("\n⚠️ Minimal requirements failed, installing core dependencies individually...")
    
    core_deps = [
        "fastapi",
        "uvicorn[standard]",
        "aiofiles",
        "aiohttp",
        "google-api-python-client",
        "google-auth-httplib2",
        "google-auth-oauthlib",
        "tenacity",
        "requests",
        "python-dotenv",
        "websockets"
    ]
    
    success_count = 0
    for dep in core_deps:
        print(f"\n🔄 Installing: {dep}")
        if run_command(f"pip install {dep}", f"Installing {dep}"):
            success_count += 1
    
    if success_count >= len(core_deps) - 2:  # Allow 2 failures
        print(f"\n✅ Core dependencies installed ({success_count}/{len(core_deps)})!")
        return True
    else:
        print(f"\n❌ Too many failures ({len(core_deps) - success_count} failed)!")
        return False

def verify_installation():
    """Verify that ALL MANDATORY packages are installed"""
    print("\n" + "="*50)
    print("VERIFYING MANDATORY CORE FEATURES")
    print("="*50)

    # MANDATORY packages - ALL must work
    mandatory_packages = [
        ("fastapi", "FastAPI web framework"),
        ("uvicorn", "ASGI server"),
        ("aiofiles", "Async file operations"),
        ("google.oauth2", "Google Drive integration"),
        ("tenacity", "Retry logic"),
        ("requests", "HTTP requests"),
        ("py7zr", "7-Zip archive extraction"),
        ("rarfile", "RAR archive extraction"),
        ("aioaria2", "Aria2 WebSocket client")
    ]

    failed_imports = []

    for package, description in mandatory_packages:
        try:
            __import__(package)
            print(f"✅ {package} - {description}")
        except ImportError:
            print(f"❌ {package} - {description} - FAILED!")
            failed_imports.append((package, description))

    if not failed_imports:
        print("\n🎉 ALL MANDATORY CORE FEATURES VERIFIED!")
        return True
    else:
        print(f"\n🚨 CRITICAL ERROR: Mandatory features failed:")
        for package, description in failed_imports:
            print(f"   ❌ {package} - {description}")
        print("\nThese are CORE FEATURES and must work for the project to function!")
        return False

def main():
    """Main installation function"""
    print("🚀 AutoUploadBot Dependency Installer")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major != 3 or python_version.minor < 8:
        print("❌ Python 3.8+ required!")
        sys.exit(1)
    
    # Install dependencies
    if not install_requirements():
        print("\n❌ INSTALLATION FAILED!")
        print("Please check the errors above and try manual installation.")
        sys.exit(1)
    
    # Verify installation
    if not verify_installation():
        print("\n⚠️ VERIFICATION FAILED!")
        print("Some packages may not work correctly.")
        response = input("Continue anyway? (y/n): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    # Test archive extraction - MANDATORY CORE FEATURE
    print("\n" + "="*50)
    print("🧪 TESTING ARCHIVE EXTRACTION CORE FEATURE")
    print("="*50)

    if run_command("python test_archive_simple.py", "Testing archive extraction"):
        print("✅ Archive extraction CORE FEATURE verified!")
    else:
        print("❌ CRITICAL: Archive extraction CORE FEATURE failed!")
        print("This is a mandatory feature and must work!")
        response = input("Continue anyway? (NOT RECOMMENDED) (y/n): ")
        if response.lower() != 'y':
            sys.exit(1)

    print("\n" + "="*50)
    print("🎉 INSTALLATION COMPLETED SUCCESSFULLY!")
    print("="*50)
    print("\n✅ ALL CORE FEATURES VERIFIED:")
    print("   - Archive extraction (ZIP, 7Z, RAR, TAR)")
    print("   - Google Drive integration")
    print("   - Aria2 WebSocket client")
    print("   - Video hosting APIs")
    print("\nNext steps:")
    print("1. Run: python setup_aria2.py")
    print("2. Run: python setup_rclone.py")
    print("3. Place your service account JSON in accounts/ directory")
    print("4. Start the application!")

if __name__ == "__main__":
    main()
