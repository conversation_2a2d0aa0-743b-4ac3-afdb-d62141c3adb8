#!/usr/bin/env python3
"""
Test the fixed backend with ProactorEventLoop
"""
import requests
import json

BASE_URL = "http://localhost:8004"

def test_url_download():
    """Test URL download with the fixed backend"""
    print("Testing URL download with ProactorEventLoop fix...")
    
    url = f"{BASE_URL}/api/download/url"
    payload = {
        "urls": ["https://httpbin.org/json"]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("SUCCESS: URL download started successfully!")
            return True
        else:
            print("FAIL: URL download failed")
            return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_magnet_download():
    """Test magnet download with the fixed backend"""
    print("\nTesting magnet download with ProactorEventLoop fix...")
    
    url = f"{BASE_URL}/api/download/magnet"
    payload = {
        "magnets": ["magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=test"]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("SUCCESS: Magnet download started successfully!")
            return True
        else:
            print("FAIL: Magnet download failed")
            return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_download_status():
    """Test download status"""
    print("\nTesting download status...")
    
    url = f"{BASE_URL}/api/downloads/status"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Active Downloads: {len(data.get('downloads', []))}")
            print("SUCCESS: Download status working!")
            return True
        else:
            print("FAIL: Download status failed")
            return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    print("Testing Fixed AutoUploadBot Backend")
    print("=" * 40)
    
    # Test all endpoints
    results = []
    results.append(test_download_status())
    results.append(test_url_download())
    results.append(test_magnet_download())
    
    print("\n" + "=" * 40)
    print("FINAL RESULTS:")
    print(f"Download Status: {'PASS' if results[0] else 'FAIL'}")
    print(f"URL Download: {'PASS' if results[1] else 'FAIL'}")
    print(f"Magnet Download: {'PASS' if results[2] else 'FAIL'}")
    
    if all(results):
        print("\nSUCCESS: All tests passed! ProactorEventLoop fix is working!")
    else:
        print(f"\nWARNING: {len([r for r in results if not r])} tests failed.")
