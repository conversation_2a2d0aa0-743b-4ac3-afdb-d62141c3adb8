#!/usr/bin/env python3
"""
Test script for Remote URL and Magnet Link download fixes
"""
import asyncio
import logging
from core.rclone_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TorrentToHttpService
from config import Config

# Setup logging
logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)

async def test_torrent_service():
    """Test torrent magnet-to-URL conversion"""
    print("\n" + "="*50)
    print("TESTING TORRENT MAGNET CONVERSION")
    print("="*50)

    torrent_service = TorrentToHttpService()
    
    # Test magnet link
    test_magnet = "magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=Americas.Team.The.Gambler.and.His.Cowboys.S01E01.1080p.AV1.10bit-MeGusta%20%5Bext.to%5D&tr=udp%3A%2F%2Ftracker.coppersurfer.tk%3A6969%2Fannounce"
    
    print(f"Testing magnet: {test_magnet[:80]}...")
    
    try:
        result = await torrent_service.convert_magnet_to_urls(test_magnet)
        
        if result["success"]:
            print("SUCCESS: Conversion successful!")
            print(f"   Info Hash: {result['info_hash']}")
            print(f"   Name: {result['name']}")
            print(f"   Files: {result['total_files']}")

            for i, file_info in enumerate(result["files"][:3]):  # Show first 3 files
                print(f"   File {i+1}: {file_info['name']} ({file_info['size']} bytes)")
                print(f"           URL: {file_info['download_url']}")
        else:
            print(f"FAILED: Conversion failed: {result['error']}")

    except Exception as e:
        print(f"ERROR: Exception during conversion: {e}")

async def test_rclone_config():
    """Test RClone configuration"""
    print("\n" + "="*50)
    print("TESTING RCLONE CONFIGURATION")
    print("="*50)
    
    rclone_manager = RcloneManager()
    
    print(f"RClone path: {rclone_manager.rclone_path}")
    print(f"Primary folder ID: {Config.GDRIVE_PRIMARY_FOLDER_ID}")
    
    # Test a simple HTTP URL (small file)
    test_url = "https://httpbin.org/json"
    
    print(f"Testing URL download: {test_url}")
    
    try:
        gid = await rclone_manager.add_url_download(
            url=test_url,
            gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID,
            filename="test_httpbin.json"
        )
        
        if gid:
            print(f"✅ Download started with GID: {gid}")
            
            # Monitor for a few seconds
            for i in range(10):
                await asyncio.sleep(2)
                status = await rclone_manager.get_download_status(gid)
                if status:
                    print(f"   Status: {status['status']} - Progress: {status.get('progress', 0)}%")
                    if status['status'] in ['complete', 'error']:
                        if status['status'] == 'complete':
                            print("✅ Download completed successfully!")
                        else:
                            print(f"❌ Download failed: {status.get('error', 'Unknown error')}")
                        break
                else:
                    print("   No status available")
        else:
            print("❌ Failed to start download")
            
    except Exception as e:
        print(f"❌ Exception during download: {e}")

async def test_magnet_download():
    """Test magnet download with Webtor conversion"""
    print("\n" + "="*50)
    print("TESTING MAGNET DOWNLOAD")
    print("="*50)
    
    rclone_manager = RcloneManager()
    
    # Test magnet link
    test_magnet = "magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=Americas.Team.The.Gambler.and.His.Cowboys.S01E01.1080p.AV1.10bit-MeGusta%20%5Bext.to%5D&tr=udp%3A%2F%2Ftracker.coppersurfer.tk%3A6969%2Fannounce"
    
    print(f"Testing magnet: {test_magnet[:80]}...")
    
    try:
        gid = await rclone_manager.add_magnet_download(
            magnet_url=test_magnet,
            gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID
        )
        
        if gid:
            print(f"✅ Magnet download started with GID: {gid}")
            
            # Monitor for conversion and download
            for i in range(30):  # Monitor for up to 60 seconds
                await asyncio.sleep(2)
                status = await rclone_manager.get_download_status(gid)
                if status:
                    stage = status.get('stage', 'Processing...')
                    print(f"   Status: {status['status']} - {stage}")
                    if status['status'] in ['complete', 'error']:
                        if status['status'] == 'complete':
                            print("✅ Magnet download completed successfully!")
                        else:
                            print(f"❌ Magnet download failed: {status.get('error', 'Unknown error')}")
                        break
                else:
                    print("   No status available")
        else:
            print("❌ Failed to start magnet download")
            
    except Exception as e:
        print(f"❌ Exception during magnet download: {e}")

async def main():
    """Run all tests"""
    print("AutoUploadBot Download Fixes Test Suite")
    print("="*50)
    
    # Test torrent service
    await test_torrent_service()
    
    # Test RClone configuration
    await test_rclone_config()
    
    # Test magnet download (only if RClone works)
    # await test_magnet_download()
    
    print("\n" + "="*50)
    print("TEST SUITE COMPLETED")
    print("="*50)

if __name__ == "__main__":
    asyncio.run(main())
