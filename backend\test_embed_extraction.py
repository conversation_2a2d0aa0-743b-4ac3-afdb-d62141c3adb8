#!/usr/bin/env python3
"""
Test script for embed code extraction functionality
This script tests the VideoHostManager's ability to extract real embed codes
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager
from config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_embed_extraction():
    """Test the embed code extraction functionality"""
    print("🧪 Testing Embed Code Extraction")
    print("=" * 50)
    
    # Initialize VideoHostManager
    video_manager = VideoHostManager()
    await video_manager.initialize()
    
    try:
        # Test with different time ranges
        test_hours = [48, 24, 168]  # 48h, 24h, 1 week
        
        for hours in test_hours:
            print(f"\n📅 Testing embed extraction for last {hours} hours...")
            
            try:
                embed_data = await video_manager.extract_embed_codes(hours)
                
                print(f"✅ Found {len(embed_data)} files with embed codes")
                
                # Display results
                for i, file_data in enumerate(embed_data):
                    filename = file_data.get('filename', 'Unknown')
                    embed_codes = file_data.get('embed_codes', {})
                    
                    print(f"\n📄 File {i+1}: {filename}")
                    
                    for host, embed_code in embed_codes.items():
                        if embed_code:
                            # Truncate long embed codes for display
                            display_code = embed_code[:80] + "..." if len(embed_code) > 80 else embed_code
                            print(f"  🎬 {host}: {display_code}")
                        else:
                            print(f"  ❌ {host}: No embed code")
                    
                    # Only show first 3 files to avoid spam
                    if i >= 2:
                        if len(embed_data) > 3:
                            print(f"  ... and {len(embed_data) - 3} more files")
                        break
                        
            except Exception as e:
                print(f"❌ Error testing {hours}h extraction: {e}")
                
    except Exception as e:
        print(f"❌ Failed to initialize video manager: {e}")
        
    finally:
        await video_manager.close()
        print("\n🏁 Test completed")

async def test_individual_hosts():
    """Test individual host file listing"""
    print("\n🔍 Testing Individual Host File Listing")
    print("=" * 50)
    
    video_manager = VideoHostManager()
    await video_manager.initialize()
    
    try:
        hosts = ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']
        
        for host in hosts:
            if not video_manager.api_keys.get(host):
                print(f"⚠️  Skipping {host} - No API key configured")
                continue
                
            print(f"\n🌐 Testing {host}...")
            
            try:
                files = await video_manager.list_recent_uploads(host, 48)
                print(f"✅ {host}: Found {len(files)} files")
                
                # Show sample files
                for i, file_info in enumerate(files[:3]):
                    filename = file_info.get('filename', 'Unknown')
                    file_code = file_info.get('file_code', 'N/A')
                    print(f"  📁 {filename} (Code: {file_code})")
                    
                if len(files) > 3:
                    print(f"  ... and {len(files) - 3} more files")
                    
            except Exception as e:
                print(f"❌ {host} failed: {e}")
                
    finally:
        await video_manager.close()

async def test_embed_code_generation():
    """Test embed code generation with sample data"""
    print("\n🎨 Testing Embed Code Generation")
    print("=" * 50)
    
    video_manager = VideoHostManager()
    
    # Test data matching your samples
    test_data = [
        ("streamp2p", "8wwqu", "sample_video.mp4"),
        ("rpmshare", "6syts", "sample_video.mp4"), 
        ("filemoon", "6gsmq8ziegir", "28_Years_Later_2025_1080p_AMZN_WEB-DL_DDP5_1_H_264-Vegamovies_is"),
        ("upnshare", "ipcmgm", "sample_video.mp4")
    ]
    
    print("Generated embed codes:")
    for host, file_code, filename in test_data:
        embed_code = video_manager._generate_embed_code(host, file_code, filename)
        print(f"\n🎬 {host}:")
        print(f"  {embed_code}")

async def main():
    """Main test function"""
    print("🚀 Starting AutoUploadBot Embed Code Tests")
    print("=" * 60)
    
    # Check configuration
    missing_keys = []
    for host in ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']:
        if not getattr(Config, f'{host.upper()}_API_KEY', ''):
            missing_keys.append(host)
    
    if missing_keys:
        print(f"⚠️  Warning: Missing API keys for: {', '.join(missing_keys)}")
        print("   Some tests may fail or be skipped")
    
    # Run tests
    await test_embed_code_generation()
    await test_individual_hosts()
    await test_embed_extraction()
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    asyncio.run(main())