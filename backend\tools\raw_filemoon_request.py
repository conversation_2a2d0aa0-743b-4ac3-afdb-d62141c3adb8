import sys, requests
sys.path.insert(0, r'g:/My Websites/Catalogue-Website/automatic-upload-bot/automatic-upload-bot')
from backend.config import Config

fm = Config.FILEMOON_API_URL.rstrip('/')
if fm.endswith('/api'):
    fm = fm[:-4]
url = f"{fm}/api/file/list"
params = {'key': Config.FILEMOON_API_KEY, 'per_page':5, 'page':1}
print('Requesting', url, 'with params', params)
resp = requests.get(url, params=params, timeout=15)
print('status', resp.status_code)
try:
    j = resp.json()
    import json
    print(json.dumps(j, indent=2))
except Exception as e:
    print('non-json response', resp.text)
