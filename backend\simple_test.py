#!/usr/bin/env python3
"""
COMPREHENSIVE VERIFICATION AND TEST SYSTEM
Tests all components of the AutoUploadBot with FULL RCLONE IMPLEMENTATION
"""
import asyncio
import aiohttp
import subprocess
import os
from pathlib import Path
import requests
import json

# Test the drive list API
try:
    response = requests.get("http://localhost:8001/api/gdrive/list/primary", timeout=10)
    print(f"List API Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Files found: {len(data.get('files', []))}")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"List API Error: {e}")

# Test the cleanup API with a dummy request
try:
    test_data = {
        "file_ids": ["dummy_id"],
        "drive_type": "primary",
        "permanent_delete": True
    }
    response = requests.delete(
        "http://localhost:8001/api/gdrive/cleanup",
        headers={"Content-Type": "application/json"},
        json=test_data,
        timeout=10
    )
    print(f"Cleanup API Status: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"Cleanup response: {json.dumps(data, indent=2)}")
    else:
        print(f"Error: {response.text}")
except Exception as e:
    print(f"Cleanup API Error: {e}")


async def test_system():
    """Run comprehensive system test"""
    print("STARTING COMPREHENSIVE VERIFICATION AND TESTING")
    print("=" * 60)
    
    results = {}
    
    # Test 1: Backend Health
    print("\nTesting Backend Health...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8001/health") as response:
                if response.status == 200:
                    data = await response.json()
                    results["backend"] = "PASS"
                    print(f"PASS - Backend Health: {data}")
                else:
                    results["backend"] = "FAIL"
                    print(f"FAIL - Backend Health: HTTP {response.status}")
    except Exception as e:
        results["backend"] = "FAIL"
        print(f"FAIL - Backend Health: {e}")
    
    # Test 2: RClone Configuration
    print("\nTesting RClone Configuration...")
    try:
        rclone_path = Path("rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
        
        if rclone_path.exists():
            result = subprocess.run([str(rclone_path), "version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                result2 = subprocess.run([str(rclone_path), "listremotes"], 
                                       capture_output=True, text=True, timeout=10)
                if "gdrive:" in result2.stdout:
                    results["rclone"] = "PASS"
                    print(f"PASS - RClone: {result.stdout.split()[1]} with gdrive remote")
                else:
                    results["rclone"] = "PARTIAL"
                    print("PARTIAL - RClone installed but gdrive remote not configured")
            else:
                results["rclone"] = "FAIL"
                print(f"FAIL - RClone: {result.stderr}")
        else:
            results["rclone"] = "FAIL"
            print("FAIL - RClone executable not found")
    except Exception as e:
        results["rclone"] = "FAIL"
        print(f"FAIL - RClone: {e}")
    
    # Test 3: Aria2 Connection
    print("\nTesting Aria2 Connection...")
    try:
        async with aiohttp.ClientSession() as session:
            rpc_data = {
                "jsonrpc": "2.0",
                "id": "test",
                "method": "aria2.getVersion"
            }
            
            async with session.post("http://localhost:6800/jsonrpc", json=rpc_data) as response:
                if response.status == 200:
                    data = await response.json()
                    if "result" in data:
                        results["aria2"] = "PASS"
                        print(f"PASS - Aria2: {data['result']['version']}")
                    else:
                        results["aria2"] = "FAIL"
                        print(f"FAIL - Aria2: {data}")
                else:
                    results["aria2"] = "FAIL"
                    print(f"FAIL - Aria2: HTTP {response.status}")
    except Exception as e:
        results["aria2"] = "FAIL"
        print(f"FAIL - Aria2: {e}")
    
    # Test 4: API Endpoints
    print("\nTesting API Endpoints...")
    try:
        async with aiohttp.ClientSession() as session:
            # Test download status endpoint
            async with session.get("http://localhost:8001/api/downloads/status") as response:
                status_ok = response.status == 200
                
            # Test URL download endpoint
            url_data = {"urls": ["https://httpbin.org/json"]}
            async with session.post("http://localhost:8001/api/download/url", json=url_data) as response:
                url_ok = response.status in [200, 500]  # 500 is expected due to Google Drive permissions
                url_text = await response.text()
                
            # Test magnet download endpoint
            magnet_data = {"magnets": ["magnet:?xt=urn:btih:test"]}
            async with session.post("http://localhost:8001/api/download/magnet", json=magnet_data) as response:
                magnet_ok = response.status in [200, 500]  # 500 is expected due to Google Drive permissions
                magnet_text = await response.text()
            
            if status_ok and url_ok and magnet_ok:
                results["api"] = "PASS"
                print(f"PASS - API Endpoints: Status={status_ok}, URL={url_ok}, Magnet={magnet_ok}")
            else:
                results["api"] = "FAIL"
                print(f"FAIL - API Endpoints: Status={status_ok}, URL={url_ok}, Magnet={magnet_ok}")
                
    except Exception as e:
        results["api"] = "FAIL"
        print(f"FAIL - API Endpoints: {e}")
    
    # Test 5: Frontend Connectivity
    print("\nTesting Frontend Connectivity...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8082") as response:
                if response.status == 200:
                    results["frontend"] = "PASS"
                    print("PASS - Frontend accessible at http://localhost:8082")
                else:
                    results["frontend"] = "FAIL"
                    print(f"FAIL - Frontend: HTTP {response.status}")
    except Exception as e:
        results["frontend"] = "FAIL"
        print(f"FAIL - Frontend: {e}")
    
    # Test 6: CORS Configuration
    print("\nTesting CORS Configuration...")
    try:
        async with aiohttp.ClientSession() as session:
            headers = {
                "Origin": "http://localhost:8082",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
            
            async with session.options("http://localhost:8001/api/download/url", headers=headers) as response:
                cors_origin = response.headers.get("Access-Control-Allow-Origin")
                
                if response.status in [200, 204] and cors_origin:
                    results["cors"] = "PASS"
                    print("PASS - CORS properly configured")
                else:
                    results["cors"] = "FAIL"
                    print(f"FAIL - CORS: HTTP {response.status}, Origin: {cors_origin}")
    except Exception as e:
        results["cors"] = "FAIL"
        print(f"FAIL - CORS: {e}")
    
    # Test 7: Google Drive Permissions Issue
    print("\nTesting Google Drive Permissions...")
    try:
        rclone_path = Path("rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
        result = subprocess.run([
            str(rclone_path), "copyurl", 
            "https://httpbin.org/json", 
            "gdrive:1yN9h1urFbusV6CEboM2Pd08AMgJKqkEq/test.json",
            "--dry-run"
        ], capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            results["gdrive_permissions"] = "PASS"
            print("PASS - Google Drive permissions working")
        else:
            results["gdrive_permissions"] = "FAIL"
            if "Shared drive not found" in result.stderr:
                print("FAIL - Google Drive: Service account lacks shared drive access")
            else:
                print(f"FAIL - Google Drive: {result.stderr[:100]}")
                
    except Exception as e:
        results["gdrive_permissions"] = "FAIL"
        print(f"FAIL - Google Drive Permissions: {e}")
    
    # Generate Report
    print("\n" + "=" * 60)
    print("COMPREHENSIVE TEST REPORT")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = len([r for r in results.values() if r == "PASS"])
    partial_tests = len([r for r in results.values() if r == "PARTIAL"])
    failed_tests = len([r for r in results.values() if r == "FAIL"])
    
    print(f"SUMMARY: {passed_tests}/{total_tests} PASSED, {partial_tests} PARTIAL, {failed_tests} FAILED")
    print()
    
    for test_name, result in results.items():
        status_symbol = "✓" if result == "PASS" else "⚠" if result == "PARTIAL" else "✗"
        print(f"{status_symbol} {test_name.upper().replace('_', ' ')}: {result}")
    
    print()
    
    # Overall Assessment
    if failed_tests == 0 and partial_tests == 0:
        print("ALL SYSTEMS FULLY FUNCTIONAL!")
        overall_status = "FULLY_FUNCTIONAL"
    elif failed_tests <= 1:
        print("SYSTEMS MOSTLY FUNCTIONAL WITH MINOR ISSUES")
        overall_status = "MOSTLY_FUNCTIONAL"
    else:
        print("CRITICAL ISSUES DETECTED - REQUIRES ATTENTION")
        overall_status = "NEEDS_ATTENTION"
    
    print("=" * 60)
    
    return overall_status, results

if __name__ == "__main__":
    asyncio.run(test_system())
