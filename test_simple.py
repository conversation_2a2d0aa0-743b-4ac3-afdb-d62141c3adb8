#!/usr/bin/env python3
"""
Simple test without Unicode characters
"""
import asyncio
import platform
from pathlib import Path

async def test_rclone():
    """Test rclone command"""
    print("Testing rclone command...")
    
    rclone_path = Path("backend/rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
    
    if not rclone_path.exists():
        print(f"FAIL: RClone not found at: {rclone_path}")
        return False
    
    try:
        # Test rclone version command
        process = await asyncio.create_subprocess_exec(
            str(rclone_path), "version",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=str(rclone_path.parent)
        )
        
        stdout, stderr = await process.communicate()
        print(f"Return code: {process.returncode}")
        
        if process.returncode == 0:
            print("PASS: RClone command works")
            return True
        else:
            print("FAIL: RClone command failed")
            print(f"Stderr: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"FAIL: RClone test error: {e}")
        return False

async def main():
    """Run test"""
    print("Testing RClone with ProactorEventLoop")
    print("=" * 40)
    
    # Set ProactorEventLoop for Windows
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("Set Windows ProactorEventLoop policy")
    
    print(f"Event Loop: {type(asyncio.get_event_loop())}")
    
    # Run test
    result = await test_rclone()
    
    print("=" * 40)
    print(f"RClone Test: {'PASS' if result else 'FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
