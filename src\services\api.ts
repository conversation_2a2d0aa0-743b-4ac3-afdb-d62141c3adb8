// API service for backend communication
// Updated to use ProactorEventLoop-fixed backend on port 8001
const API_BASE_URL = 'http://localhost:8001';

export interface DownloadRequest {
  urls?: string[];
  magnets?: string[];
}

export interface UploadRequest {
  file_path: string;
  drive_type: 'primary' | 'secondary';
}

export interface VideoHostUploadRequest {
  file_url: string;
  filename: string;
}

export interface ArchiveExtractionRequest {
  file_id: string;
  filename: string;
  source_drive: 'primary' | 'secondary';
  target_drive: 'primary' | 'secondary';
}

export interface DownloadStatus {
  gid: string;
  status: string;
  progress: number;
  completed_length: number;
  total_length: number;
  download_speed: number;
  eta: number;
  filename: string;
}

export interface GDriveFile {
  id: string;
  name: string;
  size: string;
  mimeType: string;
  createdTime: string;
  isTrash?: boolean; // Optional property for trash files
}

export interface EmbedData {
  filename: string;
  upload_date: string;
  embed_codes: {
    streamp2p: string;
    rpmshare: string;
    upnshare: string;
    filemoon: string;
  };
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_BASE_URL;
  }

  // Health check
  async healthCheck(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/health`);
    return response.json();
  }

  // Download methods
  async downloadFromUrls(urls: string[]): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/download/url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ urls }),
    });
    return response.json();
  }

  async downloadFromMagnets(magnets: string[]): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/download/magnet`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ magnets }),
    });
    return response.json();
  }

  async getDownloadsStatus(): Promise<{ downloads: DownloadStatus[] }> {
    const response = await fetch(`${this.baseUrl}/api/downloads/status`);
    return response.json();
  }

  // Upload methods
  async uploadToGDrive(filePath: string, driveType: 'primary' | 'secondary'): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/upload/gdrive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_path: filePath,
        drive_type: driveType,
      }),
    });
    return response.json();
  }

  async uploadToVideoHosts(fileUrl: string, filename: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/upload/video-hosts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_url: fileUrl,
        filename: filename,
      }),
    });
    return response.json();
  }

  async manualUploadToHosts(fileUrls: string[], filenames: string[]): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/auto-upload/manual-upload`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_urls: fileUrls,
        filenames: filenames,
      }),
    });
    return response.json();
  }

  // Google Drive management
  async listGDriveFiles(driveType: 'primary' | 'secondary'): Promise<{ files: GDriveFile[] }> {
    const response = await fetch(`${this.baseUrl}/api/gdrive/list/${driveType}`);
    return response.json();
  }

  async deleteGDriveFiles(fileIds: string[]): Promise<{ results: Array<{ file_id: string; deleted: boolean }> }> {
    const response = await fetch(`${this.baseUrl}/api/gdrive/delete`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fileIds),
    });
    return response.json();
  }

  async cleanupGDrive(fileIds: string[], driveType: 'primary' | 'secondary' | 'both', permanentDelete: boolean = true): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/gdrive/cleanup`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_ids: fileIds,
        drive_type: driveType,
        permanent_delete: permanentDelete,
      }),
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  // Duplicate checking
  async checkDuplicates(hours: number = 24): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/duplicates/check`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ hours }),
    });
    return response.json();
  }

  // Embed code extraction
  async extractEmbedCodes(hours: number = 48): Promise<{ embed_data: EmbedData[] }> {
    const response = await fetch(`${this.baseUrl}/api/embed/extract`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ hours }),
    });
    return response.json();
  }

  async downloadEmbedCsv(hours: number = 48): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/api/embed/download-csv?hours=${hours}`);
    return response.blob();
  }

  // Queue management
  async clearQueue(type: 'all' | 'downloads' | 'uploads'): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/queue/clear`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type }),
    });
    return response.json();
  }

  async cancelTask(taskId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/task/cancel`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ task_id: taskId }),
    });
    return response.json();
  }

  // Statistics
  async getStats(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/stats`);
    return response.json();
  }

  // WebSocket connection for real-time updates
  connectWebSocket(onMessage: (data: any) => void): WebSocket {
    const ws = new WebSocket(`ws://localhost:8006/ws`);
    
    ws.onopen = () => {
      console.log('WebSocket connected');
    };
    
    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      onMessage(data);
    };
    
    ws.onclose = () => {
      console.log('WebSocket disconnected');
    };
    
    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
    
    return ws;
  }

  // Archive extraction methods
  async extractArchive(request: ArchiveExtractionRequest): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/extract/archive`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getExtractionStatus(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/extract/status`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getExtractionJobStatus(jobId: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/api/extract/job/${jobId}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // Audio notifications
  playCompletionSound(): void {
    if ('speechSynthesis' in window) {
      // Use a simple beep sound
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.play().catch(() => {
        // Fallback to system beep
        console.log('🔔 Task completed!');
      });
    }
  }

  playErrorSound(): void {
    if ('speechSynthesis' in window) {
      // Use a different tone for errors
      const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
      audio.play().catch(() => {
        // Fallback to system beep
        console.log('❌ Task failed!');
      });
    }
  }
}

export const apiService = new ApiService();
