#!/usr/bin/env python3
import asyncio
import aiohttp
import json

async def test_api_error():
    """Test the API to see the exact error"""
    
    # Test URL download
    print("Testing URL download API...")
    async with aiohttp.ClientSession() as session:
        try:
            url_data = {
                "urls": ["https://httpbin.org/json"]
            }
            
            async with session.post(
                "http://localhost:8001/api/download/url",
                json=url_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"URL Response Status: {response.status}")
                text = await response.text()
                print(f"URL Response: {text}")
                
        except Exception as e:
            print(f"URL Error: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # Test magnet download
    print("Testing magnet download API...")
    async with aiohttp.ClientSession() as session:
        try:
            magnet_data = {
                "magnets": ["magnet:?xt=urn:btih:test"]
            }
            
            async with session.post(
                "http://localhost:8001/api/download/magnet",
                json=magnet_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                print(f"Magnet Response Status: {response.status}")
                text = await response.text()
                print(f"Magnet Response: {text}")
                
        except Exception as e:
            print(f"Magnet Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_api_error())
