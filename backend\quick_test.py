#!/usr/bin/env python3
"""Quick test of ALL Time filter"""

import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))
from api.video_hosts import VideoHostManager

async def quick_test():
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test ALL Time
        embed_data = await vm.extract_embed_codes(999999)
        count = len(embed_data)
        
        print(f"ALL Time filter result: {count} files")
        print(f"Expected: 107-110 files")
        
        if 107 <= count <= 110:
            print("✅ WITHIN EXPECTED RANGE")
        else:
            print(f"❌ OUTSIDE RANGE (off by {abs(count - 108)})")
        
        return count
        
    finally:
        await vm.close()

if __name__ == "__main__":
    asyncio.run(quick_test())