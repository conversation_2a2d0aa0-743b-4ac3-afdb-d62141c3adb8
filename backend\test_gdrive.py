#!/usr/bin/env python3
import asyncio
from core.gdrive_direct_manager import GoogleDriveDirectManager

async def test_gdrive():
    try:
        manager = GoogleDriveDirectManager()
        print("Testing Google Drive access...")
        
        # Test primary folder
        files = await manager.list_files_in_folder('1yN9h1urFbusV6CEboM2Pd08AMgJKqkEq')
        print(f"Primary folder - Found {len(files)} files")
        
        # Test secondary folder  
        files2 = await manager.list_files_in_folder('1DRTg4Om0LwFGlFaxOGX4izPWBdwlxKGJ')
        print(f"Secondary folder - Found {len(files2)} files")
        
        print("Google Drive access test completed successfully!")
        return True
        
    except Exception as e:
        print(f"Google Drive access test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_gdrive())
