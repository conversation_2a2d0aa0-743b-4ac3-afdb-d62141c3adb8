import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Trash2, Alert<PERSON>riangle, RefreshCw, HardDrive, Download } from "lucide-react";
import { apiService, GDriveFile } from "@/services/api";
import { useToast } from "@/hooks/use-toast";

const DriveCleanup = () => {
  const [primaryFiles, setPrimaryFiles] = useState<GDriveFile[]>([]);
  const [secondaryFiles, setSecondaryFiles] = useState<GDriveFile[]>([]);
  const [selectedPrimary, setSelectedPrimary] = useState<string[]>([]);
  const [selectedSecondary, setSelectedSecondary] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [includeTrash, setIncludeTrash] = useState(false);
  const [permanentDelete, setPermanentDelete] = useState(true); // Default to permanent delete
  const [stats, setStats] = useState({
    primaryFiles: 0,
    secondaryFiles: 0,
    totalSize: "0 B"
  });
  const { toast } = useToast();

  // Load files on component mount
  useEffect(() => {
    loadFiles();
  }, []);

  const loadFiles = async () => {
    setIsLoading(true);
    try {
      // Load files from both drives (including trash if requested)
      const [primaryResponse, secondaryResponse] = await Promise.all([
        fetch(`http://localhost:8001/api/gdrive/list/primary?include_trash=${includeTrash}`).then(r => r.json()),
        fetch(`http://localhost:8001/api/gdrive/list/secondary?include_trash=${includeTrash}`).then(r => r.json())
      ]);

      setPrimaryFiles(primaryResponse.files || []);
      setSecondaryFiles(secondaryResponse.files || []);

      // Calculate stats
      const totalFiles = (primaryResponse.files?.length || 0) + (secondaryResponse.files?.length || 0);
      const totalSizeBytes = [
        ...(primaryResponse.files || []),
        ...(secondaryResponse.files || [])
      ].reduce((sum, file) => sum + (parseInt(file.size) || 0), 0);

      setStats({
        primaryFiles: primaryResponse.files?.length || 0,
        secondaryFiles: secondaryResponse.files?.length || 0,
        totalSize: formatFileSize(totalSizeBytes)
      });

    } catch (error) {
      console.error("Failed to load files:", error);
      toast({
        title: "Error",
        description: "Failed to load drive files",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const handleSelectAll = (driveType: "primary" | "secondary", checked: boolean) => {
    if (driveType === "primary") {
      // Select all files except those that might be in shared folders
      const fileIds = primaryFiles
        .filter(file => !file.name.startsWith("shared_")) // Exclude shared folders if any
        .map(f => f.id);
      setSelectedPrimary(checked ? fileIds : []);
    } else {
      // Select all files except those that might be in shared folders
      const fileIds = secondaryFiles
        .filter(file => !file.name.startsWith("shared_")) // Exclude shared folders if any
        .map(f => f.id);
      setSelectedSecondary(checked ? fileIds : []);
    }
  };

  const handleFileSelect = (fileId: string, driveType: "primary" | "secondary", checked: boolean) => {
    if (driveType === "primary") {
      setSelectedPrimary(prev =>
        checked ? [...prev, fileId] : prev.filter(id => id !== fileId)
      );
    } else {
      setSelectedSecondary(prev =>
        checked ? [...prev, fileId] : prev.filter(id => id !== fileId)
      );
    }
  };

  const handleDeleteSelected = async () => {
    // Combine all selected files from both drives
    const allSelectedFiles = [...selectedPrimary, ...selectedSecondary];

    if (allSelectedFiles.length === 0) {
      toast({
        title: "No Selection",
        description: "Please select files to delete",
        variant: "destructive",
      });
      return;
    }

    setIsDeleting(true);
    try {
      // Delete from both drives
      const response = await apiService.cleanupGDrive(allSelectedFiles, "both", permanentDelete);

      const deletedCount = response.results?.filter(r => r.deleted).length || 0;
      const failedCount = response.results?.filter(r => !r.deleted).length || 0;

      if (deletedCount > 0) {
        toast({
          title: "Success",
          description: `${permanentDelete ? 'Permanently deleted' : 'Moved to trash'} ${deletedCount} files`,
        });
      }

      if (failedCount > 0) {
        toast({
          title: "Partial Failure",
          description: `Failed to delete ${failedCount} files. Check logs for details.`,
          variant: "destructive",
        });
      }

      // Clear selections and reload files
      setSelectedPrimary([]);
      setSelectedSecondary([]);
      await loadFiles();

    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to delete files: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const handleClearSelections = () => {
    setSelectedPrimary([]);
    setSelectedSecondary([]);
    toast({
      title: "Selections Cleared",
      description: "All file selections have been cleared",
    });
  };

  // Function to select all files from both drives at once
  const handleSelectAllFiles = (checked: boolean) => {
    if (checked) {
      // Select all files from both drives, excluding shared folders
      const primaryIds = primaryFiles
        .filter(file => !file.name.startsWith("shared_"))
        .map(f => f.id);
      const secondaryIds = secondaryFiles
        .filter(file => !file.name.startsWith("shared_"))
        .map(f => f.id);
      setSelectedPrimary(primaryIds);
      setSelectedSecondary(secondaryIds);
    } else {
      setSelectedPrimary([]);
      setSelectedSecondary([]);
    }
  };

  // Calculate total selected files
  const totalSelected = selectedPrimary.length + selectedSecondary.length;

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold font-mono mb-2">DRIVE CLEANUP MANAGER</h2>
        <p className="text-muted-foreground font-mono">CLEAN UP GOOGLE DRIVES • BULK DELETE • FREE UP SPACE</p>
      </div>

      {/* Options */}
      <Card className="brutal-card">
        <CardHeader className="bg-secondary text-secondary-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">CLEANUP OPTIONS</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-trash"
                  checked={includeTrash}
                  onCheckedChange={(checked) => {
                    setIncludeTrash(checked as boolean);
                    // Reload files when trash option changes
                    if (checked !== includeTrash) {
                      loadFiles();
                    }
                  }}
                />
                <label htmlFor="include-trash" className="font-mono font-bold text-sm">
                  INCLUDE TRASH FILES
                </label>
              </div>
              <div className="text-xs text-muted-foreground font-mono">
                Scan and display files in Google Drive trash
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="permanent-delete"
                  checked={permanentDelete}
                  onCheckedChange={(checked) => setPermanentDelete(checked as boolean)}
                />
                <label htmlFor="permanent-delete" className="font-mono font-bold text-sm">
                  PERMANENT DELETE
                </label>
              </div>
              <div className="text-xs text-muted-foreground font-mono">
                Permanently delete files instead of moving to trash
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="select-all-files"
                  checked={totalSelected > 0 && totalSelected === (primaryFiles.length + secondaryFiles.length)}
                  onCheckedChange={(checked) => handleSelectAllFiles(checked as boolean)}
                />
                <label htmlFor="select-all-files" className="font-mono font-bold text-sm">
                  SELECT ALL FILES
                </label>
              </div>
              <div className="text-xs text-muted-foreground font-mono">
                Select all files from both drives
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <HardDrive className="w-10 h-10 mx-auto mb-4 text-primary" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.primaryFiles}</div>
            <div className="text-sm font-mono text-muted-foreground">PRIMARY FILES</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <HardDrive className="w-10 h-10 mx-auto mb-4 text-purple" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.secondaryFiles}</div>
            <div className="text-sm font-mono text-muted-foreground">SECONDARY FILES</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Trash2 className="w-10 h-10 mx-auto mb-4 text-destructive" />
            <div className="text-4xl font-bold font-mono mb-2">{totalSelected}</div>
            <div className="text-sm font-mono text-muted-foreground">SELECTED</div>
            <div className="text-xs font-mono">{((totalSelected / Math.max(stats.primaryFiles + stats.secondaryFiles, 1) * 100).toFixed(0))}% selected</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 space-y-2">
            <Button
              onClick={loadFiles}
              disabled={isLoading}
              className="brutal-button w-full"
              variant="outline"
            >
              <RefreshCw className={`w-3 h-3 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              REFRESH
            </Button>
            <Button
              onClick={handleDeleteSelected}
              disabled={isDeleting || totalSelected === 0}
              className="brutal-button w-full"
              variant="destructive"
            >
              <Trash2 className="w-3 h-3 mr-2" />
              DELETE SELECTED ({totalSelected})
            </Button>
            <Button
              onClick={handleClearSelections}
              disabled={totalSelected === 0}
              className="brutal-button w-full"
              variant="outline"
            >
              <RefreshCw className="w-3 h-3 mr-2" />
              CLEAR QUEUE
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Danger Zone Warning */}
      <Card className="brutal-card">
        <CardHeader className="bg-destructive text-destructive-foreground brutal-border-b">
          <CardTitle className="flex items-center gap-2 font-mono text-lg">
            <AlertTriangle className="w-4 h-4" />
            ⚠️ DANGER ZONE ⚠️
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          <div className="bg-destructive/10 p-4 brutal-border">
            <p className="font-mono font-bold text-center">
              {permanentDelete 
                ? "PERMANENTLY DELETING FILES FROM GOOGLE DRIVE IS IRREVERSIBLE!" 
                : "DELETING FILES FROM GOOGLE DRIVE MOVES THEM TO TRASH!"}
            </p>
            <p className="font-mono text-sm text-center mt-2 text-muted-foreground">
              {permanentDelete
                ? "Selected files will be permanently deleted and cannot be recovered!"
                : "Selected files will be moved to Google Drive trash and can be recovered for 30 days"}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Drive Sections */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Primary Google Drive */}
        <Card className="brutal-card">
          <CardHeader className="bg-primary text-primary-foreground brutal-border-b">
            <CardTitle className="font-mono text-lg">PRIMARY GOOGLE DRIVE ({primaryFiles.length} FILES)</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Checkbox
                checked={primaryFiles.length > 0 && selectedPrimary.length === primaryFiles.length}
                onCheckedChange={(checked) => handleSelectAll("primary", checked as boolean)}
              />
              <span className="text-sm">SELECT ALL PRIMARY</span>
              <span className="text-xs ml-auto">Selected: {selectedPrimary.length}/{primaryFiles.length}</span>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {primaryFiles.map((file) => (
                <div key={file.id} className="brutal-card p-3">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedPrimary.includes(file.id)}
                      onCheckedChange={(checked) => handleFileSelect(file.id, "primary", checked as boolean)}
                    />
                    <div className="flex-1">
                      <div className="font-mono font-bold text-sm truncate">{file.name}</div>
                      <div className="text-xs text-muted-foreground font-mono">
                        Size: {formatFileSize(parseInt(file.size) || 0)} • Type: {file.mimeType}
                      </div>
                      <div className="text-xs font-mono mt-1 text-muted-foreground">
                        Created: {new Date(file.createdTime).toLocaleDateString()}
                      </div>
                    </div>
                    {includeTrash && file.isTrash && (
                      <div className="bg-destructive text-destructive-foreground px-2 py-1 text-xs font-bold brutal-border">
                        TRASH
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {primaryFiles.length === 0 && (
                <div className="text-center text-muted-foreground font-mono py-8">
                  No files found in Primary Google Drive
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Secondary Google Drive */}
        <Card className="brutal-card">
          <CardHeader className="bg-purple text-purple-foreground brutal-border-b">
            <CardTitle className="font-mono text-lg">SECONDARY GOOGLE DRIVE ({secondaryFiles.length} FILES)</CardTitle>
            <div className="flex items-center gap-2 mt-2">
              <Checkbox
                checked={secondaryFiles.length > 0 && selectedSecondary.length === secondaryFiles.length}
                onCheckedChange={(checked) => handleSelectAll("secondary", checked as boolean)}
              />
              <span className="text-sm">SELECT ALL SECONDARY</span>
              <span className="text-xs ml-auto">Selected: {selectedSecondary.length}/{secondaryFiles.length}</span>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {secondaryFiles.map((file) => (
                <div key={file.id} className="brutal-card p-3">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedSecondary.includes(file.id)}
                      onCheckedChange={(checked) => handleFileSelect(file.id, "secondary", checked as boolean)}
                    />
                    <div className="flex-1">
                      <div className="font-mono font-bold text-sm truncate">{file.name}</div>
                      <div className="text-xs text-muted-foreground font-mono">
                        Size: {formatFileSize(parseInt(file.size) || 0)} • Type: {file.mimeType}
                      </div>
                      <div className="text-xs font-mono mt-1 text-muted-foreground">
                        Created: {new Date(file.createdTime).toLocaleDateString()}
                      </div>
                    </div>
                    {includeTrash && file.isTrash && (
                      <div className="bg-destructive text-destructive-foreground px-2 py-1 text-xs font-bold brutal-border">
                        TRASH
                      </div>
                    )}
                  </div>
                </div>
              ))}
              {secondaryFiles.length === 0 && (
                <div className="text-center text-muted-foreground font-mono py-8">
                  No files found in Secondary Google Drive
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DriveCleanup;