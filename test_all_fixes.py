#!/usr/bin/env python3
"""
Test all fixes: WebSocket, Magnet conversion, and new Zip Extractor
"""
import requests
import json
import time

BASE_URL = "http://localhost:8005"

def test_websocket_endpoint():
    """Test WebSocket endpoint"""
    print("1. Testing WebSocket Endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/ws-test", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   SUCCESS: WebSocket endpoint available - Connections: {data.get('connections', 0)}")
            return True
        else:
            print(f"   FAIL: WebSocket endpoint error - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: WebSocket endpoint error: {e}")
        return False

def test_magnet_download():
    """Test improved magnet download with multiple services"""
    print("2. Testing Improved Magnet Download...")
    
    url = f"{BASE_URL}/api/download/magnet"
    payload = {
        "magnets": ["magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=test"]
    }
    
    try:
        response = requests.post(url, json=payload, timeout=15)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print(f"   SUCCESS: Magnet download started - GID: {data['results'][0]['gid']}")
                return True

        print(f"   FAIL: Magnet download failed - Response: {response.text}")
        return False
    except Exception as e:
        print(f"   FAIL: Magnet download error: {e}")
        return False

def test_zip_extraction_endpoints():
    """Test new zip extraction endpoints"""
    print("3. Testing Zip Extraction Endpoints...")
    
    # Test extraction status endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/extract/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   SUCCESS: Extraction status endpoint working - Jobs: {len(data.get('jobs', []))}")

            # Test archive extraction endpoint (with dummy data)
            extract_payload = {
                "file_id": "test_file_id",
                "filename": "test_archive.zip",
                "source_drive": "primary",
                "target_drive": "secondary"
            }
            
            extract_response = requests.post(f"{BASE_URL}/api/extract/archive", json=extract_payload, timeout=10)
            if extract_response.status_code == 200:
                extract_data = extract_response.json()
                job_id = extract_data.get("job_id")
                print(f"   SUCCESS: Archive extraction started - Job ID: {job_id}")

                # Test job status endpoint
                time.sleep(1)
                job_response = requests.get(f"{BASE_URL}/api/extract/job/{job_id}", timeout=5)
                if job_response.status_code == 200:
                    job_data = job_response.json()
                    print(f"   SUCCESS: Job status endpoint working - Status: {job_data.get('status')}")
                    return True

            print(f"   FAIL: Archive extraction failed - Response: {extract_response.text}")
            return False
        else:
            print(f"   FAIL: Extraction status error - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: Zip extraction endpoints error: {e}")
        return False

def test_auto_upload_endpoints():
    """Test auto upload endpoints with correct port"""
    print("4. Testing Auto Upload Endpoints...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/auto-upload/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"   SUCCESS: Auto upload status endpoint working")
            return True
        else:
            print(f"   FAIL: Auto upload status error - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: Auto upload endpoints error: {e}")
        return False

def test_basic_api():
    """Test basic API functionality"""
    print("5. Testing Basic API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/downloads/status", timeout=5)
        if response.status_code == 200:
            print("   SUCCESS: Basic API working")
            return True
        else:
            print(f"   FAIL: Basic API error - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: Basic API error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("TESTING ALL FIXES - AutoUploadBot")
    print("=" * 60)
    print("Testing: WebSocket, Magnet Conversion, Zip Extractor, Port Fixes")
    print("=" * 60)
    
    tests = [
        ("WebSocket Endpoint", test_websocket_endpoint),
        ("Improved Magnet Download", test_magnet_download),
        ("Zip Extraction Endpoints", test_zip_extraction_endpoints),
        ("Auto Upload Endpoints", test_auto_upload_endpoints),
        ("Basic API", test_basic_api),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} - Exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"OVERALL: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\nSUCCESS: ALL FIXES WORKING!")
        print("WebSocket port issues fixed!")
        print("Magnet conversion improved!")
        print("Zip Extractor functionality added!")
        print("Auto upload endpoints working!")
        print("\nAutoUploadBot is fully functional!")
    else:
        failed = len(results) - passed
        print(f"\nWARNING: {failed} test(s) failed.")
        print("Check the backend logs for more details.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
