import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Co<PERSON>, AlertTriangle, CheckCircle, RefreshCw, Trash, Calendar } from "lucide-react";
import { apiService } from "@/services/api";
import { useToast } from "@/hooks/use-toast";

const DuplicateChecker = () => {
  const [extractionMode, setExtractionMode] = useState<"hours" | "all">("hours");
  const [timeRange, setTimeRange] = useState<string>("24");
  const [duplicates, setDuplicates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalFiles: 0,
    duplicateGroups: 0,
    uniqueFiles: 0
  });
  const { toast } = useToast();

  const handleScanDuplicates = async () => {
    setIsLoading(true);
    try {
      // Convert time range to hours based on extraction mode
      const hours = extractionMode === "all" ? 999999 : parseInt(timeRange) || 24;
      
      const response = await apiService.checkDuplicates(hours);
      setDuplicates(response.duplicates || []);
      setStats({
        totalFiles: response.duplicates?.length || 0,
        duplicateGroups: response.duplicates?.length || 0,
        uniqueFiles: 0
      });

      toast({
        title: "Scan Complete",
        description: `Found ${response.duplicates?.length || 0} duplicate groups in the last ${hours === 999999 ? 'all time' : hours + ' hours'}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to scan for duplicates",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearResults = () => {
    setDuplicates([]);
    setStats({
      totalFiles: 0,
      duplicateGroups: 0,
      uniqueFiles: 0
    });
    toast({
      title: "Results Cleared",
      description: "All duplicate scan results have been cleared",
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold font-mono mb-2">DUPLICATE FILE CHECKER</h2>
        <p className="text-muted-foreground font-mono">FIND DUPLICATES • CROSS-PLATFORM DETECTION • CLEANUP TOOLS</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Copy className="w-10 h-10 mx-auto mb-4 text-primary" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.totalFiles}</div>
            <div className="text-sm font-mono text-muted-foreground">TOTAL FILES</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-10 h-10 mx-auto mb-4 text-destructive" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.duplicateGroups}</div>
            <div className="text-sm font-mono text-muted-foreground">DUPLICATE GROUPS</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <CheckCircle className="w-10 h-10 mx-auto mb-4 text-accent" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.uniqueFiles}</div>
            <div className="text-sm font-mono text-muted-foreground">UNIQUE FILES</div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6">
            <h3 className="font-mono font-bold mb-4 text-center">ACTIONS</h3>
            <div className="space-y-2">
              <Button
                onClick={handleClearResults}
                disabled={duplicates.length === 0}
                className="brutal-button w-full"
                variant="destructive"
              >
                <Trash className="w-3 h-3 mr-2" />
                CLEAR QUEUE
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time Range Settings */}
      <Card className="brutal-card">
        <CardHeader className="bg-destructive text-destructive-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">SCAN SETTINGS</CardTitle>
        </CardHeader>
        <CardContent className="p-4 space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 items-start">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-bold mb-2 font-mono">
                  SCAN MODE
                </label>
                <Select value={extractionMode} onValueChange={(value: "hours" | "all") => setExtractionMode(value)}>
                  <SelectTrigger className="brutal-button">
                    <SelectValue placeholder="Select scan mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="hours">Last 24 Hours</SelectItem>
                    <SelectItem value="all">All Time</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {extractionMode === "hours" && (
                <div>
                  <label className="block text-sm font-bold mb-2 font-mono">
                    TIME RANGE (HOURS)
                  </label>
                  <Input
                    type="number"
                    value={timeRange}
                    onChange={(e) => setTimeRange(e.target.value)}
                    className="brutal-border font-mono"
                    placeholder="24"
                  />
                  <div className="text-xs text-muted-foreground mt-1 font-mono">
                    Scan for duplicates in files uploaded in the last {timeRange} hours
                  </div>
                </div>
              )}

              {extractionMode === "all" && (
                <div className="text-xs text-muted-foreground font-mono">
                  Scan for duplicates in all files since the beginning
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Button
                onClick={handleScanDuplicates}
                disabled={isLoading}
                className="bg-destructive text-destructive-foreground brutal-button w-full"
              >
                {isLoading ? (
                  <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="w-3 h-3 mr-2" />
                )}
                {isLoading ? "SCANNING..." : "SCAN FOR DUPLICATES"}
              </Button>
              <Button
                onClick={handleClearResults}
                disabled={duplicates.length === 0}
                className="bg-accent text-accent-foreground brutal-button w-full"
              >
                <Trash className="w-3 h-3 mr-2" />
                CLEAR RESULTS
              </Button>
            </div>
          </div>

          <div className="bg-muted p-4 brutal-border">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-3 h-3" />
              <span className="font-mono font-bold text-sm">SUPPORTED PLATFORMS:</span>
            </div>
            <div className="font-mono text-sm">
              STREAMP2P, RPMSHARE, UPNSHARE, FILEMOON
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Section */}
      <Card className="brutal-card">
        <CardHeader className="bg-accent text-accent-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">DUPLICATE CHECKER RESULTS</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {duplicates.length === 0 ? (
            <div className="text-center space-y-4">
              <CheckCircle className="w-14 h-14 mx-auto text-accent" />
              <h3 className="text-2xl font-bold font-mono">NO DUPLICATES FOUND!</h3>
              <p className="text-muted-foreground font-mono">All your files are unique across platforms</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-center mb-6">
                <AlertTriangle className="w-14 h-14 mx-auto text-destructive mb-2" />
                <h3 className="text-2xl font-bold font-mono text-destructive">DUPLICATES FOUND!</h3>
                <p className="text-muted-foreground font-mono">Found {duplicates.length} duplicate file groups</p>
              </div>

              <div className="space-y-4 max-h-96 overflow-y-auto">
                {duplicates.map((duplicate, index) => (
                  <Card key={index} className="brutal-card border-destructive">
                    <CardHeader className="bg-destructive text-destructive-foreground brutal-border-b">
                      <CardTitle className="font-mono text-sm">{duplicate.filename}</CardTitle>
                    </CardHeader>
                    <CardContent className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm font-mono font-bold mb-2">PLATFORMS:</div>
                          <div className="space-y-1">
                            {(duplicate.platforms || []).map((platform: string, i: number) => (
                              <div key={i} className="flex items-center justify-between text-xs font-mono">
                                <span className="uppercase">{platform}</span>
                                <span className="text-muted-foreground">{(duplicate.file_ids && duplicate.file_ids[i]) ?? ''}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div>
                          <div className="text-sm font-mono font-bold mb-2">EMBED CODES:</div>
                          <div className="space-y-1">
                            {Object.entries(duplicate.embed_codes || {}).map(([platform, embedCode]) => (
                              <div key={platform} className="text-xs">
                                <div className="font-mono font-bold uppercase">{platform}:</div>
                                <div className="bg-muted p-2 rounded text-xs font-mono break-all">
                                  {embedCode as string}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t">
                      <div className="grid grid-cols-2 gap-4 text-xs font-mono">
                        <div>
                          <span className="font-bold">Size:</span> {duplicate.size ?? ''} {duplicate.size ? 'bytes' : ''}
                        </div>
                        <div>
                          <span className="font-bold">Upload Dates:</span> {Array.isArray(duplicate.upload_dates) ? duplicate.upload_dates.join(', ') : (duplicate.upload_dates || '')}
                        </div>
                      </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default DuplicateChecker;