#!/usr/bin/env python3
import asyncio
from core.rclone_manager import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from config import Config

async def test_rclone_direct():
    """Test RClone manager directly"""
    try:
        print("Testing RClone Manager directly...")
        
        rclone_manager = RcloneManager()
        
        # Test URL download
        print(f"Testing URL download to folder: {Config.GDRIVE_PRIMARY_FOLDER_ID}")
        
        gid = await rclone_manager.add_url_download(
            url="https://httpbin.org/json",
            gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID,
            filename="test_httpbin.json"
        )
        
        print(f"Started download with GID: {gid}")
        
        # Monitor progress for 30 seconds
        for i in range(30):
            status = await rclone_manager.get_download_status(gid)
            progress = await rclone_manager.get_download_progress(gid)
            
            print(f"Status: {status}")
            print(f"Progress: {progress}")
            
            if status and status.get("status") in ["complete", "error"]:
                break
                
            await asyncio.sleep(1)
        
        final_status = await rclone_manager.get_download_status(gid)
        print(f"Final status: {final_status}")
        
        if final_status and final_status.get("status") == "complete":
            print("✅ RClone URL download test PASSED!")
            return True
        else:
            print("❌ RClone URL download test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    result = asyncio.run(test_rclone_direct())
    print(f"Test result: {'PASSED' if result else 'FAILED'}")
