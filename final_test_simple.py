#!/usr/bin/env python3
"""
Final comprehensive test - simple version without Unicode
"""
import asyncio
import aiohttp
import requests
import json
import platform
from pathlib import Path

BASE_URL = "http://localhost:8001"

async def test_subprocess_fix():
    """Test 1: Subprocess ProactorEventLoop fix"""
    print("1. Testing Subprocess Fix...")
    
    try:
        if platform.system() == "Windows":
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        # Test basic subprocess
        process = await asyncio.create_subprocess_exec(
            "cmd", "/c", "echo", "test",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            print("   PASS: Subprocess working")
            return True
        else:
            print("   FAIL: Subprocess failed")
            return False
    except Exception as e:
        print(f"   FAIL: Subprocess error: {e}")
        return False

def test_api_endpoints():
    """Test 2: API endpoints"""
    print("2. Testing API Endpoints...")
    
    tests = [
        ("Download Status", "GET", "/api/downloads/status", None),
        ("WebSocket Test", "GET", "/api/ws-test", None),
        ("URL Download", "POST", "/api/download/url", {"urls": ["https://httpbin.org/json"]}),
        ("Clear Queue", "POST", "/api/queue/clear", {"type": "all"})
    ]
    
    results = []
    for name, method, endpoint, payload in tests:
        try:
            url = f"{BASE_URL}{endpoint}"
            if method == "GET":
                response = requests.get(url)
            else:
                response = requests.post(url, json=payload)
            
            if response.status_code == 200:
                print(f"   PASS: {name}")
                results.append(True)
            else:
                print(f"   FAIL: {name} - Status: {response.status_code}")
                results.append(False)
        except Exception as e:
            print(f"   FAIL: {name} - Error: {e}")
            results.append(False)
    
    return all(results)

async def test_websocket():
    """Test 3: WebSocket connection"""
    print("3. Testing WebSocket...")
    
    try:
        session = aiohttp.ClientSession()
        async with session.ws_connect(f'ws://localhost:8001/ws') as ws:
            # Receive one message
            msg = await asyncio.wait_for(ws.receive(), timeout=5.0)
            if msg.type == aiohttp.WSMsgType.TEXT:
                data = json.loads(msg.data)
                if data.get('type') == 'downloads_update':
                    print("   PASS: WebSocket working")
                    await session.close()
                    return True
        
        await session.close()
        print("   FAIL: WebSocket - No valid message received")
        return False
        
    except Exception as e:
        print(f"   FAIL: WebSocket error: {e}")
        try:
            await session.close()
        except:
            pass
        return False

def test_rclone_availability():
    """Test 4: RClone availability"""
    print("4. Testing RClone Availability...")
    
    rclone_path = Path("backend/rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
    
    if rclone_path.exists():
        print("   PASS: RClone executable found")
        return True
    else:
        print("   FAIL: RClone executable not found")
        return False

def test_aria2_availability():
    """Test 5: Aria2 availability"""
    print("5. Testing Aria2 Availability...")
    
    aria2_path = Path("backend/aria2/aria2-1.37.0-win-64bit-build1/aria2c.exe")
    
    if aria2_path.exists():
        print("   PASS: Aria2 executable found")
        return True
    else:
        print("   FAIL: Aria2 executable not found")
        return False

def test_frontend_availability():
    """Test 6: Frontend availability"""
    print("6. Testing Frontend Availability...")
    
    try:
        response = requests.get("http://localhost:8080", timeout=5)
        if response.status_code == 200:
            print("   PASS: Frontend accessible")
            return True
        else:
            print(f"   FAIL: Frontend - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: Frontend error: {e}")
        return False

async def main():
    """Run all tests"""
    print("AutoUploadBot - Final Comprehensive Test")
    print("=" * 50)
    
    # Run all tests
    test_results = []
    
    # Async tests
    test_results.append(await test_subprocess_fix())
    test_results.append(await test_websocket())
    
    # Sync tests
    test_results.append(test_api_endpoints())
    test_results.append(test_rclone_availability())
    test_results.append(test_aria2_availability())
    test_results.append(test_frontend_availability())
    
    # Summary
    print("\n" + "=" * 50)
    print("FINAL TEST RESULTS:")
    print("=" * 50)
    
    test_names = [
        "Subprocess Fix",
        "WebSocket Connection", 
        "API Endpoints",
        "RClone Availability",
        "Aria2 Availability",
        "Frontend Availability"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "PASS" if result else "FAIL"
        print(f"{i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"OVERALL: {passed}/{len(test_results)} tests passed")
    
    if passed == len(test_results):
        print("SUCCESS: ALL TESTS PASSED! AutoUploadBot is working correctly!")
    else:
        print(f"WARNING: {len(test_results) - passed} tests failed. Check the issues above.")

if __name__ == "__main__":
    asyncio.run(main())
