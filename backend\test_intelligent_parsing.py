#!/usr/bin/env python3
"""
Test the intelligent filename parsing logic
This will verify that HTML entities and special characters are handled correctly
"""

import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))
from api.video_hosts import VideoHostManager

def test_filename_normalization():
    """Test the new filename normalization logic"""
    print("🧪 TESTING INTELLIGENT FILENAME PARSING")
    print("=" * 60)
    
    vm = VideoHostManager()
    
    # Test cases from the FileMoon list with HTML entities
    test_cases = [
        # HTML entity issues
        ("SaamRajya &#40;Kingdom&#41; 2025 1080p HEVC V2-HDTC Hindi-CLEAN x265-HDHub4u Ms", 
         "SaamRajya (Kingdom) 2025 1080p HEVC V2-HDTC Hindi-CLEAN x265-HDHub4u Ms"),
        
        ("Maa &#40;2025&#41; WEB-DL 1080p", 
         "Maa (2025) WEB-DL 1080p"),
        
        ("The Bhootnii &#40;2025&#41; 1080p WEB-DL", 
         "The Bhootnii (2025) 1080p WEB-DL"),
        
        ("Sitaare Zameen Par &#40;2025&#41; 1080p WEB-DL", 
         "Sitaare Zameen Par (2025) 1080p WEB-DL"),
        
        ("The Fantastic Four: First Steps &#40;2025&#41; 1080p [Hindi-English]", 
         "The Fantastic Four: First Steps (2025) 1080p [Hindi-English]"),
        
        ("Brick &#40;2025&#41; 1080p WEB-DL &#40;Hin-Eng&#41;", 
         "Brick (2025) 1080p WEB-DL (Hin-Eng)"),
        
        ("Happy Gilmore 2 &#40;2025&#41; 1080P WEB-DL &#40;Hin-Eng&#41;", 
         "Happy Gilmore 2 (2025) 1080P WEB-DL (Hin-Eng)"),
        
        # Extension variations
        ("Movie.2025.1080p.mkv.mp4", 
         "Movie.2025.1080p.mkv"),
        
        ("Movie.2025.1080p.mkv", 
         "Movie (2025) 1080p.mkv"),
    ]
    
    print("Testing normalization pairs:")
    duplicates_found = 0
    total_tests = len(test_cases)
    
    for i, (filename1, filename2) in enumerate(test_cases, 1):
        norm1 = vm._normalize_filename(filename1)
        norm2 = vm._normalize_filename(filename2)
        
        print(f"\n📝 Test {i}/{total_tests}:")
        print(f"  Original 1: {filename1}")
        print(f"  Original 2: {filename2}")
        print(f"  Normalized 1: '{norm1}'")
        print(f"  Normalized 2: '{norm2}'")
        
        is_match = vm._intelligent_filename_match(filename1, filename2)
        
        if is_match:
            duplicates_found += 1
            print(f"  ✅ MATCH: Correctly identified as duplicate")
        else:
            print(f"  ❌ NO MATCH: Should be duplicate but not detected")
    
    print(f"\n📊 NORMALIZATION TEST RESULTS:")
    print(f"  Total tests: {total_tests}")
    print(f"  Duplicates found: {duplicates_found}")
    print(f"  Success rate: {duplicates_found/total_tests*100:.1f}%")
    
    return duplicates_found == total_tests

async def test_actual_extraction():
    """Test the actual extraction with new logic"""
    print(f"\n🎯 TESTING ACTUAL EXTRACTION WITH NEW LOGIC")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test ALL Time extraction
        embed_data = await vm.extract_embed_codes(999999)
        count = len(embed_data)
        
        print(f"✅ Extraction result: {count} files")
        print(f"📊 Expected: 107-110 files (if duplicate detection working)")
        
        if 107 <= count <= 120:  # Allow some margin
            print(f"✅ WITHIN ACCEPTABLE RANGE")
            return True
        else:
            print(f"⚠️ Outside expected range (off by {abs(count - 108)})")
            
            # Show some results to understand the issue
            print(f"\n📋 Sample results:")
            for i, file_data in enumerate(embed_data[:5]):
                filename = file_data['filename']
                hosts = list(file_data.get('embed_codes', {}).keys())
                print(f"  {i+1}. {filename} -> {hosts}")
            
            return False
        
    finally:
        await vm.close()

async def main():
    """Main test function"""
    print("🚀 INTELLIGENT FILENAME PARSING TEST")
    print("=" * 80)
    
    # Test 1: Normalization logic
    normalization_ok = test_filename_normalization()
    
    # Test 2: Actual extraction
    extraction_ok = await test_actual_extraction()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Normalization logic: {'PASSED' if normalization_ok else 'FAILED'}")
    print(f"✅ Extraction test: {'PASSED' if extraction_ok else 'NEEDS TUNING'}")
    
    if normalization_ok and extraction_ok:
        print(f"\n🎉 INTELLIGENT FILENAME PARSING: WORKING PERFECTLY!")
        return True
    else:
        print(f"\n⚠️ INTELLIGENT FILENAME PARSING: NEEDS ADJUSTMENT")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)