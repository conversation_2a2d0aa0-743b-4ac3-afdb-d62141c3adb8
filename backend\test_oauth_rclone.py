#!/usr/bin/env python3
"""
Test OAuth RClone Configuration
"""
import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from core.rclone_manager import RcloneManager
from config import Config

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)


async def test_oauth_rclone():
    """Test OAuth RClone configuration"""
    
    LOGGER.info("🚀 Testing OAuth RClone Configuration")
    
    # Initialize RClone Manager
    rclone_manager = RcloneManager()
    
    # Test URL for download
    test_url = "https://httpbin.org/uuid"
    
    try:
        # Test Primary Account
        LOGGER.info("📁 Testing Primary Account Upload...")
        primary_gid = await rclone_manager.add_url_download(
            url=test_url,
            gdrive_folder_id="",  # Root folder
            filename="test_primary_oauth.json",
            use_secondary=False
        )
        
        if primary_gid:
            LOGGER.info(f"✅ Primary account upload started: {primary_gid}")
            
            # Wait for completion
            for i in range(30):  # Wait up to 30 seconds
                status = await rclone_manager.get_download_status(primary_gid)
                if status:
                    LOGGER.info(f"Primary Status: {status['status']} - Progress: {status.get('progress', 0)}%")
                    if status['status'] in ['complete', 'error']:
                        break
                await asyncio.sleep(1)
        else:
            LOGGER.error("❌ Failed to start primary account upload")
        
        # Test Secondary Account
        LOGGER.info("📁 Testing Secondary Account Upload...")
        secondary_gid = await rclone_manager.add_url_download(
            url=test_url,
            gdrive_folder_id="AutoUploadBot-Secondary",
            filename="test_secondary_oauth.json",
            use_secondary=True
        )
        
        if secondary_gid:
            LOGGER.info(f"✅ Secondary account upload started: {secondary_gid}")
            
            # Wait for completion
            for i in range(30):  # Wait up to 30 seconds
                status = await rclone_manager.get_download_status(secondary_gid)
                if status:
                    LOGGER.info(f"Secondary Status: {status['status']} - Progress: {status.get('progress', 0)}%")
                    if status['status'] in ['complete', 'error']:
                        break
                await asyncio.sleep(1)
        else:
            LOGGER.error("❌ Failed to start secondary account upload")
        
        # Final status check
        LOGGER.info("📊 Final Status Report:")
        if primary_gid:
            primary_status = await rclone_manager.get_download_status(primary_gid)
            LOGGER.info(f"Primary Account: {primary_status['status'] if primary_status else 'Unknown'}")
        
        if secondary_gid:
            secondary_status = await rclone_manager.get_download_status(secondary_gid)
            LOGGER.info(f"Secondary Account: {secondary_status['status'] if secondary_status else 'Unknown'}")
        
        LOGGER.info("🎉 OAuth RClone test completed!")
        
    except Exception as e:
        LOGGER.error(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_oauth_rclone())
