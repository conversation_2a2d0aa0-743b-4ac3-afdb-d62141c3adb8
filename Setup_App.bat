@echo off
setlocal
REM ================================================================
REM AutoUploadBot - WORKING Setup Script
REM Installs all dependencies and sets up the application
REM ================================================================

cd /d "%~dp0"

echo ================================================================
echo AutoUploadBot Setup
echo ================================================================
echo Starting setup at %DATE% %TIME%

REM Check prerequisites
where python >NUL 2>&1
if errorlevel 1 (
  echo [ERROR] Python not found in PATH
  echo Please install Python 3.10+ and re-run
  pause
  goto :EOF
)

where npm >NUL 2>&1
if errorlevel 1 (
  echo [ERROR] npm not found in PATH
  echo Please install Node.js and re-run
  pause
  goto :EOF
)

echo [OK] Prerequisites found

REM Install Python dependencies
echo.
echo ================================================================
echo [1/4] Installing Python dependencies...
echo ================================================================
cd /d "%CD%\backend"
if not exist "requirements.txt" (
  echo [ERROR] requirements.txt not found in backend directory
  pause
  goto :EOF
)

echo Upgrading pip...
python -m pip install --upgrade pip >NUL 2>&1

echo Installing requirements...
python -m pip install -r requirements.txt >NUL 2>&1
if errorlevel 1 (
  echo [ERROR] pip install failed
  pause
  goto :EOF
)

echo [OK] Python dependencies installed
cd /d "%CD%\.."

REM Install Node.js dependencies
echo.
echo ================================================================
echo [2/4] Installing Node.js dependencies...
echo ================================================================
if not exist "node_modules" (
  echo Installing npm packages...
  npm install >NUL 2>&1
  if errorlevel 1 (
    echo [ERROR] npm install failed
    pause
    goto :EOF
  )
  echo [OK] Node.js dependencies installed
) else (
  echo [INFO] node_modules already exists
)

REM Setup Aria2
echo.
echo ================================================================
echo [3/4] Setting up Aria2...
echo ================================================================
cd /d "%CD%\backend"
if not exist "setup_aria2.py" (
  echo [ERROR] setup_aria2.py not found in backend directory
  pause
  goto :EOF
)

python setup_aria2.py >NUL 2>&1
if errorlevel 1 (
  echo [ERROR] Aria2 setup failed
  echo You can try running: cd backend ^&^& python setup_aria2.py
  pause
  goto :EOF
)

echo [OK] Aria2 setup completed
cd /d "%CD%\.."

REM Final setup
echo.
echo ================================================================
echo [4/4] Final setup...
echo ================================================================

REM Check rclone
set RCLONE_EXE=%CD%\backend\rclone\rclone-v1.70.3-windows-amd64\rclone.exe
if exist "%RCLONE_EXE%" (
  echo [OK] rclone.exe found
) else (
  echo [WARN] rclone.exe not found at backend\rclone\
)

REM Create directories
if not exist "%CD%\backend\accounts" mkdir "%CD%\backend\accounts"
if not exist "%CD%\backend\downloads" mkdir "%CD%\backend\downloads"

REM Setup .env
if exist "%CD%\backend\.env" (
  echo [INFO] backend\.env already exists
) else if exist "%CD%\backend\.env.example" (
  copy /Y "%CD%\backend\.env.example" "%CD%\backend\.env" >NUL 2>&1
  echo [INFO] Created backend\.env from .env.example
) else (
  echo [INFO] No .env.example found. Create backend\.env manually.
)

echo.
echo ================================================================
echo Setup completed successfully!
echo ================================================================
echo.
echo Next steps:
echo 1. Configure backend\.env with your API keys
echo 2. Run Start_App_WORKING.bat to launch the application
echo ================================================================
echo.
echo Press any key to exit...
pause >NUL

endlocal
exit /b 0
