# File utilities adapted from mirror-leech-telegram-bot
import os
import asyncio
import logging
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
from pathlib import Path
from typing import List, Optional
import aiofiles
import aioshutil

from config import Config

LOGGER = logging.getLogger(__name__)

# Archive extensions from mirror-leech-telegram-bot
ARCH_EXT = [
    ".tar.bz2", ".tar.gz", ".bz2", ".gz", ".tar.xz", ".tar", ".tbz2", ".tgz",
    ".lzma2", ".zip", ".7z", ".z", ".rar", ".iso", ".wim", ".cab", ".apm",
    ".arj", ".chm", ".cpio", ".cramfs", ".deb", ".dmg", ".fat", ".hfs",
    ".lzh", ".lzma", ".mbr", ".msi", ".mslz", ".nsis", ".ntfs", ".rpm",
    ".squashfs", ".udf", ".vhd", ".xar", ".lzip", ".zpaq", ".zstd"
]


def get_mime_type(file_path: str) -> str:
    """Get MIME type of file following mirror-leech-telegram-bot pattern"""
    if os.path.islink(file_path):
        file_path = os.readlink(file_path)

    try:
        if HAS_MAGIC:
            mime = magic.Magic(mime=True)
            mime_type = mime.from_file(file_path)
            mime_type = mime_type or "text/plain"
            return mime_type
        else:
            # Fallback to basic extension-based detection
            ext = os.path.splitext(file_path)[1].lower()
            mime_types = {
                '.mp4': 'video/mp4',
                '.mkv': 'video/x-matroska',
                '.avi': 'video/x-msvideo',
                '.mov': 'video/quicktime',
                '.wmv': 'video/x-ms-wmv',
                '.flv': 'video/x-flv',
                '.webm': 'video/webm',
                '.m4v': 'video/x-m4v',
                '.zip': 'application/zip',
                '.rar': 'application/x-rar-compressed',
                '.7z': 'application/x-7z-compressed'
            }
            return mime_types.get(ext, "application/octet-stream")
    except Exception as e:
        LOGGER.error(f"Error getting MIME type for {file_path}: {e}")
        return "application/octet-stream"


def is_archive(file_path: str) -> bool:
    """Check if file is an archive"""
    return any(file_path.lower().endswith(ext) for ext in ARCH_EXT)


def is_video_file(file_path: str) -> bool:
    """Check if file is a video file"""
    return any(file_path.lower().endswith(ext) for ext in Config.VIDEO_EXTENSIONS)


async def get_file_size(file_path: str) -> int:
    """Get file size asynchronously"""
    try:
        stat = await aiofiles.os.stat(file_path)
        return stat.st_size
    except Exception as e:
        LOGGER.error(f"Error getting file size for {file_path}: {e}")
        return 0


async def create_directory(path: str) -> bool:
    """Create directory if it doesn't exist"""
    try:
        await aiofiles.os.makedirs(path, exist_ok=True)
        return True
    except Exception as e:
        LOGGER.error(f"Error creating directory {path}: {e}")
        return False


async def move_file(src: str, dst: str) -> bool:
    """Move file from source to destination"""
    try:
        await aioshutil.move(src, dst)
        return True
    except Exception as e:
        LOGGER.error(f"Error moving file from {src} to {dst}: {e}")
        return False


async def copy_file(src: str, dst: str) -> bool:
    """Copy file from source to destination"""
    try:
        await aioshutil.copy2(src, dst)
        return True
    except Exception as e:
        LOGGER.error(f"Error copying file from {src} to {dst}: {e}")
        return False


async def delete_file(file_path: str) -> bool:
    """Delete file"""
    try:
        await aiofiles.os.remove(file_path)
        return True
    except Exception as e:
        LOGGER.error(f"Error deleting file {file_path}: {e}")
        return False


async def delete_directory(dir_path: str) -> bool:
    """Delete directory recursively"""
    try:
        await aioshutil.rmtree(dir_path)
        return True
    except Exception as e:
        LOGGER.error(f"Error deleting directory {dir_path}: {e}")
        return False


async def list_files_recursive(directory: str, extensions: Optional[List[str]] = None) -> List[str]:
    """List all files in directory recursively"""
    files = []
    try:
        for root, dirs, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(root, filename)
                if extensions:
                    if any(filename.lower().endswith(ext) for ext in extensions):
                        files.append(file_path)
                else:
                    files.append(file_path)
    except Exception as e:
        LOGGER.error(f"Error listing files in {directory}: {e}")
    
    return files


async def get_video_files_from_directory(directory: str) -> List[str]:
    """Get all video files from directory"""
    return await list_files_recursive(directory, Config.VIDEO_EXTENSIONS)


async def clean_filename(filename: str) -> str:
    """Clean filename for safe storage"""
    # Remove or replace invalid characters
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    filename = filename.strip(' .')
    
    # Limit length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename


def get_readable_file_size(size_bytes: int) -> str:
    """Convert bytes to human readable format"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"


def get_readable_time(seconds: int) -> str:
    """Convert seconds to human readable time format"""
    if seconds < 0:
        return "Unknown"
    
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours}h {minutes}m {seconds}s"
    elif minutes > 0:
        return f"{minutes}m {seconds}s"
    else:
        return f"{seconds}s"


async def extract_archive_gdrive_to_gdrive(gdrive_file_id: str, dest_folder_id: str, gdrive_helper) -> bool:
    """Extract archive from Google Drive to Google Drive (server-to-server)"""
    try:
        # This will be implemented using Google Drive API and cloud-based extraction
        # For now, we'll use a different approach - download temporarily, extract, upload videos, cleanup
        LOGGER.info(f"Starting server-to-server extraction: {gdrive_file_id} -> {dest_folder_id}")

        # TODO: Implement cloud-based extraction or use Google Apps Script
        # For MVP, we'll note that this needs cloud-based solution

        LOGGER.warning("Archive extraction needs cloud-based implementation - no local processing")
        return False

    except Exception as e:
        LOGGER.error(f"Error extracting archive from Google Drive: {e}")
        return False
