import sys
sys.path.insert(0, r'g:/My Websites/Catalogue-Website/automatic-upload-bot/automatic-upload-bot')
from backend.api.video_hosts import VideoHostManager
from backend.config import Config

print('Config FILEMOON_API_KEY:', Config.FILEMOON_API_KEY)
print('Config FILEMOON_API_URL:', Config.FILEMOON_API_URL)

import asyncio, aiohttp

async def run():
    mgr = VideoHostManager()
    await mgr.initialize()
    try:
        # Use a very large hours to avoid created filter being sent
        files = await mgr._list_filemoon_files(999999)
        print('Found', len(files), 'files via VideoHostManager')
        if files:
            import json
            print(json.dumps(files[:10], indent=2))

        # Also test direct HTTP to both endpoints using aiohttp
        async with aiohttp.ClientSession() as session:
            for base in [Config.FILEMOON_API_URL, 'https://filemoonapi.com/api']:
                fm = base.rstrip('/')
                if fm.endswith('/api'):
                    fm = fm[:-4]
                url = f"{fm}/api/file/list"
                params = {'key': Config.FILEMOON_API_KEY, 'per_page':5, 'page':1}
                try:
                    async with session.get(url, params=params, timeout=15) as resp:
                        print('\nDirect request to', url, 'status', resp.status)
                        try:
                            j = await resp.json()
                            import json
                            print(json.dumps(j, indent=2)[:2000])
                        except Exception as e:
                            print('non-json response')
                except Exception as e:
                    print('Direct request failed for', url, e)
    finally:
        await mgr.close()

if __name__ == '__main__':
    asyncio.run(run())
