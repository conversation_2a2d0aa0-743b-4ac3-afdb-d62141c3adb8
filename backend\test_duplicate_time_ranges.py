#!/usr/bin/env python3
"""
Test script to verify that the duplicate checker API works correctly with flexible time ranges
"""

import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

async def test_duplicate_checker_time_ranges():
    """Test the duplicate checker with various time ranges"""
    print("🔍 TESTING DUPLICATE CHECKER WITH FLEXIBLE TIME RANGES")
    print("=" * 70)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test with 24 hours
        print("Testing 24-hour duplicate detection:")
        duplicates_24h = await vm.check_duplicate_files(24)
        print(f"✅ Found {len(duplicates_24h)} duplicate groups in 24 hours")
        
        # Test with 48 hours
        print("\nTesting 48-hour duplicate detection:")
        duplicates_48h = await vm.check_duplicate_files(48)
        print(f"✅ Found {len(duplicates_48h)} duplicate groups in 48 hours")
        
        # Test with 72 hours
        print("\nTesting 72-hour duplicate detection:")
        duplicates_72h = await vm.check_duplicate_files(72)
        print(f"✅ Found {len(duplicates_72h)} duplicate groups in 72 hours")
        
        # Test with ALL time (large number)
        print("\nTesting ALL-time duplicate detection:")
        duplicates_all = await vm.check_duplicate_files(999999)
        print(f"✅ Found {len(duplicates_all)} duplicate groups in ALL time")
        
        # Verify that longer time ranges include results from shorter ones
        print("\n📊 VALIDATION RESULTS:")
        if len(duplicates_48h) >= len(duplicates_24h):
            print("✅ 48-hour range includes 24-hour results")
        else:
            print("❌ 48-hour range does not include 24-hour results")
            
        if len(duplicates_72h) >= len(duplicates_48h):
            print("✅ 72-hour range includes 48-hour results")
        else:
            print("❌ 72-hour range does not include 48-hour results")
            
        if len(duplicates_all) >= len(duplicates_72h):
            print("✅ ALL-time range includes 72-hour results")
        else:
            print("❌ ALL-time range does not include 72-hour results")
        
        # Show sample results
        if duplicates_24h:
            print(f"\n📋 SAMPLE 24-HOUR DUPLICATE GROUPS:")
            for i, dup in enumerate(duplicates_24h[:3], 1):
                host = dup.get('host', 'Unknown')
                filename = dup.get('filename', 'Unknown')
                count = dup.get('count', 0)
                print(f"  {i}. Host: {host}")
                print(f"     File: {filename}")
                print(f"     Copies: {count}")
                print()
        else:
            print("\n✅ No duplicates found in 24 hours (this is expected!)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing duplicate checker: {e}")
        return False
        
    finally:
        await vm.close()

if __name__ == "__main__":
    success = asyncio.run(test_duplicate_checker_time_ranges())
    sys.exit(0 if success else 1)