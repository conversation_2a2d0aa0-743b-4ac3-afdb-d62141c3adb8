#!/usr/bin/env python3
"""
Test script to verify that both issues are completely fixed:
1. Duplicate Checker frontend error (Cannot read properties of undefined)
2. Embed Code extraction pagination (only getting 51 entries instead of ALL)
"""

import requests
import json
import time

def test_duplicate_checker():
    """Test that duplicate checker API returns proper structure for frontend"""
    print("🔍 Testing Duplicate Checker API...")
    
    try:
        response = requests.post(
            "http://localhost:8001/api/duplicates/check",
            headers={"Content-Type": "application/json"},
            json={"time_range": "all"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response Status: {data.get('status')}")
            
            duplicates = data.get('duplicates', [])
            print(f"✅ Found {len(duplicates)} duplicate groups")
            
            # Check structure for frontend compatibility
            if duplicates:
                first_duplicate = duplicates[0]
                required_fields = ['filename', 'platforms', 'file_ids']
                
                for field in required_fields:
                    if field in first_duplicate:
                        print(f"✅ Field '{field}' present: {type(first_duplicate[field])}")
                    else:
                        print(f"❌ Missing required field: {field}")
                        return False
                
                # Check that platforms and file_ids are arrays
                if isinstance(first_duplicate.get('platforms'), list):
                    print(f"✅ 'platforms' is array with {len(first_duplicate['platforms'])} items")
                else:
                    print(f"❌ 'platforms' is not an array: {type(first_duplicate.get('platforms'))}")
                    return False
                    
                if isinstance(first_duplicate.get('file_ids'), list):
                    print(f"✅ 'file_ids' is array with {len(first_duplicate['file_ids'])} items")
                else:
                    print(f"❌ 'file_ids' is not an array: {type(first_duplicate.get('file_ids'))}")
                    return False
                    
            print("✅ Duplicate Checker API structure is correct for frontend!")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing duplicate checker: {e}")
        return False

def test_embed_extraction_pagination():
    """Test that embed extraction gets ALL files, not just 51"""
    print("\n📄 Testing Embed Code Extraction Pagination...")
    
    try:
        response = requests.post(
            "http://localhost:8001/api/embed/extract",
            headers={"Content-Type": "application/json"},
            json={"hours": 999999}  # All time
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response Status: {data.get('status')}")
            
            embed_data = data.get('embed_data', [])
            total_files = len(embed_data)
            print(f"✅ Total files with embed codes: {total_files}")
            
            # Check if we got more than 51 files (the old limit)
            if total_files > 51:
                print(f"✅ SUCCESS: Got {total_files} files (more than the old 51 limit)")
                
                # Check embed code structure
                if embed_data:
                    first_file = embed_data[0]
                    embed_codes = first_file.get('embed_codes', {})
                    
                    print(f"✅ Sample file: {first_file.get('filename', 'Unknown')}")
                    print(f"✅ Available hosts: {list(embed_codes.keys())}")
                    
                    # Check FileMoon embed code format
                    if 'filemoon' in embed_codes:
                        filemoon_code = embed_codes['filemoon']
                        if 'filemoon.to/e/' in filemoon_code and '/code/' not in filemoon_code:
                            print("✅ FileMoon embed code format is correct (no '/code/' issue)")
                        else:
                            print(f"❌ FileMoon embed code has '/code/' issue: {filemoon_code[:100]}...")
                            return False
                
                return True
            else:
                print(f"❌ Only got {total_files} files (should be more than 51 for 'all time')")
                return False
                
        else:
            print(f"❌ API returned status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing embed extraction: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting verification of both fixes...\n")
    
    # Test 1: Duplicate Checker
    test1_passed = test_duplicate_checker()
    
    # Test 2: Embed Extraction Pagination
    test2_passed = test_embed_extraction_pagination()
    
    # Summary
    print("\n" + "="*60)
    print("📋 VERIFICATION SUMMARY")
    print("="*60)
    print(f"1. Duplicate Checker Frontend Fix: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"2. Embed Extraction Pagination Fix: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 ALL FIXES VERIFIED SUCCESSFULLY!")
        print("✅ Duplicate Checker will no longer crash the frontend")
        print("✅ Embed Extraction now gets ALL files from all hosts")
        return True
    else:
        print("\n❌ Some fixes need more work")
        return False

if __name__ == "__main__":
    main()
