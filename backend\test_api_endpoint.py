#!/usr/bin/env python3
"""
Test script to verify that the duplicate checker API endpoint works with the new hours parameter
"""

import requests
import json

def test_duplicate_checker_api():
    """Test the duplicate checker API endpoint"""
    print("🔍 TESTING DUPLICATE CHECKER API ENDPOINT")
    print("=" * 50)
    
    # Test with 24 hours
    print("Testing API with 24 hours:")
    try:
        response = requests.post(
            "http://localhost:8001/api/duplicates/check",
            headers={"Content-Type": "application/json"},
            json={"hours": 24},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Response Status: {data.get('status')}")
            print(f"✅ Hours Parameter: {data.get('hours')}")
            print(f"✅ Found {len(data.get('duplicates', []))} duplicate groups")
            print("✅ API endpoint working correctly with hours parameter")
            return True
        else:
            print(f"❌ API returned status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API: {e}")
        return False

if __name__ == "__main__":
    success = test_duplicate_checker_api()
    exit(0 if success else 1)