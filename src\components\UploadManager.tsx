import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Download, Magnet, Trash, RefreshCw } from "lucide-react";
import { apiService, DownloadStatus } from "@/services/api";
import { useToast } from "@/hooks/use-toast";

const UploadManager = () => {
  const [remoteUrls, setRemoteUrls] = useState("");
  const [magnetLinks, setMagnetLinks] = useState("");
  const [downloadQueue, setDownloadQueue] = useState<DownloadStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { toast } = useToast();

  // Load downloads on component mount
  useEffect(() => {
    loadDownloads();

    // Set up periodic refresh
    const interval = setInterval(loadDownloads, 3000);
    return () => clearInterval(interval);
  }, []);

  const loadDownloads = async () => {
    try {
      const response = await apiService.getDownloadsStatus();
      setDownloadQueue(response.downloads || []);
    } catch (error) {
      console.error("Failed to load downloads:", error);
    }
  };

  const handleStartDownload = async () => {
    if (!remoteUrls.trim()) {
      toast({
        title: "Error",
        description: "Please enter at least one URL",
        variant: "destructive",
      });
      return;
    }

    const urls = remoteUrls.split('\n').filter(url => url.trim());

    if (urls.length > 50) {
      toast({
        title: "Error",
        description: "Maximum 50 URLs allowed per batch",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.downloadFromUrls(urls);

      const failed = (response?.results || []).filter((r: any) => r.status !== 'added');
      if (failed.length > 0) {
        const firstErr = failed[0]?.error || response?.message || 'Some URLs failed';
        toast({
          title: `Partial Failure (${failed.length} failed)`,
          description: firstErr,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: response.message,
        });
      }

      setRemoteUrls("");
      await loadDownloads();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start downloads",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartTorrent = async () => {
    if (!magnetLinks.trim()) {
      toast({
        title: "Error",
        description: "Please enter at least one magnet link",
        variant: "destructive",
      });
      return;
    }

    const magnets = magnetLinks.split('\n').filter(magnet => magnet.trim());

    if (magnets.length > 50) {
      toast({
        title: "Error",
        description: "Maximum 50 magnet links allowed per batch",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.downloadFromMagnets(magnets);

      const failed = (response?.results || []).filter((r: any) => r.status !== 'added');
      if (failed.length > 0) {
        const firstErr = failed[0]?.error || response?.message || 'Some magnets failed';
        toast({
          title: `Partial Failure (${failed.length} failed)`,
          description: firstErr,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Success",
          description: response.message,
        });
      }

      setMagnetLinks("");
      await loadDownloads();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start torrent downloads",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadDownloads();
    setIsRefreshing(false);
  };

  const handleClearQueue = async () => {
    try {
      await apiService.clearQueue("all");
      toast({
        title: "Success",
        description: "Queue cleared successfully",
      });
      await loadDownloads();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to clear queue",
        variant: "destructive",
      });
    }
  };

  const handleCancelTask = async (taskId: string) => {
    try {
      await apiService.cancelTask(taskId);
      toast({
        title: "Success",
        description: "Task cancelled successfully",
      });
      await loadDownloads();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to cancel task",
        variant: "destructive",
      });
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatSpeed = (bytesPerSec: number): string => {
    return formatFileSize(bytesPerSec) + "/s";
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Remote URL Downloader */}
        <Card className="brutal-card">
          <CardHeader className="bg-primary text-primary-foreground brutal-border-b">
            <CardTitle className="flex items-center gap-2 font-mono text-lg">
              <Download className="w-4 h-4" />
              REMOTE URL DOWNLOADER
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-bold mb-2 font-mono">
                PASTE YOUR URLS (ONE PER LINE)
              </label>
              <Textarea
                value={remoteUrls}
                onChange={(e) => setRemoteUrls(e.target.value)}
                placeholder="https://example.com/file1.zip&#10;https://example.com/file2.rar&#10;ftp://server.com/video.mp4&#10;&#10;Maximum 50 URLs per batch"
                className="brutal-border h-32 font-mono text-sm"
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleStartDownload}
                disabled={isLoading}
                className="bg-primary text-primary-foreground brutal-button flex-1"
              >
                {isLoading ? (
                  <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                ) : (
                  <Download className="w-3 h-3 mr-2" />
                )}
                {isLoading ? "STARTING..." : "START DOWNLOAD"}
              </Button>
              <Button
                variant="outline"
                className="brutal-button"
                onClick={() => setRemoteUrls("")}
                disabled={isLoading}
              >
                CLEAR
              </Button>
            </div>
            <div className="bg-secondary p-3 brutal-border">
              <div className="text-sm font-mono">
                <strong>+ SUPPORTED FORMATS:</strong><br />
                HTTP/HTTPS, FTP, ZIP, RAR, 7Z, TAR.GZ + ALL VIDEO FORMATS
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Magnet Link Downloader */}
        <Card className="brutal-card">
          <CardHeader className="bg-purple text-purple-foreground brutal-border-b">
            <CardTitle className="flex items-center gap-2 font-mono text-lg">
              <Magnet className="w-4 h-4" />
              MAGNET LINK DOWNLOADER
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 space-y-4">
            <div>
              <label className="block text-sm font-bold mb-2 font-mono">
                PASTE YOUR MAGNET LINKS (ONE PER LINE)
              </label>
              <Textarea
                value={magnetLinks}
                onChange={(e) => setMagnetLinks(e.target.value)}
                placeholder="magnet:?xt=urn:btih:1234567890abcdef...&#10;magnet:?xt=urn:btih:fedcba0987654321...&#10;&#10;Maximum 50 magnet links per batch"
                className="brutal-border h-32 font-mono text-sm"
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleStartTorrent}
                disabled={isLoading}
                className="bg-purple text-purple-foreground brutal-button flex-1"
              >
                {isLoading ? (
                  <RefreshCw className="w-3 h-3 mr-2 animate-spin" />
                ) : (
                  <Magnet className="w-3 h-3 mr-2" />
                )}
                {isLoading ? "STARTING..." : "START TORRENT"}
              </Button>
              <Button
                variant="outline"
                className="brutal-button"
                onClick={() => setMagnetLinks("")}
                disabled={isLoading}
              >
                CLEAR
              </Button>
            </div>
            <div className="bg-secondary p-3 brutal-border">
              <div className="text-sm font-mono">
                <strong>+ TORRENT FEATURES:</strong><br />
                DHT, PEX, MAGNET LINKS, FAST RESUME, AUTO-EXTRACT ARCHIVES
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Download Queue */}
      <Card className="brutal-card">
        <CardHeader className="bg-accent text-accent-foreground brutal-border-b flex flex-row items-center justify-between">
          <CardTitle className="font-mono text-lg">DOWNLOAD QUEUE</CardTitle>
          <div className="flex gap-2">
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              variant="outline"
              size="sm"
              className="brutal-button bg-background text-foreground hover:bg-muted hover:text-foreground"
            >
              {isRefreshing ? (
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
              ) : (
                <RefreshCw className="w-3 h-3 mr-1" />
              )}
              REFRESH
            </Button>
            <Button
              onClick={handleClearQueue}
              variant="destructive"
              size="sm"
              className="brutal-button"
            >
              CLEAR COMPLETED
            </Button>
            <span className="font-mono text-sm px-3 py-1 bg-background text-foreground brutal-border">
              TOTAL JOBS: {downloadQueue.length}
            </span>
          </div>
        </CardHeader>
        <CardContent className="p-4">
          <div className="space-y-3">
            {downloadQueue.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground font-mono">
                NO ACTIVE DOWNLOADS
              </div>
            ) : (
              downloadQueue.map((item) => (
                <div key={item.gid} className="brutal-card p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className={`px-2 py-1 text-xs font-bold brutal-border ${
                        item.status === 'complete' ? 'bg-accent text-accent-foreground' :
                        item.status === 'active' ? 'bg-primary text-primary-foreground' :
                        item.status === 'waiting' ? 'bg-secondary text-secondary-foreground' :
                        item.status === 'error' ? 'bg-destructive text-destructive-foreground' :
                        'bg-secondary text-secondary-foreground'
                      }`}>
                        {item.status.toUpperCase()}
                      </div>
                    </div>
                    <Button
                      onClick={() => handleCancelTask(item.gid)}
                      variant="destructive"
                      size="sm"
                      className="brutal-button"
                    >
                      <Trash className="w-3 h-3" />
                    </Button>
                  </div>
                  <div className="space-y-1">
                    <div className="font-mono font-bold">{item.filename}</div>
                    <div className="text-sm text-muted-foreground font-mono">
                      Size: {formatFileSize(item.total_length)} •
                      Downloaded: {formatFileSize(item.completed_length)}
                      {item.download_speed > 0 && (
                        <> • Speed: {formatSpeed(item.download_speed)}</>
                      )}
                    </div>
                    {(item.status === 'active' || item.status === 'waiting') && (
                      <div className="mt-2">
                        <div className="flex justify-between text-xs font-mono mb-1">
                          <span>Progress: {item.progress.toFixed(1)}%</span>
                          {item.eta !== "unknown" && (
                            <span>ETA: {item.eta}</span>
                          )}
                        </div>
                        <div className="w-full bg-muted brutal-border h-4">
                          <div
                            className="bg-primary h-full transition-all duration-300"
                            style={{ width: `${Math.min(item.progress, 100)}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UploadManager;