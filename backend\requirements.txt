# Core dependencies - Python 3.13 compatible
fastapi
uvicorn[standard]
aiofiles
aiohttp

# Aria2 WebSocket client - MANDATORY
aioaria2

# Google Drive integration - <PERSON><PERSON><PERSON><PERSON><PERSON>
google-api-python-client
google-auth-httplib2
google-auth-oauthlib

# Retry and error handling - MANDATORY
tenacity

# File handling and utilities - MANDATORY
psutil

# Archive extraction - MANDATORY CORE FEATURE
py7zr>=0.20.0
rarfile>=4.0
patool>=1.12.0

# HTTP requests - <PERSON><PERSON><PERSON><PERSON><PERSON>
requests

# Environment variables - <PERSON><PERSON><PERSON><PERSON><PERSON>
python-dotenv

# WebSocket support - <PERSON><PERSON><PERSON><PERSON><PERSON>
websockets

# Windows compatibility - MA<PERSON><PERSON><PERSON><PERSON>
pywin32; sys_platform == "win32"
