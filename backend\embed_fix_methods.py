"""
COMPREHENSIVE FIX FOR EMBED CODE EXTRACTION

This file contains the corrected methods that should replace the existing ones
in video_hosts.py to fix all the issues identified:

1. Time range filtering not working
2. Empty file codes in embed URLs  
3. Duplicate entries
4. Template codes instead of real codes
"""

# REPLACE THE EXISTING _list_filemoon_files METHOD WITH THIS:

async def _list_filemoon_files(self, hours: int) -> List[Dict[str, Any]]:
    """List recent FileMoon files with PROPER TIME FILTERING and validation"""
    try:
        api_key = self.api_keys['filemoon']
        all_files = []
        page = 1
        per_page = 50
        max_pages = 20  # Increased to get more files

        # Calculate cutoff time for filtering
        cutoff_time = datetime.now() - timedelta(hours=hours)
        LOGGER.info(f"FileMoon: Starting file listing for last {hours} hours (since {cutoff_time})")

        while page <= max_pages:
            url = f"{self.base_urls['filemoon']}/api/file/list"
            params = {
                'key': api_key,
                'per_page': per_page,
                'page': page
            }

            try:
                if page > 1:
                    await asyncio.sleep(1)

                async with self.session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract files from FileMoon response
                        files_on_page = []
                        if isinstance(data, dict) and 'result' in data:
                            result = data['result']
                            if isinstance(result, dict) and 'files' in result:
                                files_on_page = result['files']

                        if not files_on_page:
                            LOGGER.info(f"FileMoon: No more files on page {page}")
                            break

                        files_added_this_page = 0
                        for file_info in files_on_page:
                            if not isinstance(file_info, dict):
                                continue

                            # Extract file code - FileMoon uses 'file_code'
                            file_code = file_info.get('file_code', '').strip()

                            # Strict validation for file codes
                            if not file_code or len(file_code) < 3:
                                LOGGER.warning(f"FileMoon: Invalid file_code '{file_code}'")
                                continue

                            # Extract filename
                            filename = file_info.get('title', '').strip()
                            if not filename:
                                LOGGER.warning(f"FileMoon: Empty filename for file_code {file_code}")
                                continue

                            # Get upload date and apply time filtering
                            upload_date = file_info.get('uploaded', '')
                            
                            # Apply time filtering if not "all time" request
                            if hours < 999999:  # Not "all time"
                                if not self._is_file_recent_by_date(upload_date, cutoff_time):
                                    LOGGER.debug(f"FileMoon: Skipping old file {filename}")
                                    continue

                            all_files.append({
                                'filename': filename,
                                'file_code': file_code,
                                'upload_date': upload_date,
                                'host': 'filemoon'
                            })
                            files_added_this_page += 1

                        LOGGER.info(f"FileMoon page {page}: Added {files_added_this_page}/{len(files_on_page)} files")

                        if len(files_on_page) < per_page:
                            break
                        page += 1
                        
                    elif response.status == 429:
                        LOGGER.warning(f"FileMoon: Rate limited, waiting 5 seconds")
                        await asyncio.sleep(5)
                        continue
                    else:
                        error_text = await response.text()
                        LOGGER.error(f"FileMoon API error {response.status}: {error_text}")
                        break
                        
            except Exception as e:
                LOGGER.error(f"FileMoon: Error on page {page}: {e}")
                break

        LOGGER.info(f"FileMoon: Retrieved {len(all_files)} files (time filtered)")
        return all_files

    except Exception as e:
        LOGGER.error(f"FileMoon file list failed: {str(e)}")
        return []


# REPLACE THE EXISTING _list_p2p_files METHOD WITH THIS:

async def _list_p2p_files(self, host: str, hours: int) -> List[Dict[str, Any]]:
    """List recent P2P host files with PROPER TIME FILTERING and validation"""
    try:
        api_key = self.api_keys[host]
        url = self._build_p2p_api_url(host, 'video/manage')
        headers = {
            'api-token': api_key,
            'Content-Type': 'application/json',
            'User-Agent': 'AutoUploadBot/1.0'
        }

        all_files = []
        page = 1
        per_page = 50
        max_pages = 20

        # Calculate cutoff time for filtering
        cutoff_time = datetime.now() - timedelta(hours=hours)
        LOGGER.info(f"{host}: Starting file listing for last {hours} hours (since {cutoff_time})")

        while page <= max_pages:
            params = {
                'perPage': per_page,
                'page': page
            }

            try:
                if page > 1:
                    await asyncio.sleep(2)

                async with self.session.get(url, headers=headers, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Extract videos from P2P response - they use 'data' key
                        videos_on_page = []
                        if isinstance(data, dict) and 'data' in data:
                            videos_on_page = data['data']

                        if not videos_on_page:
                            LOGGER.info(f"{host}: No more videos on page {page}")
                            break

                        videos_added_this_page = 0
                        for video in videos_on_page:
                            if not isinstance(video, dict):
                                continue

                            # Extract file ID - P2P hosts use 'id'
                            file_id = video.get('id', '').strip()
                            
                            # Strict validation for file IDs
                            if not file_id or len(file_id) < 3:
                                LOGGER.warning(f"{host}: Invalid file_id '{file_id}'")
                                continue

                            # Extract filename - P2P hosts use 'name'
                            filename = video.get('name', '').strip()
                            if not filename:
                                LOGGER.warning(f"{host}: Empty filename for file_id {file_id}")
                                continue

                            # Get upload date and apply time filtering
                            upload_date = video.get('createdAt', '')
                            
                            # Apply time filtering if not "all time" request
                            if hours < 999999:  # Not "all time"
                                if not self._is_file_recent_by_date(upload_date, cutoff_time):
                                    LOGGER.debug(f"{host}: Skipping old file {filename}")
                                    continue

                            all_files.append({
                                'filename': filename,
                                'file_code': file_id,
                                'upload_date': upload_date,
                                'host': host
                            })
                            videos_added_this_page += 1

                        LOGGER.info(f"{host} page {page}: Added {videos_added_this_page}/{len(videos_on_page)} videos")

                        if len(videos_on_page) < per_page:
                            break
                        page += 1
                        
                    elif response.status == 429:
                        LOGGER.warning(f"{host}: Rate limited, waiting 5 seconds")
                        await asyncio.sleep(5)
                        continue
                    else:
                        error_text = await response.text()
                        LOGGER.error(f"{host}: API error {response.status}: {error_text}")
                        break
                        
            except Exception as e:
                LOGGER.error(f"{host}: Error on page {page}: {e}")
                break

        LOGGER.info(f"{host}: Retrieved {len(all_files)} files (time filtered)")
        return all_files

    except Exception as e:
        LOGGER.error(f"{host} file list failed: {str(e)}")
        return []


# REPLACE THE EXISTING extract_embed_codes METHOD WITH THIS:

async def extract_embed_codes(self, hours: int = 48) -> List[Dict[str, Any]]:
    """Extract REAL embed codes with proper validation and duplicate removal"""
    try:
        LOGGER.info(f"Starting embed code extraction for last {hours} hours")
        all_files = []
        
        # Get files from all hosts
        for host in self.api_keys.keys():
            if not self.api_keys[host]:
                LOGGER.warning(f"Skipping {host} - no API key configured")
                continue
                
            try:
                LOGGER.info(f"Fetching files from {host}...")
                host_files = await self.list_recent_uploads(host, hours)
                LOGGER.info(f"{host}: Found {len(host_files)} files")
                
                # Validate files before adding
                valid_files = []
                for file_info in host_files:
                    filename = file_info.get('filename', '').strip()
                    file_code = file_info.get('file_code', '').strip()
                    
                    # Strict validation
                    if not filename or not file_code or len(file_code) < 3:
                        LOGGER.warning(f"{host}: Skipping invalid file - filename: '{filename}', file_code: '{file_code}'")
                        continue
                        
                    valid_files.append(file_info)
                
                LOGGER.info(f"{host}: {len(valid_files)} valid files after validation")
                all_files.extend(valid_files)
                
                await asyncio.sleep(1)  # Rate limiting
                
            except Exception as e:
                LOGGER.error(f"Failed to get files from {host}: {e}")
                continue

        LOGGER.info(f"Total valid files collected: {len(all_files)}")

        # IMPROVED DUPLICATE REMOVAL
        # Group files by normalized filename to better detect duplicates
        embed_data = []
        files_by_normalized_name = {}
        
        for file_info in all_files:
            filename = file_info.get('filename', '').strip()
            host = file_info['host']
            file_code = file_info.get('file_code', '').strip()
            
            # Normalize filename for duplicate detection
            normalized_name = filename.lower()
            # Remove common video extensions
            for ext in ['.mkv', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v']:
                if normalized_name.endswith(ext):
                    normalized_name = normalized_name[:-len(ext)]
                    break
            
            # Remove spaces, dots, underscores for better matching
            normalized_name = normalized_name.replace(' ', '').replace('.', '').replace('_', '')
            
            if normalized_name not in files_by_normalized_name:
                files_by_normalized_name[normalized_name] = {
                    'filename': filename,  # Use first occurrence's filename
                    'upload_date': file_info.get('upload_date', ''),
                    'embed_codes': {},
                    'hosts_seen': set()
                }

            # Only add if this host hasn't been processed for this file yet
            if host not in files_by_normalized_name[normalized_name]['hosts_seen']:
                # Generate embed code with validation
                embed_code = self._generate_embed_code(host, file_code, filename)
                if embed_code and file_code not in embed_code:  # Ensure file_code is in the embed
                    LOGGER.error(f"Generated invalid embed for {host} {filename}: {embed_code}")
                    continue
                    
                if embed_code:
                    files_by_normalized_name[normalized_name]['embed_codes'][host] = embed_code
                    files_by_normalized_name[normalized_name]['hosts_seen'].add(host)
                    LOGGER.info(f"Generated embed code for '{filename}' on {host}: {file_code}")

        # Convert to final format
        for file_data in files_by_normalized_name.values():
            file_data.pop('hosts_seen', None)  # Remove internal tracking
            
            # Only include files that have at least one valid embed code
            if file_data['embed_codes']:
                embed_data.append(file_data)

        # Sort by filename for consistent output
        embed_data.sort(key=lambda x: x['filename'].lower())
        
        LOGGER.info(f"Generated embed codes for {len(embed_data)} unique files")
        return embed_data

    except Exception as e:
        LOGGER.error(f"Extract embed codes failed: {str(e)}")
        return []