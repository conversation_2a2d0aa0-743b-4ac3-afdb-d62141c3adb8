# AutoUploadBot Backend

Backend API server for the AutoUploadBot following mirror-leech-telegram-bot patterns.

## Features

- **Download Management**: Aria2-based downloads for URLs and magnet links
- **Google Drive Integration**: Upload and manage files on Google Drive
- **Video Host Uploads**: Sequential uploads to StreamP2P, RPMShare, UPnShare, and Filemoon
- **Archive Extraction**: Automatic extraction of video files from archives
- **Progress Monitoring**: Real-time progress tracking via WebSocket
- **Duplicate Detection**: Check for duplicate files across platforms
- **Embed Code Generation**: Extract embed codes for uploaded videos
- **Drive Cleanup**: Manage and clean Google Drive storage

## Prerequisites

### 1. Python Requirements
- Python 3.8 or higher
- pip package manager

### 2. Aria2 Setup
Run the setup script to download and configure Aria2:
```bash
python setup_aria2.py
```

Then start Aria2:
```bash
start_aria2.bat
```

### 3. Google Drive API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google Drive API
4. Create Service Account credentials
5. Download the JSON key file
6. Create `accounts/` directory and place JSON files there
7. Share your Google Drive folders with the service account email

### 4. 7-Zip Installation
Install 7-Zip for archive extraction:
- Download from: https://www.7-zip.org/
- Add to system PATH

## Installation

1. **Clone/Navigate to backend directory**:
```bash
cd backend
```

2. **Install Python dependencies**:
```bash
pip install -r requirements.txt
```

3. **Setup environment variables**:
```bash
copy .env.example .env
```

4. **Edit `.env` file with your configuration**:
```env
# Google Drive Configuration
GDRIVE_PRIMARY_FOLDER_ID=your_primary_folder_id
GDRIVE_SECONDARY_FOLDER_ID=your_secondary_folder_id

# Video Hosting API Keys
STREAMP2P_API_KEY=your_streamp2p_api_key
RPMSHARE_API_KEY=your_rpmshare_api_key
UPNSHARE_API_KEY=your_upnshare_api_key
FILEMOON_API_KEY=your_filemoon_api_key
```

5. **Create required directories**:
```bash
mkdir accounts downloads
```

6. **Place Google Drive service account JSON files in `accounts/` directory**

## Running the Server

1. **Start Aria2** (in separate terminal):
```bash
start_aria2.bat
```

2. **Start the backend server**:
```bash
python -m uvicorn main:app --host localhost --port 8001 --reload
```

The server will be available at: http://localhost:8001

## API Documentation

Once the server is running, visit:
- **API Docs**: http://localhost:8001/docs
- **Health Check**: http://localhost:8001/health

## Key Endpoints

### Downloads
- `POST /api/download/url` - Download from URLs
- `POST /api/download/magnet` - Download from magnet links
- `GET /api/downloads/status` - Get download status

### Uploads
- `POST /api/upload/gdrive` - Upload to Google Drive
- `POST /api/upload/video-hosts` - Upload to video hosting platforms

### File Management
- `POST /api/extract/archive` - Extract archive files
- `GET /api/gdrive/list/{drive_type}` - List Google Drive files
- `DELETE /api/gdrive/cleanup` - Clean up Google Drive

### Utilities
- `POST /api/duplicate/check` - Check for duplicates
- `POST /api/embed/extract` - Extract embed codes
- `GET /api/embed/download-csv` - Download embed codes CSV

### Real-time Updates
- `WebSocket /ws` - Real-time progress updates

## Architecture

The backend follows the proven patterns from mirror-leech-telegram-bot:

### Core Components
- **Aria2Manager**: Handles downloads using aioaria2 WebSocket client
- **GoogleDriveHelper**: Manages Google Drive operations with service accounts
- **VideoHostManager**: Handles uploads to video hosting platforms
- **File Utilities**: Archive extraction and file management

### Key Features
- **Retry Logic**: Uses tenacity for robust error handling
- **Progress Tracking**: Real-time progress monitoring
- **Service Account Rotation**: Automatic switching for Google Drive
- **Sequential Uploads**: Prevents rate limiting on video hosts
- **WebSocket Updates**: Real-time status updates to frontend

## Configuration

### Environment Variables
See `.env.example` for all available configuration options.

### Google Drive Setup
1. Create two Google Drive folders (Primary and Secondary)
2. Share both folders with your service account email
3. Copy the folder IDs from the URLs
4. Add them to your `.env` file

### Video Host APIs
Obtain API keys from:
- StreamP2P: https://streamp2p.com/
- RPMShare: https://rpmshare.com/
- UPnShare: https://upnshare.com/
- Filemoon: https://filemoon.sx/

## Troubleshooting

### Common Issues

1. **Aria2 Connection Failed**
   - Ensure Aria2 is running: `start_aria2.bat`
   - Check port 6800 is not blocked

2. **Google Drive Authorization Failed**
   - Verify service account JSON files are in `accounts/` directory
   - Check folder IDs are correct
   - Ensure folders are shared with service account

3. **Video Host Upload Failed**
   - Verify API keys are correct
   - Check rate limiting (use sequential uploads)
   - Ensure file URLs are accessible

4. **Archive Extraction Failed**
   - Install 7-Zip and add to PATH
   - Check file permissions

### Logs
Check console output for detailed error messages and debugging information.

## Development

### Adding New Features
1. Follow the existing patterns from mirror-leech-telegram-bot
2. Use tenacity for retry logic
3. Implement proper error handling
4. Add logging for debugging

### Testing
Run the server in development mode:
```bash
python -m uvicorn main:app --host localhost --port 8001 --reload
```

## Security Notes

- Keep your `.env` file secure and never commit it
- Service account JSON files contain sensitive credentials
- API keys should be kept confidential
- Use HTTPS in production environments
