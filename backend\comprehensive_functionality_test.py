#!/usr/bin/env python3
"""
Comprehensive Functionality Test for AutoUploadBot
Tests all major features end-to-end
"""
import asyncio
import aiohttp
import logging
import json
import time
from typing import Dict, List

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8001"

class ComprehensiveTester:
    """Comprehensive functionality tester"""
    
    def __init__(self):
        self.session = None
        self.test_results = {}
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_api_health(self) -> bool:
        """Test API health check"""
        try:
            LOGGER.info("🔍 Testing API Health...")
            async with self.session.get(f"{API_BASE_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ API Health: {data}")
                    return True
                else:
                    LOGGER.error(f"❌ API Health failed: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ API Health error: {e}")
            return False
    
    async def test_download_url(self) -> bool:
        """Test URL download functionality"""
        try:
            LOGGER.info("📥 Testing URL Download...")

            # Test with a small file
            test_url = "https://httpbin.org/uuid"
            payload = {"urls": [test_url]}

            async with self.session.post(
                f"{API_BASE_URL}/api/download/url",
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ URL Download started: {data}")
                    
                    # Wait for completion
                    gid = data.get("downloads", [{}])[0].get("gid")
                    if gid:
                        await self._wait_for_download_completion(gid)
                    return True
                else:
                    LOGGER.error(f"❌ URL Download failed: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ URL Download error: {e}")
            return False
    
    async def test_video_host_uploads(self) -> bool:
        """Test video hosting platform uploads"""
        try:
            LOGGER.info("📤 Testing Video Host Uploads...")
            
            # Test with a small video file URL
            test_video_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
            
            platforms = ["streamp2p", "rpmshare", "upnshare", "filemoon"]
            
            for platform in platforms:
                LOGGER.info(f"Testing {platform} upload...")
                payload = {
                    "file_url": test_video_url,
                    "filename": f"test_video_{platform}.mp4"
                }
                
                async with self.session.post(
                    f"{API_BASE_URL}/api/upload/video-hosts",
                    json=payload
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        LOGGER.info(f"✅ {platform} upload started: {data}")
                    else:
                        LOGGER.warning(f"⚠️ {platform} upload failed: {response.status}")
            
            return True
        except Exception as e:
            LOGGER.error(f"❌ Video Host Uploads error: {e}")
            return False
    
    async def test_gdrive_operations(self) -> bool:
        """Test Google Drive operations"""
        try:
            LOGGER.info("💾 Testing Google Drive Operations...")
            
            # Test listing files
            async with self.session.get(f"{API_BASE_URL}/api/gdrive/list/primary") as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ GDrive file listing: {len(data.get('files', []))} files found")
                    return True
                else:
                    LOGGER.error(f"❌ GDrive operations failed: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ GDrive operations error: {e}")
            return False
    
    async def test_duplicate_checker(self) -> bool:
        """Test duplicate file checker"""
        try:
            LOGGER.info("🔍 Testing Duplicate Checker...")
            
            async with self.session.post(f"{API_BASE_URL}/api/duplicate/check", json={}) as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Duplicate check completed: {data}")
                    return True
                else:
                    LOGGER.error(f"❌ Duplicate checker failed: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ Duplicate checker error: {e}")
            return False
    
    async def test_embed_extractor(self) -> bool:
        """Test embed code extractor"""
        try:
            LOGGER.info("🔗 Testing Embed Extractor...")
            
            async with self.session.post(f"{API_BASE_URL}/api/embed/extract", json={}) as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Embed extraction completed: {data}")
                    return True
                else:
                    LOGGER.error(f"❌ Embed extractor failed: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ Embed extractor error: {e}")
            return False
    
    async def _wait_for_download_completion(self, gid: str, timeout: int = 60):
        """Wait for download to complete"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                async with self.session.get(f"{API_BASE_URL}/api/downloads/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        downloads = data.get("downloads", [])
                        
                        for download in downloads:
                            if download.get("gid") == gid:
                                status = download.get("status")
                                progress = download.get("progress", 0)
                                LOGGER.info(f"Download {gid}: {status} - {progress}%")
                                
                                if status in ["complete", "error"]:
                                    return status == "complete"
                
                await asyncio.sleep(2)
            except Exception as e:
                LOGGER.error(f"Error checking download status: {e}")
                break
        
        return False
    
    async def run_comprehensive_test(self):
        """Run all tests"""
        LOGGER.info("🚀 Starting Comprehensive Functionality Test")
        
        tests = [
            ("API Health", self.test_api_health),
            ("URL Download", self.test_download_url),
            ("Video Host Uploads", self.test_video_host_uploads),
            ("Google Drive Operations", self.test_gdrive_operations),
            ("Duplicate Checker", self.test_duplicate_checker),
            ("Embed Extractor", self.test_embed_extractor),
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            LOGGER.info(f"\n{'='*50}")
            LOGGER.info(f"Running: {test_name}")
            LOGGER.info(f"{'='*50}")
            
            try:
                result = await test_func()
                results[test_name] = result
                status = "✅ PASSED" if result else "❌ FAILED"
                LOGGER.info(f"{test_name}: {status}")
            except Exception as e:
                results[test_name] = False
                LOGGER.error(f"{test_name}: ❌ FAILED - {e}")
        
        # Summary
        LOGGER.info(f"\n{'='*50}")
        LOGGER.info("📊 TEST SUMMARY")
        LOGGER.info(f"{'='*50}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            LOGGER.info(f"{test_name}: {status}")
        
        LOGGER.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            LOGGER.info("🎉 ALL TESTS PASSED! Application is fully functional!")
        else:
            LOGGER.warning(f"⚠️ {total - passed} tests failed. Check logs for details.")
        
        return results


async def main():
    """Main test runner"""
    async with ComprehensiveTester() as tester:
        await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
