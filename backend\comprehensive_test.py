#!/usr/bin/env python3
"""
COMPREHENSIVE VERIFICATION AND TEST SYSTEM
Tests all components of the AutoUploadBot with FULL RCLONE IMPLEMENTATION
"""
import asyncio
import aiohttp
import subprocess
import os
from pathlib import Path

class ComprehensiveTestSuite:
    def __init__(self):
        self.results = {}
        self.backend_url = "http://localhost:8001"
        self.frontend_url = "http://localhost:8082"
        
    async def run_all_tests(self):
        """Run comprehensive test suite"""
        print("STARTING COMPREHENSIVE VERIFICATION AND TESTING")
        print("=" * 60)
        
        # Test 1: Backend Health
        await self.test_backend_health()
        
        # Test 2: RClone Configuration
        await self.test_rclone_configuration()
        
        # Test 3: Google Drive Access
        await self.test_google_drive_access()
        
        # Test 4: Aria2 Connection
        await self.test_aria2_connection()
        
        # Test 5: API Endpoints
        await self.test_api_endpoints()
        
        # Test 6: Frontend Connectivity
        await self.test_frontend_connectivity()
        
        # Test 7: CORS Configuration
        await self.test_cors_configuration()
        
        # Generate comprehensive report
        self.generate_report()
        
    async def test_backend_health(self):
        """Test backend health and initialization"""
        print("\nTesting Backend Health...")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.backend_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        self.results["backend_health"] = {
                            "status": "✅ PASS",
                            "details": data
                        }
                        print(f"✅ Backend Health: {data}")
                    else:
                        self.results["backend_health"] = {
                            "status": "❌ FAIL",
                            "details": f"HTTP {response.status}"
                        }
        except Exception as e:
            self.results["backend_health"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ Backend Health Failed: {e}")
    
    async def test_rclone_configuration(self):
        """Test RClone installation and configuration"""
        print("\n🔍 Testing RClone Configuration...")
        try:
            rclone_path = Path("rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
            
            if not rclone_path.exists():
                self.results["rclone_config"] = {
                    "status": "❌ FAIL",
                    "details": "RClone executable not found"
                }
                return
            
            # Test rclone version
            result = subprocess.run([str(rclone_path), "version"], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # Test rclone remotes
                result2 = subprocess.run([str(rclone_path), "listremotes"], 
                                       capture_output=True, text=True, timeout=10)
                
                if "gdrive:" in result2.stdout:
                    self.results["rclone_config"] = {
                        "status": "✅ PASS",
                        "details": {
                            "version": result.stdout.split('\n')[0],
                            "remotes": result2.stdout.strip().split('\n')
                        }
                    }
                    print(f"✅ RClone Configuration: {result.stdout.split()[1]}")
                else:
                    self.results["rclone_config"] = {
                        "status": "⚠️ PARTIAL",
                        "details": "RClone installed but gdrive remote not configured"
                    }
            else:
                self.results["rclone_config"] = {
                    "status": "❌ FAIL",
                    "details": result.stderr
                }
                
        except Exception as e:
            self.results["rclone_config"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ RClone Configuration Failed: {e}")
    
    async def test_google_drive_access(self):
        """Test Google Drive access via service account"""
        print("\n🔍 Testing Google Drive Access...")
        try:
            from core.gdrive_direct_manager import GoogleDriveDirectManager
            
            gdrive_manager = GoogleDriveDirectManager()
            
            # Test basic Google Drive connection
            # This will test if service account can authenticate
            service = gdrive_manager.service
            
            if service:
                self.results["gdrive_access"] = {
                    "status": "✅ PASS",
                    "details": "Service account authentication successful"
                }
                print("✅ Google Drive Access: Service account authenticated")
            else:
                self.results["gdrive_access"] = {
                    "status": "❌ FAIL",
                    "details": "Service account authentication failed"
                }
                
        except Exception as e:
            self.results["gdrive_access"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ Google Drive Access Failed: {e}")
    
    async def test_aria2_connection(self):
        """Test Aria2 RPC connection"""
        print("\n🔍 Testing Aria2 Connection...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test Aria2 RPC endpoint
                rpc_data = {
                    "jsonrpc": "2.0",
                    "id": "test",
                    "method": "aria2.getVersion"
                }
                
                async with session.post("http://localhost:6800/jsonrpc", 
                                       json=rpc_data) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "result" in data:
                            self.results["aria2_connection"] = {
                                "status": "✅ PASS",
                                "details": data["result"]
                            }
                            print(f"✅ Aria2 Connection: {data['result']['version']}")
                        else:
                            self.results["aria2_connection"] = {
                                "status": "❌ FAIL",
                                "details": data
                            }
                    else:
                        self.results["aria2_connection"] = {
                            "status": "❌ FAIL",
                            "details": f"HTTP {response.status}"
                        }
                        
        except Exception as e:
            self.results["aria2_connection"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ Aria2 Connection Failed: {e}")
    
    async def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n🔍 Testing API Endpoints...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test download status endpoint
                async with session.get(f"{self.backend_url}/api/downloads/status") as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Test URL download endpoint
                        url_data = {"urls": ["https://httpbin.org/json"]}
                        async with session.post(f"{self.backend_url}/api/download/url", 
                                              json=url_data) as url_response:
                            url_status = url_response.status
                            url_text = await url_response.text()
                            
                        # Test magnet download endpoint
                        magnet_data = {"magnets": ["magnet:?xt=urn:btih:test"]}
                        async with session.post(f"{self.backend_url}/api/download/magnet", 
                                              json=magnet_data) as magnet_response:
                            magnet_status = magnet_response.status
                            magnet_text = await magnet_response.text()
                        
                        self.results["api_endpoints"] = {
                            "status": "✅ PASS" if url_status in [200, 500] and magnet_status in [200, 500] else "❌ FAIL",
                            "details": {
                                "downloads_status": response.status,
                                "url_download": {"status": url_status, "response": url_text[:100]},
                                "magnet_download": {"status": magnet_status, "response": magnet_text[:100]}
                            }
                        }
                        print(f"✅ API Endpoints: Status={response.status}, URL={url_status}, Magnet={magnet_status}")
                    else:
                        self.results["api_endpoints"] = {
                            "status": "❌ FAIL",
                            "details": f"Downloads status endpoint failed: HTTP {response.status}"
                        }
                        
        except Exception as e:
            self.results["api_endpoints"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ API Endpoints Failed: {e}")
    
    async def test_frontend_connectivity(self):
        """Test frontend connectivity"""
        print("\n🔍 Testing Frontend Connectivity...")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.frontend_url) as response:
                    if response.status == 200:
                        self.results["frontend_connectivity"] = {
                            "status": "✅ PASS",
                            "details": f"Frontend accessible at {self.frontend_url}"
                        }
                        print(f"✅ Frontend Connectivity: Accessible at {self.frontend_url}")
                    else:
                        self.results["frontend_connectivity"] = {
                            "status": "❌ FAIL",
                            "details": f"HTTP {response.status}"
                        }
                        
        except Exception as e:
            self.results["frontend_connectivity"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ Frontend Connectivity Failed: {e}")
    
    async def test_cors_configuration(self):
        """Test CORS configuration"""
        print("\n🔍 Testing CORS Configuration...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test preflight request
                headers = {
                    "Origin": self.frontend_url,
                    "Access-Control-Request-Method": "POST",
                    "Access-Control-Request-Headers": "Content-Type"
                }
                
                async with session.options(f"{self.backend_url}/api/download/url", 
                                         headers=headers) as response:
                    cors_headers = {
                        "access-control-allow-origin": response.headers.get("Access-Control-Allow-Origin"),
                        "access-control-allow-methods": response.headers.get("Access-Control-Allow-Methods"),
                        "access-control-allow-headers": response.headers.get("Access-Control-Allow-Headers")
                    }
                    
                    if response.status in [200, 204] and cors_headers["access-control-allow-origin"]:
                        self.results["cors_configuration"] = {
                            "status": "✅ PASS",
                            "details": cors_headers
                        }
                        print("✅ CORS Configuration: Properly configured")
                    else:
                        self.results["cors_configuration"] = {
                            "status": "❌ FAIL",
                            "details": f"HTTP {response.status}, Headers: {cors_headers}"
                        }
                        
        except Exception as e:
            self.results["cors_configuration"] = {
                "status": "❌ FAIL",
                "details": str(e)
            }
            print(f"❌ CORS Configuration Failed: {e}")
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results.values() if r["status"].startswith("✅")])
        partial_tests = len([r for r in self.results.values() if r["status"].startswith("⚠️")])
        failed_tests = len([r for r in self.results.values() if r["status"].startswith("❌")])
        
        print(f"📈 SUMMARY: {passed_tests}/{total_tests} PASSED, {partial_tests} PARTIAL, {failed_tests} FAILED")
        print()
        
        for test_name, result in self.results.items():
            print(f"{result['status']} {test_name.upper().replace('_', ' ')}")
            if isinstance(result['details'], dict):
                for key, value in result['details'].items():
                    print(f"   {key}: {value}")
            else:
                print(f"   {result['details']}")
            print()
        
        # Overall status
        if failed_tests == 0 and partial_tests == 0:
            print("🎉 ALL SYSTEMS FULLY FUNCTIONAL!")
        elif failed_tests == 0:
            print("⚠️ SYSTEMS MOSTLY FUNCTIONAL WITH MINOR ISSUES")
        else:
            print("❌ CRITICAL ISSUES DETECTED - REQUIRES ATTENTION")
        
        print("=" * 60)

async def main():
    """Run comprehensive test suite"""
    test_suite = ComprehensiveTestSuite()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
