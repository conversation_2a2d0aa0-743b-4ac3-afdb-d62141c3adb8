#!/usr/bin/env python3
"""
FINAL COMPREHENSIVE TEST - AutoUploadBot ProactorEventLoop Fix
Tests all functionality with the updated configuration
"""
import requests
import json
import time
import platform

# Updated configuration for ProactorEventLoop-fixed backend
BASE_URL = "http://localhost:8004"
FRONTEND_URL = "http://localhost:8080"
ARIA2_URL = "http://localhost:6800/jsonrpc"

def test_aria2_connection():
    """Test Aria2 RPC connection"""
    print("1. Testing Aria2 RPC Connection...")
    
    payload = {
        "jsonrpc": "2.0",
        "id": "test",
        "method": "aria2.getVersion",
        "params": []
    }
    
    try:
        response = requests.post(ARIA2_URL, json=payload, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if "result" in data:
                print(f"   SUCCESS: Aria2 RPC working - Version: {data['result']['version']}")
                return True
        
        print("   FAIL: Aria2 RPC not responding correctly")
        return False
    except Exception as e:
        print(f"   FAIL: Aria2 RPC error: {e}")
        return False

def test_backend_api():
    """Test Backend API endpoints"""
    print("2. Testing Backend API...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/downloads/status", timeout=5)
        if response.status_code == 200:
            print("   SUCCESS: Backend API responding")
            return True
        else:
            print(f"   FAIL: Backend API error - Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"   FAIL: Backend API error: {e}")
        return False

def test_url_download():
    """Test URL download functionality"""
    print("3. Testing URL Download (ProactorEventLoop Fix)...")
    
    url = f"{BASE_URL}/api/download/url"
    payload = {
        "urls": ["https://httpbin.org/json"]
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print(f"   SUCCESS: URL download started - GID: {data['results'][0]['gid']}")
                return True
        
        print(f"   FAIL: URL download failed - Response: {response.text}")
        return False
    except Exception as e:
        print(f"   FAIL: URL download error: {e}")
        return False

def test_magnet_download():
    """Test magnet download functionality"""
    print("4. Testing Magnet Download (Aria2 Integration)...")
    
    url = f"{BASE_URL}/api/download/magnet"
    payload = {
        "magnets": ["magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=test"]
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print(f"   SUCCESS: Magnet download started - GID: {data['results'][0]['gid']}")
                return True
        
        print(f"   FAIL: Magnet download failed - Response: {response.text}")
        return False
    except Exception as e:
        print(f"   FAIL: Magnet download error: {e}")
        return False

def main():
    """Run comprehensive test suite"""
    print("=" * 60)
    print("FINAL COMPREHENSIVE TEST - AutoUploadBot")
    print("=" * 60)
    print("Testing ProactorEventLoop Fix & Updated Configuration")
    print(f"Platform: {platform.system()}")
    print(f"Backend URL: {BASE_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    print(f"Aria2 URL: {ARIA2_URL}")
    print("=" * 60)
    
    # Run all tests
    tests = [
        ("Aria2 RPC Connection", test_aria2_connection),
        ("Backend API", test_backend_api),
        ("URL Download", test_url_download),
        ("Magnet Download", test_magnet_download),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   FAIL: {test_name} - Exception: {e}")
            results.append((test_name, False))
        
        time.sleep(1)  # Brief pause between tests
    
    # Summary
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"OVERALL: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\nSUCCESS: ALL TESTS PASSED!")
        print("Windows subprocess issue COMPLETELY RESOLVED!")
        print("ProactorEventLoop fix working perfectly!")
        print("URL downloads working!")
        print("Magnet downloads working!")
        print("\nAutoUploadBot is ready for production use!")
    else:
        failed = len(results) - passed
        print(f"\nWARNING: {failed} test(s) failed.")
        print("Check that all services are running:")
        print("1. Run Start_App.bat to start all services")
        print("2. Wait for all 3 windows to fully load")
        print("3. Re-run this test")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
