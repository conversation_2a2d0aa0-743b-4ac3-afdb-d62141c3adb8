#!/usr/bin/env python3
"""
Test script to verify time filtering is working correctly
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.video_hosts import VideoHostManager

async def test_time_filtering():
    """Test time filtering with different hour ranges"""
    print("=" * 60)
    print("TESTING TIME FILTERING FUNCTIONALITY")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test different time ranges
        test_ranges = [24, 48, 72, 168]  # 1 day, 2 days, 3 days, 1 week
        
        for hours in test_ranges:
            print(f"\n{'-' * 40}")
            print(f"TESTING {hours} HOUR TIME RANGE")
            print(f"{'-' * 40}")
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            print(f"Cutoff time ({hours}h ago): {cutoff_time}")
            
            # Get embed data for this time range
            embed_data = await vm.extract_embed_codes(hours)
            
            print(f"Files found in last {hours} hours: {len(embed_data)}")
            
            # Show sample files with their upload dates
            for i, item in enumerate(embed_data[:3]):  # Show first 3 files
                filename = item.get('filename', 'Unknown')
                upload_date = item.get('upload_date', 'Unknown')
                embed_codes = item.get('embed_codes', {})
                hosts_count = len([host for host, code in embed_codes.items() if code and code.strip()])
                
                print(f"  {i+1}. {filename[:50]}...")
                print(f"     Upload date: {upload_date}")
                print(f"     Hosts with embed codes: {hosts_count}")
            
            if len(embed_data) > 3:
                print(f"  ... and {len(embed_data) - 3} more files")
        
        # Test edge case: very short time range (should return fewer or no files)
        print(f"\n{'-' * 40}")
        print("TESTING EDGE CASE: 1 HOUR TIME RANGE")
        print(f"{'-' * 40}")
        
        embed_data_1h = await vm.extract_embed_codes(1)
        print(f"Files found in last 1 hour: {len(embed_data_1h)}")
        
        # Test edge case: very long time range (should return more files)
        print(f"\n{'-' * 40}")
        print("TESTING EDGE CASE: ALL TIME (999999 HOURS)")
        print(f"{'-' * 40}")
        
        embed_data_all = await vm.extract_embed_codes(999999)
        print(f"Files found in all time: {len(embed_data_all)}")
        
        # Verify time filtering logic
        print(f"\n{'-' * 40}")
        print("VERIFICATION SUMMARY")
        print(f"{'-' * 40}")
        
        print("Time range progression (should generally increase or stay same):")
        for hours in test_ranges:
            embed_data = await vm.extract_embed_codes(hours)
            print(f"  {hours:3d} hours: {len(embed_data):2d} files")
        
        print(f"  1 hour:     {len(embed_data_1h):2d} files")
        print(f"  All time:   {len(embed_data_all):2d} files")
        
        # Check if time filtering is working logically
        embed_24h = await vm.extract_embed_codes(24)
        embed_48h = await vm.extract_embed_codes(48)
        
        if len(embed_48h) >= len(embed_24h):
            print("\n✅ Time filtering appears to be working correctly")
            print(f"   48h ({len(embed_48h)} files) >= 24h ({len(embed_24h)} files)")
        else:
            print("\n❌ Time filtering may have issues")
            print(f"   48h ({len(embed_48h)} files) < 24h ({len(embed_24h)} files)")
        
    finally:
        await vm.close()

if __name__ == "__main__":
    asyncio.run(test_time_filtering())
