# Test script to verify archive extraction works - MANDATORY CORE FEATURE
import os
import sys
import tempfile
import zipfile
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)

def test_archive_dependencies():
    """Test that all archive extraction dependencies are available"""
    print("Testing Archive Extraction Dependencies...")
    print("=" * 50)
    
    dependencies = [
        ("zipfile", "Built-in ZIP support"),
        ("py7zr", "7-Zip archive support"),
        ("rarfile", "RAR archive support"),
        ("tarfile", "TAR archive support"),
        ("gzip", "GZ compression support")
    ]
    
    failed_deps = []
    
    for dep_name, description in dependencies:
        try:
            __import__(dep_name)
            print(f"✅ {dep_name} - {description}")
        except ImportError as e:
            print(f"❌ {dep_name} - {description} - FAILED: {e}")
            failed_deps.append(dep_name)
    
    if failed_deps:
        print(f"\n🚨 CRITICAL ERROR: Missing dependencies: {failed_deps}")
        print("Archive extraction is a CORE FEATURE and must work!")
        return False
    else:
        print("\n✅ All archive extraction dependencies available!")
        return True

def create_test_zip():
    """Create a test ZIP file with video files"""
    print("\n🔧 Creating test ZIP file...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create test files
        test_files = [
            ("video1.mp4", b"fake video content 1"),
            ("video2.mkv", b"fake video content 2"),
            ("readme.txt", b"this is a readme file"),
            ("subfolder/video3.avi", b"fake video content 3")
        ]
        
        # Create directory structure
        os.makedirs(os.path.join(temp_dir, "subfolder"), exist_ok=True)
        
        # Create files
        for filename, content in test_files:
            filepath = os.path.join(temp_dir, filename)
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            with open(filepath, 'wb') as f:
                f.write(content)
        
        # Create ZIP file
        zip_path = os.path.join(temp_dir, "test_archive.zip")
        with zipfile.ZipFile(zip_path, 'w') as zip_ref:
            for filename, _ in test_files:
                file_path = os.path.join(temp_dir, filename)
                zip_ref.write(file_path, filename)
        
        print(f"✅ Created test ZIP: {zip_path}")
        return zip_path, test_files

def test_zip_extraction():
    """Test ZIP file extraction"""
    print("\n🧪 Testing ZIP Extraction...")
    
    try:
        zip_path, expected_files = create_test_zip()
        
        with tempfile.TemporaryDirectory() as extract_dir:
            # Extract ZIP
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Check extracted files
            extracted_files = []
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    rel_path = os.path.relpath(os.path.join(root, file), extract_dir)
                    extracted_files.append(rel_path.replace('\\', '/'))
            
            # Verify video files
            video_extensions = ['.mp4', '.mkv', '.avi', '.mov', '.wmv', '.flv', '.webm']
            video_files = [f for f in extracted_files if any(f.lower().endswith(ext) for ext in video_extensions)]
            
            print(f"✅ Extracted {len(extracted_files)} files")
            print(f"✅ Found {len(video_files)} video files: {video_files}")
            
            if len(video_files) >= 3:  # Should have 3 video files
                print("✅ ZIP extraction test PASSED!")
                return True
            else:
                print("❌ ZIP extraction test FAILED - not enough video files found!")
                return False
                
    except Exception as e:
        print(f"❌ ZIP extraction test FAILED: {e}")
        return False

def test_py7zr_availability():
    """Test py7zr functionality"""
    print("\n🧪 Testing py7zr (7-Zip) functionality...")
    
    try:
        import py7zr
        
        # Create a simple 7z archive for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test file
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test content")
            
            # Create 7z archive
            archive_path = os.path.join(temp_dir, "test.7z")
            with py7zr.SevenZipFile(archive_path, 'w') as archive:
                archive.write(test_file, "test.txt")
            
            # Extract 7z archive
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir)
            
            with py7zr.SevenZipFile(archive_path, 'r') as archive:
                archive.extractall(extract_dir)
            
            # Verify extraction
            extracted_file = os.path.join(extract_dir, "test.txt")
            if os.path.exists(extracted_file):
                print("✅ py7zr (7-Zip) test PASSED!")
                return True
            else:
                print("❌ py7zr (7-Zip) test FAILED - file not extracted!")
                return False
                
    except Exception as e:
        print(f"❌ py7zr (7-Zip) test FAILED: {e}")
        return False

def test_rarfile_availability():
    """Test rarfile functionality"""
    print("\n🧪 Testing rarfile (RAR) functionality...")
    
    try:
        import rarfile
        print("✅ rarfile module imported successfully!")
        
        # Note: We can't easily create RAR files for testing without WinRAR
        # But we can verify the module is available and configured
        
        # Check if RAR tool is available
        if rarfile.UNRAR_TOOL:
            print(f"✅ RAR extraction tool found: {rarfile.UNRAR_TOOL}")
        else:
            print("⚠️ RAR extraction tool not found - RAR files may not extract")
            print("   This is OK for now, other formats will work")
        
        return True
        
    except Exception as e:
        print(f"❌ rarfile test FAILED: {e}")
        return False

def main():
    """Main test function"""
    print("ARCHIVE EXTRACTION CORE FEATURE TEST")
    print("=" * 50)
    print("Testing all archive extraction capabilities...")
    
    tests = [
        ("Dependencies Check", test_archive_dependencies),
        ("ZIP Extraction", test_zip_extraction),
        ("7-Zip Support", test_py7zr_availability),
        ("RAR Support", test_rarfile_availability)
    ]
    
    passed_tests = 0
    failed_tests = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed_tests.append(test_name)
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed_tests.append(test_name)
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "="*50)
    print("ARCHIVE EXTRACTION TEST RESULTS")
    print("="*50)
    print(f"✅ Passed: {passed_tests}/{len(tests)}")
    
    if failed_tests:
        print(f"❌ Failed: {failed_tests}")
        print("\n🚨 CRITICAL: Archive extraction has issues!")
        print("This is a CORE FEATURE and must be fixed!")
        return False
    else:
        print("🎉 ALL ARCHIVE EXTRACTION TESTS PASSED!")
        print("Archive extraction CORE FEATURE is working correctly!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
