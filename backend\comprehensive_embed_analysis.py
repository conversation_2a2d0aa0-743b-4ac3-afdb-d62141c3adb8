#!/usr/bin/env python3
"""
COMPREHENSIVE EMBED CODE EXTRACTOR ANALYSIS
This script analyzes ALL issues with embed code extraction including:
1. ALL Time filter returning too many files (149 vs expected 107-110)
2. Confirms reliability for future calls
3. Verifies no duplicates, templates, or empty codes
4. Bulletproofs the time range filtering
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime, timedelta

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def analyze_all_time_filter():
    """Analyze what's causing extra files in ALL Time filter"""
    print("🔍 ANALYZING ALL TIME FILTER ISSUE")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Get files from each host individually for ALL time
        all_files_by_host = {}
        total_raw_files = 0
        
        for host in vm.api_keys.keys():
            if not vm.api_keys[host]:
                print(f"⚠️ Skipping {host} - no API key")
                continue
                
            print(f"\n📡 Analyzing {host} (ALL TIME)...")
            host_files = await vm.list_recent_uploads(host, 999999)  # All time
            all_files_by_host[host] = host_files
            total_raw_files += len(host_files)
            
            print(f"✅ {host}: {len(host_files)} total files")
            
            # Analyze file dates to understand time distribution
            dates_analysis = defaultdict(int)
            for file_info in host_files:
                upload_date = file_info.get('upload_date', '')
                if upload_date:
                    try:
                        # Extract just the date part
                        date_part = upload_date.split('T')[0] if 'T' in upload_date else upload_date.split(' ')[0]
                        dates_analysis[date_part] += 1
                    except:
                        dates_analysis['unknown'] += 1
                else:
                    dates_analysis['no_date'] += 1
            
            # Show date distribution for this host
            print(f"   Date distribution (top 10):")
            for date, count in sorted(dates_analysis.items(), key=lambda x: x[1], reverse=True)[:10]:
                print(f"     {date}: {count} files")
            
            await asyncio.sleep(1)
        
        print(f"\n📊 RAW FILES SUMMARY:")
        print(f"  Total raw files: {total_raw_files}")
        for host, files in all_files_by_host.items():
            print(f"  {host}: {len(files)} files")
        
        # Now test the actual extraction
        print(f"\n🎯 TESTING ACTUAL EXTRACTION (ALL TIME):")
        embed_data = await vm.extract_embed_codes(999999)
        final_count = len(embed_data)
        
        print(f"✅ Final extraction result: {final_count} unique files")
        print(f"📊 Expected: 107-110 files")
        print(f"📊 Actual: {final_count} files")
        
        if final_count > 110:
            print(f"❌ TOO MANY FILES: {final_count - 110} extra files")
            
            # Analyze what might be causing extras
            print(f"\n🔍 ANALYZING POTENTIAL DUPLICATE CAUSES:")
            
            # Group by normalized names to see if normalization is working
            normalized_groups = defaultdict(list)
            for file_data in embed_data:
                filename = file_data['filename']
                normalized = vm._normalize_filename(filename)
                normalized_groups[normalized].append(filename)
            
            # Find groups with multiple files (potential duplicates)
            duplicate_count = 0
            for normalized, filenames in normalized_groups.items():
                if len(filenames) > 1:
                    duplicate_count += len(filenames) - 1
                    print(f"⚠️ POTENTIAL DUPLICATE GROUP: '{normalized}'")
                    for filename in filenames:
                        print(f"    - {filename}")
            
            print(f"\nDuplicate analysis: {duplicate_count} potential duplicate files")
            
            # Analyze filename patterns for common issues
            print(f"\n🔤 FILENAME PATTERN ANALYSIS:")
            extension_patterns = defaultdict(int)
            for file_data in embed_data:
                filename = file_data['filename'].lower()
                if '.mkv.mp4' in filename:
                    extension_patterns['mkv.mp4'] += 1
                elif '.avi.mp4' in filename:
                    extension_patterns['avi.mp4'] += 1
                elif filename.endswith('.mp4'):
                    extension_patterns['mp4'] += 1
                elif filename.endswith('.mkv'):
                    extension_patterns['mkv'] += 1
                else:
                    extension_patterns['other'] += 1
            
            print("Extension patterns:")
            for pattern, count in extension_patterns.items():
                print(f"  {pattern}: {count} files")
        
        elif final_count < 107:
            print(f"⚠️ TOO FEW FILES: Missing {107 - final_count} expected files")
        else:
            print(f"✅ FILE COUNT WITHIN EXPECTED RANGE")
        
        return final_count, total_raw_files, all_files_by_host
        
    finally:
        await vm.close()

async def test_reliability_guarantee():
    """Test the reliability guarantee for future calls"""
    print("\n🔒 TESTING RELIABILITY GUARANTEE")
    print("=" * 60)
    
    print("Testing multiple extraction calls to ensure consistency...")
    
    results = []
    for i in range(3):
        print(f"\n🧪 Test Run {i+1}/3:")
        
        vm = VideoHostManager()
        await vm.initialize()
        
        try:
            # Test 48-hour filter
            embed_data_48h = await vm.extract_embed_codes(48)
            count_48h = len(embed_data_48h)
            
            # Check for any issues
            issues_found = []
            
            for file_data in embed_data_48h:
                filename = file_data['filename']
                embed_codes = file_data.get('embed_codes', {})
                
                # Check for empty codes
                for host, code in embed_codes.items():
                    if '/#}' in code or '/e//' in code or '#{' in code:
                        issues_found.append(f"Empty code in {host}: {code[:50]}...")
                    elif 'mock_' in code or 'template' in code.lower():
                        issues_found.append(f"Template code in {host}: {code[:50]}...")
            
            results.append({
                'run': i+1,
                'count_48h': count_48h,
                'issues': issues_found
            })
            
            print(f"  48-hour extraction: {count_48h} files")
            if issues_found:
                print(f"  ❌ Issues found: {len(issues_found)}")
                for issue in issues_found[:3]:  # Show first 3
                    print(f"    - {issue}")
            else:
                print(f"  ✅ No issues found")
        
        finally:
            await vm.close()
        
        await asyncio.sleep(2)  # Brief pause between tests
    
    # Analyze consistency
    counts_48h = [r['count_48h'] for r in results]
    all_issues = [issue for r in results for issue in r['issues']]
    
    print(f"\n📊 CONSISTENCY ANALYSIS:")
    print(f"  48-hour counts: {counts_48h}")
    print(f"  Count consistency: {'✅ CONSISTENT' if len(set(counts_48h)) == 1 else '❌ INCONSISTENT'}")
    print(f"  Total issues across all runs: {len(all_issues)}")
    
    if len(all_issues) == 0:
        print(f"  ✅ RELIABILITY CONFIRMED: No issues in any test run")
        return True
    else:
        print(f"  ❌ RELIABILITY ISSUES: Found {len(all_issues)} problems")
        return False

async def bulletproof_time_filtering():
    """Analyze and bulletproof time filtering logic"""
    print("\n🛡️ BULLETPROOFING TIME FILTERING")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test different time ranges
        time_ranges = [24, 48, 72, 168, 720, 999999]  # 1d, 2d, 3d, 1w, 1m, all
        
        print("Testing different time ranges:")
        range_results = {}
        
        for hours in time_ranges:
            print(f"\n⏰ Testing {hours} hours ({'All Time' if hours >= 999999 else f'{hours//24} days' if hours >= 24 else f'{hours} hours'}):")
            
            embed_data = await vm.extract_embed_codes(hours)
            count = len(embed_data)
            range_results[hours] = count
            
            print(f"  Result: {count} files")
            
            # For shorter ranges, verify they're subsets of longer ranges
            if hours < 999999:
                await asyncio.sleep(1)
        
        print(f"\n📈 TIME RANGE ANALYSIS:")
        for hours in sorted(range_results.keys()):
            count = range_results[hours]
            label = 'All Time' if hours >= 999999 else f'{hours}h'
            print(f"  {label:>10}: {count:>3} files")
        
        # Verify logical consistency (longer ranges should have >= files)
        sorted_ranges = sorted([h for h in range_results.keys() if h < 999999])
        consistency_ok = True
        
        for i in range(len(sorted_ranges) - 1):
            current_hours = sorted_ranges[i]
            next_hours = sorted_ranges[i + 1]
            current_count = range_results[current_hours]
            next_count = range_results[next_hours]
            
            if current_count > next_count:
                print(f"  ❌ INCONSISTENCY: {current_hours}h ({current_count}) > {next_hours}h ({next_count})")
                consistency_ok = False
        
        if consistency_ok:
            print(f"  ✅ TIME RANGE CONSISTENCY: OK")
        else:
            print(f"  ❌ TIME RANGE CONSISTENCY: FAILED")
        
        return range_results, consistency_ok
        
    finally:
        await vm.close()

async def main():
    """Main analysis function"""
    print("🚀 COMPREHENSIVE EMBED CODE EXTRACTOR ANALYSIS")
    print("=" * 80)
    
    # 1. Analyze ALL Time filter issue
    final_count, raw_count, files_by_host = await analyze_all_time_filter()
    
    # 2. Test reliability guarantee
    reliability_ok = await test_reliability_guarantee()
    
    # 3. Bulletproof time filtering
    range_results, consistency_ok = await bulletproof_time_filtering()
    
    # Final summary
    print("\n" + "=" * 80)
    print("📋 FINAL ANALYSIS SUMMARY")
    print("=" * 80)
    
    print(f"🎯 ALL TIME FILTER:")
    print(f"  Expected files: 107-110")
    print(f"  Actual files: {final_count}")
    if 107 <= final_count <= 110:
        print(f"  Status: ✅ WITHIN EXPECTED RANGE")
    else:
        print(f"  Status: ❌ OUTSIDE EXPECTED RANGE ({abs(final_count - 108)} off)")
    
    print(f"\n🔒 RELIABILITY GUARANTEE:")
    print(f"  Status: {'✅ CONFIRMED' if reliability_ok else '❌ ISSUES FOUND'}")
    
    print(f"\n🛡️ TIME FILTERING:")
    print(f"  Status: {'✅ CONSISTENT' if consistency_ok else '❌ INCONSISTENT'}")
    
    # Provide final confirmation
    if reliability_ok and consistency_ok and 107 <= final_count <= 110:
        print(f"\n🎉 EMBED CODE EXTRACTOR: FULLY BULLETPROOF ✅")
        print(f"   ✅ Future calls will always give correct embed codes")
        print(f"   ✅ No duplicate entries guaranteed") 
        print(f"   ✅ No template codes guaranteed")
        print(f"   ✅ Accurate time range filtering guaranteed")
        print(f"   ✅ Works for 1 file or multiple files")
        return True
    else:
        print(f"\n❌ EMBED CODE EXTRACTOR: NEEDS ADDITIONAL FIXES")
        if not reliability_ok:
            print(f"   ❌ Reliability issues found")
        if not consistency_ok:
            print(f"   ❌ Time filtering inconsistencies")
        if not (107 <= final_count <= 110):
            print(f"   ❌ ALL Time filter returning wrong count")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)