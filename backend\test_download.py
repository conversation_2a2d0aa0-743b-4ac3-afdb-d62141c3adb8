#!/usr/bin/env python3
import asyncio
import aiohttp
from core.aria2_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>
from config import Config

async def test_download():
    try:
        print("Testing server-to-server download...")
        
        # Initialize Aria2 manager
        aria2_manager = Aria2Manager()
        await aria2_manager.initialize()
        
        # Test URL (small file)
        test_url = "https://httpbin.org/json"
        test_filename = "test_httpbin.json"
        
        print(f"Starting download: {test_url}")
        
        # Add download
        gid = await aria2_manager.add_download(
            url=test_url,
            gdrive_folder_id=Config.GDRIVE_PRIMARY_FOLDER_ID,
            filename=test_filename
        )
        
        print(f"Download started with GID: {gid}")
        
        # Wait a bit and check status
        await asyncio.sleep(5)
        
        status = await aria2_manager.get_download_status(gid)
        print(f"Download status: {status}")
        
        # Wait for completion
        for i in range(30):  # Wait up to 30 seconds
            status = await aria2_manager.get_download_status(gid)
            if status and status.get("status") in ["complete", "error"]:
                break
            await asyncio.sleep(1)
            print(f"Waiting... ({i+1}/30)")
        
        final_status = await aria2_manager.get_download_status(gid)
        print(f"Final status: {final_status}")
        
        if final_status and final_status.get("status") == "complete":
            print("✅ Server-to-server download test PASSED!")
            return True
        else:
            print("❌ Server-to-server download test FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False
    finally:
        if 'aria2_manager' in locals():
            await aria2_manager.close()

if __name__ == "__main__":
    result = asyncio.run(test_download())
    print(f"Test result: {'PASSED' if result else 'FAILED'}")
