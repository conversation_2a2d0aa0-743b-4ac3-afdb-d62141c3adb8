# Google Drive Helper adapted from mirror-leech-telegram-bot
import os
import pickle
import logging
import re
import urllib.parse as urlparse
from urllib.parse import parse_qs
from typing import Optional, Dict, Any, List
from random import randrange
from pathlib import Path

from google.oauth2 import service_account
from googleapiclient.discovery import build
from google_auth_httplib2 import AuthorizedHttp
from googleapiclient.http import build_http, MediaFileUpload
from googleapiclient.errors import HttpError
from tenacity import (
    retry,
    wait_exponential,
    stop_after_attempt,
    retry_if_exception_type,
    RetryError,
)

from config import Config

LOGGER = logging.getLogger(__name__)
logging.getLogger('googleapiclient.discovery').setLevel(logging.ERROR)


class GoogleDriveHelper:
    """Google Drive Helper following mirror-leech-telegram-bot pattern"""
    
    def __init__(self):
        self._OAUTH_SCOPE = ["https://www.googleapis.com/auth/drive"]
        self.token_path = "token.pickle"
        self.G_DRIVE_DIR_MIME_TYPE = "application/vnd.google-apps.folder"
        self.G_DRIVE_BASE_DOWNLOAD_URL = "https://drive.google.com/uc?id={}&export=download"
        self.G_DRIVE_DIR_BASE_DOWNLOAD_URL = "https://drive.google.com/drive/folders/{}"
        
        self.is_uploading = False
        self.is_downloading = False
        self.is_cloning = False
        self.sa_index = 0
        self.sa_count = 1
        self.sa_number = 100
        self.alt_auth = False
        self.service = None
        self.total_files = 0
        self.total_folders = 0
        self.file_processed_bytes = 0
        self.proc_bytes = 0
        self.total_time = 0
        self.status = None
        self.update_interval = Config.UPDATE_INTERVAL
        self.use_sa = Config.USE_SERVICE_ACCOUNTS

    @property
    def speed(self):
        """Calculate upload/download speed"""
        try:
            return self.proc_bytes / self.total_time
        except:
            return 0

    @property
    def processed_bytes(self):
        """Get processed bytes"""
        return self.proc_bytes

    async def progress(self):
        """Update progress tracking"""
        if self.status is not None:
            chunk_size = (
                self.status.total_size * self.status.progress()
                - self.file_processed_bytes
            )
            self.file_processed_bytes = self.status.total_size * self.status.progress()
            self.proc_bytes += chunk_size
            self.total_time += self.update_interval

    def authorize(self):
        """Authorize Google Drive API following mirror-leech-telegram-bot pattern"""
        credentials = None
        
        if self.use_sa:
            # Use service accounts
            accounts_dir = Path("accounts")
            if accounts_dir.exists():
                json_files = [f for f in accounts_dir.iterdir() if f.suffix == '.json']
                if json_files:
                    self.sa_number = len(json_files)
                    self.sa_index = randrange(self.sa_number)
                    selected_file = json_files[self.sa_index]
                    LOGGER.info(f"Authorizing with {selected_file.name} service account")
                    credentials = service_account.Credentials.from_service_account_file(
                        str(selected_file), scopes=self._OAUTH_SCOPE
                    )
                else:
                    LOGGER.error("No service account files found in accounts/ directory")
            else:
                LOGGER.error("accounts/ directory not found")
        elif os.path.exists(self.token_path):
            # Use token.pickle
            LOGGER.info(f"Authorize with {self.token_path}")
            with open(self.token_path, "rb") as f:
                credentials = pickle.load(f)
        else:
            LOGGER.error("token.pickle not found!")
        
        if credentials:
            authorized_http = AuthorizedHttp(credentials, http=build_http())
            authorized_http.http.disable_ssl_certificate_validation = True
            return build("drive", "v3", http=authorized_http, cache_discovery=False)
        else:
            raise Exception("Failed to authorize Google Drive API")

    def switch_service_account(self):
        """Switch to next service account"""
        if self.sa_index == self.sa_number - 1:
            self.sa_index = 0
        else:
            self.sa_index += 1
        self.sa_count += 1
        LOGGER.info(f"Switching to {self.sa_index} index")
        self.service = self.authorize()

    @retry(
        wait=wait_exponential(multiplier=2, min=3, max=6),
        stop=stop_after_attempt(3),
        retry=retry_if_exception_type(Exception),
    )
    def create_directory(self, directory_name: str, dest_id: str) -> str:
        """Create directory in Google Drive"""
        file_metadata = {
            "name": directory_name,
            "mimeType": self.G_DRIVE_DIR_MIME_TYPE,
        }
        if dest_id is not None:
            file_metadata["parents"] = [dest_id]
        
        file = self.service.files().create(
            body=file_metadata, supportsAllDrives=True
        ).execute()
        
        file_id = file.get("id")
        if not Config.IS_TEAM_DRIVE:
            self.set_permission(file_id)
        
        LOGGER.info(f"Created G-Drive Folder: {directory_name}")
        return file_id

    def set_permission(self, file_id: str):
        """Set file permissions"""
        permissions = {"role": "reader", "type": "anyone"}
        return self.service.permissions().create(
            fileId=file_id, body=permissions, supportsAllDrives=True
        ).execute()

    def get_id_from_url(self, link: str) -> str:
        """Extract file ID from Google Drive URL"""
        if "folders" in link or "file" in link:
            regex = r"https://drive\.google\.com/(drive)?/?u?/?\d?/?(mobile)?/?(file)?(folders)?/?d?/([-\w]+)[?+]?/?(w+)?"
            res = re.search(regex, link)
            if res is None:
                raise IndexError("GDrive ID not found.")
            return res.group(5)
        parsed = urlparse.urlparse(link)
        return parse_qs(parsed.query)["id"][0]
