import asyncio, sys
sys.path.append(r'g:/My Websites/Catalogue-Website/automatic-upload-bot/automatic-upload-bot/backend')
from backend.api.video_hosts import VideoHostManager
from config import Config

print('Config FILEMOON_API_KEY:', Config.FILEMOON_API_KEY)
print('Config FILEMOON_API_URL:', Config.FILEMOON_API_URL)

async def run():
    mgr = VideoHostManager()
    await mgr.initialize()
    try:
        files = await mgr._list_filemoon_files(48)
        print('Found', len(files), 'files')
        if files:
            import json
            print(json.dumps(files[:10], indent=2))
    finally:
        await mgr.close()

if __name__ == '__main__':
    asyncio.run(run())
