import { useState } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import Header from "@/components/Header";
import TabNavigation from "@/components/TabNavigation";
import DuplicateChecker from "@/components/DuplicateChecker";
import EmbedExtractor from "@/components/EmbedExtractor";
import DriveCleanup from "@/components/DriveCleanup";
import AutoVideoUploader from "@/components/AutoVideoUploader";
import Footer from "@/components/Footer";

const queryClient = new QueryClient();

const Index = () => {
  const [activeTab, setActiveTab] = useState("auto");

  const renderActiveTab = () => {
    switch (activeTab) {
      case "auto":
        return <AutoVideoUploader />;
      case "duplicate":
        return <DuplicateChecker />;
      case "embed":
        return <EmbedExtractor />;
      case "cleanup":
        return <DriveCleanup />;
      default:
        return <AutoVideoUploader />;
    }
  };

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <div className="min-h-screen bg-background">
          <Header />
          <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
          <main className="container mx-auto p-6">
            {renderActiveTab()}
          </main>
          <Footer />
        </div>
        <Toaster />
        <Sonner />
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default Index;
