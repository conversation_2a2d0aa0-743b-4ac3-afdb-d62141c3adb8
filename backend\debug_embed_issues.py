#!/usr/bin/env python3
"""
Debug script to identify embed code extraction issues
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from collections import defaultdict

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.video_hosts import VideoHostManager
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)

async def debug_embed_extraction():
    """Debug the embed extraction process step by step"""
    print("=" * 60)
    print("DEBUGGING EMBED CODE EXTRACTION ISSUES")
    print("=" * 60)
    
    # Initialize video manager
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test 1: Check time filtering logic
        print("\n1. TESTING TIME FILTERING LOGIC")
        print("-" * 40)
        
        hours = 48
        cutoff_time = datetime.now() - timedelta(hours=hours)
        print(f"Current time: {datetime.now()}")
        print(f"Cutoff time (48h ago): {cutoff_time}")
        
        # Test 2: Get raw files from each host
        print("\n2. GETTING RAW FILES FROM EACH HOST")
        print("-" * 40)
        
        all_raw_files = []
        for host in ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']:
            try:
                print(f"\nFetching files from {host}...")
                host_files = await vm.list_recent_uploads(host, hours)
                print(f"  Raw files from {host}: {len(host_files)}")
                
                for file_info in host_files:
                    file_info['host'] = host
                    all_raw_files.append(file_info)
                    
            except Exception as e:
                print(f"  ERROR fetching from {host}: {e}")
        
        print(f"\nTotal raw files collected: {len(all_raw_files)}")
        
        # Test 3: Analyze filename normalization
        print("\n3. ANALYZING FILENAME NORMALIZATION")
        print("-" * 40)
        
        filename_groups = defaultdict(list)
        for file_info in all_raw_files:
            filename = file_info.get('filename', '')
            normalized = vm._normalize_filename(filename)
            
            filename_groups[normalized].append({
                'original': filename,
                'host': file_info['host'],
                'upload_date': file_info.get('upload_date', ''),
                'file_code': file_info.get('file_code', '')
            })
        
        print(f"Unique normalized filenames: {len(filename_groups)}")
        
        # Find potential duplicates
        duplicates_found = 0
        for normalized, files in filename_groups.items():
            if len(files) > 1:
                duplicates_found += len(files) - 1
                print(f"\nPOTENTIAL DUPLICATE GROUP: '{normalized}'")
                for file_data in files:
                    print(f"  - {file_data['host']}: {file_data['original']}")
        
        print(f"\nTotal potential duplicates: {duplicates_found}")
        
        # Test 4: Test time filtering on actual files
        print("\n4. TESTING TIME FILTERING ON ACTUAL FILES")
        print("-" * 40)
        
        recent_files = []
        old_files = []
        
        for file_info in all_raw_files:
            upload_date = file_info.get('upload_date', '') or file_info.get('uploaded', '')
            is_recent = vm._is_file_recent_by_date(upload_date, cutoff_time)
            
            if is_recent:
                recent_files.append(file_info)
            else:
                old_files.append(file_info)
        
        print(f"Files within 48h: {len(recent_files)}")
        print(f"Files older than 48h: {len(old_files)}")
        
        # Test 5: Run actual embed extraction
        print("\n5. RUNNING ACTUAL EMBED EXTRACTION")
        print("-" * 40)
        
        embed_data = await vm.extract_embed_codes(48)
        print(f"Final embed data count: {len(embed_data)}")
        
        # Analyze the results
        print("\n6. ANALYZING FINAL RESULTS")
        print("-" * 40)
        
        hosts_per_file = defaultdict(int)
        for item in embed_data:
            embed_codes = item.get('embed_codes', {})
            valid_hosts = [host for host, code in embed_codes.items() if code and code.strip()]
            hosts_per_file[len(valid_hosts)] += 1
        
        print("Files by number of valid embed codes:")
        for num_hosts, count in sorted(hosts_per_file.items()):
            print(f"  {num_hosts} hosts: {count} files")
        
        # Check for template/mock codes
        print("\n7. CHECKING FOR TEMPLATE/MOCK CODES")
        print("-" * 40)
        
        mock_patterns = ['template', 'mock', 'example', 'test']
        mock_count = 0
        
        for item in embed_data:
            embed_codes = item.get('embed_codes', {})
            for host, code in embed_codes.items():
                if code and any(pattern in code.lower() for pattern in mock_patterns):
                    mock_count += 1
                    print(f"  MOCK CODE DETECTED in {host}: {code[:100]}...")
        
        print(f"Total mock codes found: {mock_count}")
        
        # Summary
        print("\n" + "=" * 60)
        print("SUMMARY")
        print("=" * 60)
        print(f"Raw files collected: {len(all_raw_files)}")
        print(f"Files after time filtering: {len(recent_files)}")
        print(f"Unique normalized names: {len(filename_groups)}")
        print(f"Potential duplicates: {duplicates_found}")
        print(f"Final embed data: {len(embed_data)}")
        print(f"Mock codes detected: {mock_count}")
        
        if len(embed_data) > len(filename_groups):
            print(f"\nWARNING: More embed data ({len(embed_data)}) than unique files ({len(filename_groups)})")
            print("This suggests duplicate detection is not working properly!")
        
    finally:
        await vm.close()

if __name__ == "__main__":
    asyncio.run(debug_embed_extraction())
