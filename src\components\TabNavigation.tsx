import { Button } from "@/components/ui/button";
import {
  Upload,
  Activity,
  Copy,
  Code,
  Trash2,
  Zap,
  Archive
} from "lucide-react";

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const TabNavigation = ({ activeTab, onTabChange }: TabNavigationProps) => {
  const tabs = [
    { id: "auto", label: "Auto Uploader", icon: Zap, color: "orange" },
    { id: "duplicate", label: "Duplicate Checker", icon: Copy, color: "secondary" },
    { id: "embed", label: "Embed Extractor", icon: Code, color: "purple" },
    { id: "cleanup", label: "Drive Cleanup", icon: Trash2, color: "destructive" },
  ];

  return (
    <div className="border-b-4 border-black bg-muted p-4">
      <div className="container mx-auto">
        <div className="flex gap-2 overflow-x-auto">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            const getActiveStyle = () => {
              if (!isActive) return "bg-background text-foreground hover:bg-muted hover:text-foreground";
              
              switch (tab.color) {
                case "primary":
                  return "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground";
                case "accent":
                  return "bg-accent text-accent-foreground hover:bg-accent hover:text-accent-foreground";
                case "secondary":
                  return "bg-secondary text-secondary-foreground hover:bg-secondary hover:text-secondary-foreground";
                case "purple":
                  return "bg-purple text-purple-foreground hover:bg-purple hover:text-purple-foreground";
                case "destructive":
                  return "bg-destructive text-destructive-foreground hover:bg-destructive hover:text-destructive-foreground";
                case "orange":
                  return "bg-orange-500 text-white hover:bg-orange-600 hover:text-white";
                case "green":
                  return "bg-green-500 text-white hover:bg-green-600 hover:text-white";
                default:
                  return "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground";
              }
            };
            
            return (
              <Button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                variant={isActive ? "default" : "outline"}
                className={`brutal-button whitespace-nowrap ${getActiveStyle()}`}
              >
                <Icon className="w-3 h-3 mr-2" />
                {tab.label}
              </Button>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default TabNavigation;