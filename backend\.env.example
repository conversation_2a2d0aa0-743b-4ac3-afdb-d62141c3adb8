# AutoUploadBot Configuration
# Copy this file to .env and fill in your values

# Server Configuration
HOST=localhost
PORT=8001

# Google Drive Configuration
GDRIVE_PRIMARY_FOLDER_ID=your_primary_folder_id_here
GDRIVE_SECONDARY_FOLDER_ID=your_secondary_folder_id_here
USE_SERVICE_ACCOUNTS=True
IS_TEAM_DRIVE=False

# Video Hosting API Keys
STREAMP2P_API_KEY=your_streamp2p_api_key_here
RPMSHARE_API_KEY=your_rpmshare_api_key_here
UPNSHARE_API_KEY=your_upnshare_api_key_here
FILEMOON_API_KEY=your_filemoon_api_key_here

# Aria2 Configuration
ARIA2_HOST=localhost
ARIA2_PORT=6800
ARIA2_SECRET=

# Download Configuration (Server-to-Server Only)
MAX_CONCURRENT_DOWNLOADS=3
MAX_CONCURRENT_UPLOADS=1

# File Configuration
EXCLUDED_EXTENSIONS=.txt,.nfo,.md,.log
CHUNK_SIZE=********
UPDATE_INTERVAL=3

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY=5

# Embed Code Configuration
EMBED_EXTRACT_HOURS=48

# Audio Notifications
ENABLE_AUDIO_NOTIFICATIONS=True
