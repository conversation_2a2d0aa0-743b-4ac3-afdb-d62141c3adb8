#!/usr/bin/env python3
"""
Individual Feature Testing for AutoUploadBot
Tests each feature individually with detailed logging
"""
import asyncio
import aiohttp
import logging
import json
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

API_BASE_URL = "http://localhost:8001"

async def test_simple_url_download():
    """Test simple URL download"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("🔍 Testing Simple URL Download...")
            
            # Test with a very small file
            test_url = "https://httpbin.org/uuid"
            payload = {"urls": [test_url]}
            
            LOGGER.info(f"Sending request to: {API_BASE_URL}/api/download/url")
            LOGGER.info(f"Payload: {payload}")
            
            async with session.post(
                f"{API_BASE_URL}/api/download/url", 
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                LOGGER.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Response data: {json.dumps(data, indent=2)}")
                    return True
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Error response: {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Exception: {e}")
            return False

async def test_gdrive_list():
    """Test Google Drive file listing"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("📁 Testing Google Drive File Listing...")
            
            async with session.get(
                f"{API_BASE_URL}/api/gdrive/list/primary",
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                LOGGER.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ GDrive files: {json.dumps(data, indent=2)}")
                    return True
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Error response: {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Exception: {e}")
            return False

async def test_downloads_status():
    """Test downloads status endpoint"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("📊 Testing Downloads Status...")
            
            async with session.get(
                f"{API_BASE_URL}/api/downloads/status",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                LOGGER.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Downloads status: {json.dumps(data, indent=2)}")
                    return True
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Error response: {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Exception: {e}")
            return False

async def test_video_host_upload():
    """Test video host upload with a small file"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("🎥 Testing Video Host Upload...")
            
            # Use a small test video
            payload = {
                "file_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4",
                "filename": "test_small_video.mp4",
                "platforms": ["filemoon"]  # Test only one platform
            }
            
            LOGGER.info(f"Payload: {json.dumps(payload, indent=2)}")
            
            async with session.post(
                f"{API_BASE_URL}/api/upload/video-hosts",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)  # Longer timeout for uploads
            ) as response:
                LOGGER.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Upload response: {json.dumps(data, indent=2)}")
                    return True
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Error response: {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Exception: {e}")
            return False

async def test_app_stats():
    """Test application statistics"""
    async with aiohttp.ClientSession() as session:
        try:
            LOGGER.info("📈 Testing App Statistics...")
            
            async with session.get(
                f"{API_BASE_URL}/api/stats",
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                LOGGER.info(f"Response status: {response.status}")
                
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ App stats: {json.dumps(data, indent=2)}")
                    return True
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ Error response: {error_text}")
                    return False
                    
        except Exception as e:
            LOGGER.error(f"❌ Exception: {e}")
            return False

async def main():
    """Run individual feature tests"""
    LOGGER.info("🚀 Starting Individual Feature Tests")
    
    tests = [
        ("Downloads Status", test_downloads_status),
        ("App Statistics", test_app_stats),
        ("Simple URL Download", test_simple_url_download),
        ("Google Drive List", test_gdrive_list),
        ("Video Host Upload", test_video_host_upload),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        LOGGER.info(f"\n{'='*60}")
        LOGGER.info(f"🧪 Running: {test_name}")
        LOGGER.info(f"{'='*60}")
        
        try:
            result = await test_func()
            results[test_name] = result
            status = "✅ PASSED" if result else "❌ FAILED"
            LOGGER.info(f"Result: {status}")
        except Exception as e:
            results[test_name] = False
            LOGGER.error(f"❌ FAILED with exception: {e}")
        
        # Wait between tests
        await asyncio.sleep(2)
    
    # Summary
    LOGGER.info(f"\n{'='*60}")
    LOGGER.info("📊 INDIVIDUAL TEST SUMMARY")
    LOGGER.info(f"{'='*60}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        LOGGER.info(f"{test_name}: {status}")
    
    LOGGER.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        LOGGER.info("🎉 ALL INDIVIDUAL TESTS PASSED!")
    else:
        LOGGER.warning(f"⚠️ {total - passed} tests failed.")

if __name__ == "__main__":
    asyncio.run(main())
