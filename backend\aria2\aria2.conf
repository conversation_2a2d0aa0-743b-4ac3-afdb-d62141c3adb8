# Aria2 Configuration for AutoUploadBot
# Basic Settings
dir=./downloads
file-allocation=none
continue=true
max-concurrent-downloads=3
max-connection-per-server=16
min-split-size=1M
split=16

# RPC Settings
enable-rpc=true
rpc-listen-all=true
rpc-listen-port=6800
rpc-allow-origin-all=true

# BitTorrent Settings
bt-enable-lpd=true
bt-enable-hook-after-hash-check=true
bt-max-peers=50
bt-request-peer-speed-limit=100K
bt-stop-timeout=0
seed-ratio=0
seed-time=0

# Advanced Settings
auto-file-renaming=false
parameterized-uri=true
enable-http-keep-alive=true
enable-http-pipelining=true
max-tries=3
retry-wait=3
timeout=60
connect-timeout=60

# Logging
log-level=info
console-log-level=info
