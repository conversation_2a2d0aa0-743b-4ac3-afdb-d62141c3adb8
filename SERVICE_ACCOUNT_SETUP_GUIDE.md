# Service Account Setup Guide for Google Drive

## Issue Identified
The current service account doesn't have proper access to Google Drive folders, causing the error:
```
googleapi: Error 404: Shared drive not found: false, notFound
```

## Solution Steps

### 1. Share Google Drive Folder with Service Account

1. **Get Service Account Email**:
   - Open `backend/accounts/service_account.json`
   - Find the `client_email` field: `<EMAIL>`

2. **Share Primary Folder**:
   - Go to Google Drive
   - Navigate to folder ID: `1yN9h1urFbusV6CEboM2Pd08AMgJKqkEq`
   - Right-click → Share
   - Add email: `<EMAIL>`
   - Set permission: **Editor** (to allow uploads)
   - Click Send

3. **Share Secondary Folder**:
   - Navigate to folder ID: `1DRTg4Om0LwFGlFaxOGX4izPWBdwlxKGJ`
   - Repeat the same sharing process

### 2. Test RClone Configuration

After sharing the folders, test the configuration:

```bash
# Test listing the primary folder
backend\rclone\rclone-v1.70.3-windows-amd64\rclone.exe lsd gdrive:1yN9h1urFbusV6CEboM2Pd08AMgJKqkEq

# Test uploading a small file
echo "test" > test.txt
backend\rclone\rclone-v1.70.3-windows-amd64\rclone.exe copy test.txt gdrive:1yN9h1urFbusV6CEboM2Pd08AMgJKqkEq/
```

### 3. Alternative: Create New Service Account

If the current service account can't be fixed:

1. **Create New Service Account**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Navigate to IAM & Admin → Service Accounts
   - Create new service account
   - Download JSON key file

2. **Enable Google Drive API**:
   - Go to APIs & Services → Library
   - Search for "Google Drive API"
   - Enable it

3. **Replace Service Account File**:
   - Replace `backend/accounts/service_account.json` with new file
   - Update rclone configuration

### 4. Verify Fixes

Run the test script to verify everything works:

```bash
cd backend
python test_download_fixes.py
```

## Expected Results

After proper setup, you should see:
- ✅ RClone can list and access Google Drive folders
- ✅ Remote URL downloads work without 500 errors
- ✅ Magnet links convert to URLs and download successfully

## Troubleshooting

### Error: "Shared drive not found"
- Ensure service account email is shared with correct permissions
- Check folder IDs in `.env` file are correct
- Verify service account has Google Drive API enabled

### Error: "Permission denied"
- Service account needs **Editor** permission, not just Viewer
- Check if folders are in a Team Drive (requires different configuration)

### Error: "Invalid credentials"
- Service account JSON file may be corrupted
- Re-download from Google Cloud Console
- Ensure file path in rclone config is correct
