#!/usr/bin/env python3
"""
Final Comprehensive Verification for AutoUploadBot
Verifies ALL user requirements are met
"""
import asyncio
import aiohttp
import logging
import json
import subprocess
import os

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

class FinalVerification:
    """Final comprehensive verification"""
    
    def __init__(self):
        self.frontend_url = "http://localhost:8080"
        self.backend_url = "http://localhost:8001"
        self.verification_results = {}
    
    async def verify_oauth_authentication(self):
        """Verify OAuth authentication is working"""
        try:
            LOGGER.info("🔐 Verifying OAuth Authentication...")
            
            # Check rclone config
            result = subprocess.run([
                "backend/rclone/rclone-v1.70.3-windows-amd64/rclone.exe", 
                "config", "show"
            ], capture_output=True, text=True, cwd=".")
            
            if "gdrive-primary" in result.stdout and "gdrive-secondary" in result.stdout:
                LOGGER.info("✅ OAuth accounts configured")
                
                # Test file listing
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.backend_url}/api/gdrive/list/primary") as response:
                        if response.status == 200:
                            data = await response.json()
                            LOGGER.info(f"✅ OAuth working - {len(data.get('files', []))} files found")
                            return True
            
            return False
        except Exception as e:
            LOGGER.error(f"❌ OAuth verification failed: {e}")
            return False
    
    async def verify_dual_gdrive_accounts(self):
        """Verify dual Google Drive accounts"""
        try:
            LOGGER.info("💾 Verifying Dual Google Drive Accounts...")
            
            async with aiohttp.ClientSession() as session:
                # Test primary account
                async with session.get(f"{self.backend_url}/api/gdrive/list/primary") as response:
                    primary_ok = response.status == 200
                
                # Test secondary account  
                async with session.get(f"{self.backend_url}/api/gdrive/list/secondary") as response:
                    secondary_ok = response.status == 200
                
                if primary_ok and secondary_ok:
                    LOGGER.info("✅ Both primary and secondary accounts working")
                    return True
                else:
                    LOGGER.error(f"❌ Primary: {primary_ok}, Secondary: {secondary_ok}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ Dual account verification failed: {e}")
            return False
    
    async def verify_video_hosting_platforms(self):
        """Verify all 5 video hosting platforms"""
        try:
            LOGGER.info("🎥 Verifying Video Hosting Platforms...")
            
            async with aiohttp.ClientSession() as session:
                payload = {
                    "file_url": "https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4",
                    "filename": "verification_test.mp4"
                }
                
                async with session.post(
                    f"{self.backend_url}/api/upload/video-hosts",
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        upload_results = data.get("upload_results", {})
                        embed_codes = data.get("embed_codes", {})
                        
                        # Check if at least one platform worked
                        working_platforms = [k for k, v in upload_results.items() if v is not None]
                        
                        LOGGER.info(f"✅ Working platforms: {working_platforms}")
                        LOGGER.info(f"✅ Embed codes generated: {list(embed_codes.keys())}")
                        
                        return len(working_platforms) > 0
                    else:
                        LOGGER.error(f"❌ Video hosting test failed: {response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ Video hosting verification failed: {e}")
            return False
    
    async def verify_embed_code_formats(self):
        """Verify embed code formats are correct"""
        try:
            LOGGER.info("🔗 Verifying Embed Code Formats...")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.backend_url}/api/embed/extract", json={}) as response:
                    if response.status == 200:
                        data = await response.json()
                        embed_data = data.get("embed_data", [])
                        
                        if embed_data:
                            sample_embeds = embed_data[0].get("embed_codes", {})
                            
                            # Check required formats
                            required_formats = {
                                "streamp2p": "streamdb.p2pstream.online/#",
                                "rpmshare": "streamdb.rpmstream.online/#", 
                                "upnshare": "streamdb.upns.online/#",
                                "filemoon": "filemoon.to/e/"
                            }
                            
                            all_correct = True
                            for platform, expected_format in required_formats.items():
                                if platform in sample_embeds:
                                    embed_code = sample_embeds[platform]
                                    if expected_format in embed_code:
                                        LOGGER.info(f"✅ {platform} format correct")
                                    else:
                                        LOGGER.error(f"❌ {platform} format incorrect")
                                        all_correct = False
                            
                            return all_correct
                        else:
                            LOGGER.info("✅ Embed extraction working (no data to verify formats)")
                            return True
                    else:
                        LOGGER.error(f"❌ Embed extraction failed: {response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ Embed format verification failed: {e}")
            return False
    
    async def verify_csv_functionality(self):
        """Verify CSV generation and download"""
        try:
            LOGGER.info("📊 Verifying CSV Functionality...")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.backend_url}/api/embed/download-csv") as response:
                    if response.status == 200:
                        # Check if it's CSV content
                        content_type = response.headers.get('content-type', '')
                        if 'csv' in content_type.lower() or 'text' in content_type.lower():
                            LOGGER.info("✅ CSV download working")
                            return True
                        else:
                            LOGGER.error(f"❌ CSV content type incorrect: {content_type}")
                            return False
                    else:
                        LOGGER.error(f"❌ CSV download failed: {response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ CSV verification failed: {e}")
            return False
    
    async def verify_duplicate_detection(self):
        """Verify duplicate file detection"""
        try:
            LOGGER.info("🔍 Verifying Duplicate Detection...")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.backend_url}/api/duplicate/check", json={}) as response:
                    if response.status == 200:
                        data = await response.json()
                        if "duplicates" in data:
                            LOGGER.info("✅ Duplicate detection working")
                            return True
                        else:
                            LOGGER.error("❌ Duplicate detection response invalid")
                            return False
                    else:
                        LOGGER.error(f"❌ Duplicate detection failed: {response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ Duplicate detection verification failed: {e}")
            return False
    
    async def verify_ui_components(self):
        """Verify UI components are accessible"""
        try:
            LOGGER.info("🖥️ Verifying UI Components...")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(self.frontend_url) as response:
                    if response.status == 200:
                        LOGGER.info("✅ Frontend UI accessible")
                        return True
                    else:
                        LOGGER.error(f"❌ Frontend UI not accessible: {response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ UI verification failed: {e}")
            return False
    
    async def run_final_verification(self):
        """Run all verification tests"""
        LOGGER.info("🚀 Starting Final Comprehensive Verification")
        LOGGER.info("Checking ALL user requirements...")
        
        verifications = [
            ("OAuth Authentication", self.verify_oauth_authentication),
            ("Dual Google Drive Accounts", self.verify_dual_gdrive_accounts),
            ("Video Hosting Platforms", self.verify_video_hosting_platforms),
            ("Embed Code Formats", self.verify_embed_code_formats),
            ("CSV Functionality", self.verify_csv_functionality),
            ("Duplicate Detection", self.verify_duplicate_detection),
            ("UI Components", self.verify_ui_components),
        ]
        
        results = {}
        
        for verification_name, verification_func in verifications:
            LOGGER.info(f"\n{'='*60}")
            LOGGER.info(f"🔍 Verifying: {verification_name}")
            LOGGER.info(f"{'='*60}")
            
            try:
                result = await verification_func()
                results[verification_name] = result
                status = "✅ VERIFIED" if result else "❌ FAILED"
                LOGGER.info(f"{verification_name}: {status}")
            except Exception as e:
                results[verification_name] = False
                LOGGER.error(f"{verification_name}: ❌ FAILED - {e}")
        
        # Final Summary
        LOGGER.info(f"\n{'='*60}")
        LOGGER.info("🏆 FINAL VERIFICATION SUMMARY")
        LOGGER.info(f"{'='*60}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for verification_name, result in results.items():
            status = "✅ VERIFIED" if result else "❌ FAILED"
            LOGGER.info(f"{verification_name}: {status}")
        
        LOGGER.info(f"\nOverall: {passed}/{total} verifications passed")
        
        if passed == total:
            LOGGER.info("🎉🎉🎉 ALL REQUIREMENTS VERIFIED! APPLICATION IS 100% FUNCTIONAL! 🎉🎉🎉")
            LOGGER.info("✅ OAuth authentication working")
            LOGGER.info("✅ Dual Google Drive accounts working") 
            LOGGER.info("✅ Video hosting platforms working")
            LOGGER.info("✅ Embed code generation working")
            LOGGER.info("✅ CSV functionality working")
            LOGGER.info("✅ Duplicate detection working")
            LOGGER.info("✅ UI components working")
            LOGGER.info("🚀 AutoUploadBot is READY FOR PRODUCTION USE!")
        else:
            LOGGER.warning(f"⚠️ {total - passed} verifications failed. Check logs for details.")
        
        return results

async def main():
    """Main verification runner"""
    verifier = FinalVerification()
    await verifier.run_final_verification()

if __name__ == "__main__":
    asyncio.run(main())
