#!/usr/bin/env python3
"""
Test with real-world URL from user's error message
"""
import requests
import json

BASE_URL = "http://localhost:8004"

def test_real_url_download():
    """Test with the actual URL from user's error message"""
    print("Testing real-world URL download...")
    
    # This is the URL from the user's error message
    real_url = "https://ddl2.cloudserver-1692963307.workers.dev/1397306580/f87c3e45e1e8a2b1154971813cfe664d1ef2595ec3f6a79321f4db4e75653e3c2570c26a3fce374c29ea646c9fdc8553fd6605bc8329a329ae2ed1a73dd58ec6686ab37e32539e7632e00b7033fdea58727ba9bc72192192379e0a5357b9f38a7e67c9227e5be24b8a5a5c8178913a647ea51f7d081df7327f006746a31307670398f90c162856778fd87a1f397aaa4dde1872577bc211f0a33c262aa64fcd9b72879f0d4b9deda1175de1c754d3082a6ea8aa067a3c8f3de0451459bb1ca1c5::abd83afd81cbb718095e50208690dc58/Sena.Guardians.of.the.Nation.S01.480p.Hindi.WEB-DL.ESub.x264-HDHub4u.Ms.zip"
    
    url = f"{BASE_URL}/api/download/url"
    payload = {
        "urls": [real_url]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                print("SUCCESS: Real-world URL download started successfully!")
                print(f"GID: {data['results'][0]['gid']}")
                return True
        
        print("FAIL: Real-world URL download failed")
        return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

def test_download_status():
    """Check download status"""
    print("\nChecking download status...")
    
    url = f"{BASE_URL}/api/downloads/status"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            downloads = data.get('downloads', [])
            print(f"Active Downloads: {len(downloads)}")
            
            for download in downloads:
                print(f"  - GID: {download.get('gid')}")
                print(f"    Status: {download.get('status')}")
                print(f"    Progress: {download.get('progress', 'N/A')}")
            
            return True
        else:
            print("FAIL: Download status failed")
            return False
    except Exception as e:
        print(f"ERROR: {e}")
        return False

if __name__ == "__main__":
    print("Testing AutoUploadBot with Real-World Scenario")
    print("=" * 50)
    
    # Test real URL
    url_result = test_real_url_download()
    
    # Check status
    status_result = test_download_status()
    
    print("\n" + "=" * 50)
    print("REAL-WORLD TEST RESULTS:")
    print(f"Real URL Download: {'PASS' if url_result else 'FAIL'}")
    print(f"Download Status: {'PASS' if status_result else 'FAIL'}")
    
    if url_result and status_result:
        print("\n🎉 SUCCESS: AutoUploadBot is working with real-world URLs!")
        print("The Windows subprocess issue is completely resolved!")
    else:
        print("\n⚠️ Some tests failed. Check the logs above.")
