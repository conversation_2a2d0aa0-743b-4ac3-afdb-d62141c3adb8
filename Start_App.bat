@echo off
setlocal
REM ================================================================
REM AutoUploadBot - FIXED Start Script (ProactorEventLoop)
REM Launches 3 separate windows for Aria2, Backend, and Frontend
REM WINDOWS SUBPROCESS ISSUE COMPLETELY RESOLVED!
REM ================================================================

cd /d "%~dp0"

echo ================================================================
echo AutoUploadBot Launcher
echo ================================================================
echo Starting services at %DATE% %TIME%

REM Paths
set ROOT_DIR=%CD%
set BACKEND_DIR=%ROOT_DIR%\backend
set ARIA2_EXE=%BACKEND_DIR%\aria2\aria2-1.37.0-win-64bit-build1\aria2c.exe
set ARIA2_CONF=%BACKEND_DIR%\aria2\aria2.conf

REM Quick checks (no pause on failure, just warnings)
where python >NUL 2>&1
if errorlevel 1 (
  echo [WARNING] Python not found in PATH
) else (
  echo [OK] Python found
)

where npm >NUL 2>&1
if errorlevel 1 (
  echo [WARNING] npm not found in PATH
) else (
  echo [OK] npm found
)

if not exist "%ARIA2_EXE%" (
  echo [WARNING] aria2c.exe not found - run Setup_App.bat first
) else (
  echo [OK] Aria2 found
)

if not exist "%ARIA2_CONF%" (
  echo [WARNING] aria2.conf not found - run Setup_App.bat first
) else (
  echo [OK] Aria2 config found
)

if not exist "%ROOT_DIR%\node_modules" (
  echo [WARNING] node_modules missing - run Setup_App.bat first
) else (
  echo [OK] Node modules found
)

echo.
echo ================================================================
echo Starting 3 service windows...
echo ================================================================

REM Start Aria2 in separate window
echo [1/3] Starting Aria2 RPC server...
start "Aria2 RPC Server - Port 6800" cmd /k "cd /d "%BACKEND_DIR%" && echo Starting Aria2... && "%ARIA2_EXE%" --conf-path="%ARIA2_CONF%""
timeout /t 2 >NUL

REM Start Backend in separate window (using ProactorEventLoop-fixed server)
echo [2/3] Starting Backend server with ProactorEventLoop fix...
start "AutoUploadBot Backend - Port 8001 (FIXED)" cmd /k "cd /d "%BACKEND_DIR%" && echo Starting Backend with ProactorEventLoop fix... && python start_server.py"
timeout /t 3 >NUL

REM Start Frontend in separate window
echo [3/3] Starting Frontend server...
start "AutoUploadBot Frontend - Port 8080" cmd /k "cd /d "%ROOT_DIR%" && echo Starting Frontend... && npm run dev"
timeout /t 3 >NUL

echo.
echo ================================================================
echo AutoUploadBot started successfully!
echo ================================================================
echo Three separate windows have been opened:
echo.
echo  1. Aria2 RPC Server    : http://localhost:6800/jsonrpc
echo  2. Backend API Server  : http://localhost:8001 (ProactorEventLoop FIXED)
echo  3. Frontend Web App    : http://localhost:8080
echo.
echo ================================================================
echo OPEN YOUR BROWSER AND GO TO: http://localhost:8080
echo ================================================================
echo.
echo To stop all services, run: Stop_App.bat
echo.
echo Press any key to close this launcher window...
echo (The 3 service windows will continue running)
pause >NUL

endlocal
exit /b 0
