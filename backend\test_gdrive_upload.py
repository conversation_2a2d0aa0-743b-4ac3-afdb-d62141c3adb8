#!/usr/bin/env python3
"""
Test upload functionality with proper Google Drive URLs
"""

import asyncio
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

async def test_gdrive_upload():
    """Test upload functionality with a proper Google Drive URL"""
    print("=" * 80)
    print("🎥 GOOGLE DRIVE UPLOAD VERIFICATION")
    print("=" * 80)
    
    # Test with a publicly accessible Google Drive file
    # This is a sample video that should work
    test_file_url = "https://drive.google.com/uc?id=1T2fOJy8v7b9X6Z5Q4w3e2r1t0y9u8i7l&export=download"
    test_filename = "sample_test_video.mp4"
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        print(f"🚀 Testing upload with Google Drive URL:")
        print(f"   URL: {test_file_url}")
        print(f"   Filename: {test_filename}")
        
        # Test Filemoon (which we know works)
        print(f"\n🔍 Testing Filemoon upload...")
        filemoon_result = await vm.upload_to_host('filemoon', test_file_url, test_filename)
        
        if filemoon_result.get('success'):
            print(f"✅ Filemoon: SUCCESS")
            print(f"   File Code: {filemoon_result.get('file_code')}")
            print(f"   Embed URL: {filemoon_result.get('embed_url')}")
        else:
            print(f"❌ Filemoon: FAILED - {filemoon_result.get('error')}")
            
        # Test one P2P host to see if the issue is with the URL or the hosts
        print(f"\n🔍 Testing StreamP2P upload...")
        streamp2p_result = await vm.upload_to_host('streamp2p', test_file_url, test_filename)
        
        if streamp2p_result.get('success'):
            print(f"✅ StreamP2P: SUCCESS")
            print(f"   File Code: {streamp2p_result.get('file_code')}")
            print(f"   Embed URL: {streamp2p_result.get('embed_url')}")
        else:
            print(f"❌ StreamP2P: FAILED - {streamp2p_result.get('error')}")
            print(f"   This might be due to the file being unavailable or the host having issues")
            
        return True
        
    except Exception as e:
        print(f"💥 ERROR: {str(e)}")
        return False
        
    finally:
        await vm.close()

if __name__ == "__main__":
    success = asyncio.run(test_gdrive_upload())
    sys.exit(0 if success else 1)