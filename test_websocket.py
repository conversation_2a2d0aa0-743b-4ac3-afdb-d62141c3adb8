#!/usr/bin/env python3
"""
Test WebSocket functionality
"""
import asyncio
import websockets
import json

async def test_websocket():
    """Test WebSocket connection"""
    print("Testing WebSocket connection...")
    
    try:
        # Connect to WebSocket endpoint
        uri = "ws://localhost:8001/ws"
        async with websockets.connect(uri) as websocket:
            print("PASS: WebSocket connected successfully")
            
            # Listen for a few messages
            for i in range(3):
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    print(f"Message {i+1}: {data.get('type', 'unknown')}")
                    if data.get('data'):
                        print(f"  Downloads: {len(data.get('data', []))}")
                except asyncio.TimeoutError:
                    print(f"Message {i+1}: Timeout (no message received)")
                except Exception as e:
                    print(f"Message {i+1}: Error - {e}")
            
            print("PASS: WebSocket communication working")
            return True
            
    except Exception as e:
        print(f"FAIL: WebSocket test error: {e}")
        return False

async def main():
    """Run WebSocket test"""
    print("Testing WebSocket Connection")
    print("=" * 40)
    
    result = await test_websocket()
    
    print("=" * 40)
    print(f"WebSocket Test: {'PASS' if result else 'FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
