# Google Drive Upload Manager adapted from mirror-leech-telegram-bot
import os
import logging
from pathlib import Path
from typing import Optional, Callable, List, Dict
# magic import is optional - handled in file_utils

from googleapiclient.http import MediaFileUpload
from tenacity import (
    retry,
    wait_exponential,
    stop_after_attempt,
    retry_if_exception_type,
    RetryError,
)

from core.gdrive_helper import GoogleDriveHelper
from config import Config
from utils.file_utils import get_mime_type

LOGGER = logging.getLogger(__name__)


class GoogleDriveUpload(GoogleDriveHelper):
    """Google Drive Upload class following mirror-leech-telegram-bot pattern"""
    
    def __init__(self, file_path: str, dest_folder_id: str, progress_callback: Optional[Callable] = None):
        super().__init__()
        self.file_path = file_path
        self.dest_folder_id = dest_folder_id
        self.progress_callback = progress_callback
        self._is_errored = False
        self._updater = None
        self.is_uploading = True
        self.service = self.authorize()

    async def upload(self) -> Optional[str]:
        """Upload file or directory to Google Drive"""
        try:
            if os.path.isfile(self.file_path):
                # Upload single file
                mime_type = get_mime_type(self.file_path)
                file_name = os.path.basename(self.file_path)
                link = self._upload_file(
                    self.file_path,
                    file_name,
                    mime_type,
                    self.dest_folder_id,
                    in_dir=False,
                )
                if link is None:
                    raise ValueError("Upload has been manually cancelled")
                LOGGER.info(f"Uploaded To G-Drive: {self.file_path}")
                return link
            else:
                # Upload directory
                dir_name = os.path.basename(os.path.abspath(self.file_path))
                dir_id = self.create_directory(dir_name, self.dest_folder_id)
                result = self._upload_dir(self.file_path, dir_id)
                if result is None:
                    raise ValueError("Upload has been manually cancelled!")
                link = self.G_DRIVE_DIR_BASE_DOWNLOAD_URL.format(dir_id)
                LOGGER.info(f"Uploaded To G-Drive: {dir_name}")
                return link
                
        except Exception as err:
            if isinstance(err, RetryError):
                LOGGER.info(f"Total Attempts: {err.last_attempt.attempt_number}")
                err = err.last_attempt.exception()
            err = str(err).replace(">", "").replace("<", "")
            LOGGER.error(err)
            self._is_errored = True
            raise err
        finally:
            if self._updater:
                self._updater.cancel()

    def _upload_dir(self, input_directory: str, dest_id: str) -> Optional[str]:
        """Upload directory recursively"""
        list_dirs = os.listdir(input_directory)
        if len(list_dirs) == 0:
            return dest_id
        
        new_id = None
        for item in list_dirs:
            current_file_name = os.path.join(input_directory, item)
            if os.path.isdir(current_file_name):
                current_dir_id = self.create_directory(item, dest_id)
                new_id = self._upload_dir(current_file_name, current_dir_id)
                self.total_folders += 1
            else:
                mime_type = get_mime_type(current_file_name)
                file_name = current_file_name.split("/")[-1]
                self._upload_file(current_file_name, file_name, mime_type, dest_id)
                self.total_files += 1
                new_id = dest_id
        return new_id

    @retry(
        wait=wait_exponential(multiplier=2, min=3, max=6),
        stop=stop_after_attempt(3),
        retry=retry_if_exception_type(Exception),
    )
    def _upload_file(self, file_path: str, file_name: str, mime_type: str, dest_id: str, in_dir: bool = True) -> Optional[str]:
        """Upload single file to Google Drive"""
        file_metadata = {
            "name": file_name,
            "description": "Uploaded by AutoUploadBot",
            "mimeType": mime_type,
        }
        if dest_id is not None:
            file_metadata["parents"] = [dest_id]

        file_size = os.path.getsize(file_path)
        
        if file_size == 0:
            # Handle empty files
            media_body = MediaFileUpload(file_path, mimetype=mime_type, resumable=False)
            response = (
                self.service.files()
                .create(
                    body=file_metadata, media_body=media_body, supportsAllDrives=True
                )
                .execute()
            )
            if not Config.IS_TEAM_DRIVE:
                self.set_permission(response["id"])
            return self.G_DRIVE_BASE_DOWNLOAD_URL.format(response.get("id"))
        
        # Handle regular files with resumable upload
        media_body = MediaFileUpload(
            file_path,
            mimetype=mime_type,
            resumable=True,
            chunksize=Config.CHUNK_SIZE
        )
        
        # Create the file
        drive_file = self.service.files().create(
            body=file_metadata,
            media_body=media_body,
            supportsAllDrives=True
        )
        
        response = None
        while response is None:
            try:
                self.status, response = drive_file.next_chunk()
                if self.progress_callback and self.status:
                    # Call progress callback (sync version)
                    if callable(self.progress_callback):
                        self.progress_callback(self.status.progress() * 100)
            except HttpError as e:
                if e.resp.status in [404, 500, 502, 503, 504]:
                    # Retry on server errors
                    continue
                else:
                    raise e
        
        if not Config.IS_TEAM_DRIVE:
            self.set_permission(response["id"])
        
        return self.G_DRIVE_BASE_DOWNLOAD_URL.format(response.get("id"))

    async def get_folder_contents(self, folder_id: str) -> List[Dict]:
        """Get contents of a Google Drive folder"""
        try:
            query = f"'{folder_id}' in parents and trashed=false"
            results = self.service.files().list(
                q=query,
                fields="files(id,name,size,mimeType,createdTime)",
                supportsAllDrives=True
            ).execute()
            
            return results.get('files', [])
        except Exception as e:
            LOGGER.error(f"Error getting folder contents: {e}")
            return []

    async def delete_file(self, file_id: str) -> bool:
        """Delete file from Google Drive"""
        try:
            self.service.files().delete(fileId=file_id, supportsAllDrives=True).execute()
            LOGGER.info(f"Deleted file: {file_id}")
            return True
        except Exception as e:
            LOGGER.error(f"Error deleting file {file_id}: {e}")
            return False
