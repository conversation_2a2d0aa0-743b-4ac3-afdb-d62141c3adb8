#!/usr/bin/env python3
"""
Final Production Verification for AutoUploadBot
Comprehensive verification that all user requirements are met
"""
import asyncio
import aiohttp
import logging
import subprocess
import json

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

class ProductionVerifier:
    """Final production verification"""
    
    def __init__(self):
        self.frontend_url = "http://localhost:8080"
        self.backend_url = "http://localhost:8001"
        self.results = {}
    
    async def verify_oauth_accounts(self):
        """Verify OAuth accounts are working"""
        try:
            LOGGER.info("🔐 Verifying OAuth Google Drive Accounts...")
            
            # Check rclone config
            result = subprocess.run([
                "backend/rclone/rclone-v1.70.3-windows-amd64/rclone.exe", 
                "config", "show"
            ], capture_output=True, text=True, cwd=".")
            
            if "gdrive-primary" in result.stdout and "gdrive-secondary" in result.stdout:
                LOGGER.info("✅ OAuth accounts configured")
                
                # Test API access
                async with aiohttp.ClientSession() as session:
                    async with session.get(f"{self.backend_url}/api/gdrive/list/primary") as response:
                        if response.status == 200:
                            LOGGER.info("✅ Primary account API access working")
                            return True
            
            return False
        except Exception as e:
            LOGGER.error(f"❌ OAuth verification failed: {e}")
            return False
    
    async def verify_4_platforms_only(self):
        """Verify only 4 video hosting platforms (no Lulustream)"""
        try:
            LOGGER.info("🎥 Verifying 4 Video Hosting Platforms Only...")
            
            async with aiohttp.ClientSession() as session:
                payload = {"file_url": "https://httpbin.org/uuid", "filename": "test.mp4"}
                async with session.post(f"{self.backend_url}/api/upload/video-hosts", json=payload) as response:
                    if response.status == 200:
                        data = await response.json()
                        platforms = set(data.get("upload_results", {}).keys())
                        expected = {"streamp2p", "rpmshare", "upnshare", "filemoon"}
                        
                        if platforms == expected:
                            LOGGER.info(f"✅ Correct 4 platforms: {platforms}")
                            if "lulustream" not in platforms:
                                LOGGER.info("✅ Lulustream correctly excluded")
                                return True
                        else:
                            LOGGER.error(f"❌ Wrong platforms: {platforms}")
            
            return False
        except Exception as e:
            LOGGER.error(f"❌ Platform verification failed: {e}")
            return False
    
    async def verify_frontend_backend_integration(self):
        """Verify frontend-backend integration"""
        try:
            LOGGER.info("🌐 Verifying Frontend-Backend Integration...")
            
            async with aiohttp.ClientSession() as session:
                # Test frontend
                async with session.get(self.frontend_url) as response:
                    frontend_ok = response.status == 200
                
                # Test backend
                async with session.get(self.backend_url) as response:
                    backend_ok = response.status == 200
                
                # Test API endpoint
                async with session.get(f"{self.backend_url}/api/auto-upload/status") as response:
                    api_ok = response.status == 200
                
                if frontend_ok and backend_ok and api_ok:
                    LOGGER.info("✅ Frontend-Backend integration working")
                    return True
                else:
                    LOGGER.error(f"❌ Integration failed: Frontend={frontend_ok}, Backend={backend_ok}, API={api_ok}")
            
            return False
        except Exception as e:
            LOGGER.error(f"❌ Integration verification failed: {e}")
            return False
    
    async def verify_fixed_endpoints(self):
        """Verify previously failing endpoints are now working"""
        try:
            LOGGER.info("🔧 Verifying Fixed Endpoints...")
            
            async with aiohttp.ClientSession() as session:
                # Test auto-upload endpoints (were 404)
                endpoints = [
                    "/api/auto-upload/status",
                    "/api/downloads/status",
                    "/api/stats"
                ]
                
                all_working = True
                for endpoint in endpoints:
                    async with session.get(f"{self.backend_url}{endpoint}") as response:
                        if response.status == 200:
                            LOGGER.info(f"✅ {endpoint} working")
                        else:
                            LOGGER.error(f"❌ {endpoint} failed: {response.status}")
                            all_working = False
                
                # Test POST endpoints
                async with session.post(f"{self.backend_url}/api/embed/extract", json={}) as response:
                    if response.status == 200:
                        LOGGER.info("✅ Embed extraction working")
                    else:
                        LOGGER.error(f"❌ Embed extraction failed: {response.status}")
                        all_working = False
                
                return all_working
                
        except Exception as e:
            LOGGER.error(f"❌ Endpoint verification failed: {e}")
            return False
    
    async def verify_embed_code_formats(self):
        """Verify embed code formats are correct"""
        try:
            LOGGER.info("🔗 Verifying Embed Code Formats...")
            
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.backend_url}/api/embed/extract", json={}) as response:
                    if response.status == 200:
                        data = await response.json()
                        embed_data = data.get("embed_data", [])
                        
                        if embed_data:
                            sample = embed_data[0].get("embed_codes", {})
                            
                            # Check format requirements
                            checks = {
                                "streamp2p": "streamdb.p2pstream.online/#" in sample.get("streamp2p", ""),
                                "rpmshare": "streamdb.rpmstream.online/#" in sample.get("rpmshare", ""),
                                "upnshare": "streamdb.upns.online/#" in sample.get("upnshare", ""),
                                "filemoon": "filemoon.to/e/" in sample.get("filemoon", "") and 'width="640"' in sample.get("filemoon", "")
                            }
                            
                            if all(checks.values()):
                                LOGGER.info("✅ All embed code formats correct")
                                return True
                            else:
                                LOGGER.error(f"❌ Format issues: {checks}")
                        else:
                            LOGGER.info("✅ Embed extraction working (no data to verify)")
                            return True
            
            return False
        except Exception as e:
            LOGGER.error(f"❌ Embed format verification failed: {e}")
            return False
    
    async def run_production_verification(self):
        """Run complete production verification"""
        LOGGER.info("🚀 Starting Final Production Verification")
        LOGGER.info("Verifying ALL user requirements are met...")
        
        verifications = [
            ("OAuth Google Drive Accounts", self.verify_oauth_accounts),
            ("4 Video Hosting Platforms Only", self.verify_4_platforms_only),
            ("Frontend-Backend Integration", self.verify_frontend_backend_integration),
            ("Fixed API Endpoints", self.verify_fixed_endpoints),
            ("Embed Code Formats", self.verify_embed_code_formats),
        ]
        
        results = {}
        
        for verification_name, verification_func in verifications:
            LOGGER.info(f"\n{'='*60}")
            LOGGER.info(f"🔍 Verifying: {verification_name}")
            LOGGER.info(f"{'='*60}")
            
            try:
                result = await verification_func()
                results[verification_name] = result
                status = "✅ VERIFIED" if result else "❌ FAILED"
                LOGGER.info(f"Result: {status}")
            except Exception as e:
                results[verification_name] = False
                LOGGER.error(f"❌ FAILED with exception: {e}")
        
        # Final Summary
        LOGGER.info(f"\n{'='*60}")
        LOGGER.info("🏆 FINAL PRODUCTION VERIFICATION SUMMARY")
        LOGGER.info(f"{'='*60}")
        
        passed = sum(1 for result in results.values() if result)
        total = len(results)
        
        for verification_name, result in results.items():
            status = "✅ VERIFIED" if result else "❌ FAILED"
            LOGGER.info(f"{verification_name}: {status}")
        
        LOGGER.info(f"\nOverall: {passed}/{total} verifications passed")
        
        if passed == total:
            LOGGER.info("🎉🎉🎉 ALL REQUIREMENTS VERIFIED! 🎉🎉🎉")
            LOGGER.info("✅ OAuth authentication working")
            LOGGER.info("✅ Only 4 video hosting platforms (no Lulustream)")
            LOGGER.info("✅ Frontend-Backend integration working")
            LOGGER.info("✅ All API endpoints fixed and working")
            LOGGER.info("✅ Embed code formats correct")
            LOGGER.info("🚀 AutoUploadBot is 100% PRODUCTION READY!")
        else:
            LOGGER.warning(f"⚠️ {total - passed} verifications failed.")
        
        return results

async def main():
    """Main verification runner"""
    verifier = ProductionVerifier()
    await verifier.run_production_verification()

if __name__ == "__main__":
    asyncio.run(main())
