#!/usr/bin/env python3
"""
Test script to verify the Drive Cleanup deletion fix
"""

import asyncio
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

import requests
import json

def test_file_mapping():
    """Test that file ID to name mapping works correctly"""
    print("🔍 TESTING FILE ID TO NAME MAPPING")
    print("=" * 50)
    
    try:
        # Get file listings from both drives
        primary_response = requests.get("http://localhost:8001/api/gdrive/list/primary", timeout=10)
        secondary_response = requests.get("http://localhost:8001/api/gdrive/list/secondary", timeout=10)
        
        print(f"Primary drive status: {primary_response.status_code}")
        print(f"Secondary drive status: {secondary_response.status_code}")
        
        if primary_response.status_code == 200:
            primary_data = primary_response.json()
            print(f"Primary files: {len(primary_data.get('files', []))}")
            for file in primary_data.get('files', [])[:3]:  # Show first 3 files
                print(f"  - {file.get('name')} (ID: {file.get('id')})")
                
        if secondary_response.status_code == 200:
            secondary_data = secondary_response.json()
            print(f"Secondary files: {len(secondary_data.get('files', []))}")
            for file in secondary_data.get('files', [])[:3]:  # Show first 3 files
                print(f"  - {file.get('name')} (ID: {file.get('id')})")
                
        print("✅ File mapping test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error in file mapping test: {e}")
        return False

def test_deletion_api():
    """Test the deletion API with dummy data to check structure"""
    print("\n🔍 TESTING DELETION API STRUCTURE")
    print("=" * 50)
    
    try:
        # Test with empty file list (should return error)
        test_data = {
            "file_ids": [],
            "drive_type": "both",
            "permanent_delete": True
        }
        
        response = requests.delete(
            "http://localhost:8001/api/gdrive/cleanup",
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=10
        )
        print(f"Empty file list test - Status: {response.status_code}")
        
        if response.status_code == 400:
            data = response.json()
            print(f"✅ Correctly rejected empty file list: {data.get('detail', 'No detail')}")
        else:
            print(f"⚠️  Unexpected status for empty file list: {response.status_code}")
            
        print("✅ Deletion API structure test completed")
        return True
        
    except Exception as e:
        print(f"❌ Error in deletion API test: {e}")
        return False

if __name__ == "__main__":
    print("Running Drive Cleanup Deletion Fix Tests...")
    
    # Test file mapping
    mapping_success = test_file_mapping()
    
    # Test deletion API structure
    api_success = test_deletion_api()
    
    overall_success = mapping_success and api_success
    print(f"\n{'='*50}")
    if overall_success:
        print("✅ ALL TESTS PASSED - Deletion fix is ready")
    else:
        print("❌ SOME TESTS FAILED - Check implementation")
    print(f"{'='*50}")
    
    exit(0 if overall_success else 1)