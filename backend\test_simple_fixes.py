#!/usr/bin/env python3
"""
Simple test for the download fixes without Google Drive dependency
"""
import asyncio
import logging
from core.rclone_manager import TorrentToHttpService

# Setup logging
logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)

async def test_torrent_conversion():
    """Test torrent-to-URL conversion only"""
    print("="*50)
    print("TESTING TORRENT CONVERSION SERVICE")
    print("="*50)
    
    service = TorrentToHttpService()
    
    # Test magnet link
    test_magnet = "magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=Americas.Team.The.Gambler.and.His.Cowboys.S01E01.1080p.AV1.10bit-MeGusta%20%5Bext.to%5D&tr=udp%3A%2F%2Ftracker.coppersurfer.tk%3A6969%2Fannounce"
    
    print(f"Testing magnet: {test_magnet[:80]}...")
    
    try:
        result = await service.convert_magnet_to_urls(test_magnet)
        
        if result["success"]:
            print("SUCCESS: Conversion successful!")
            print(f"   Service: {result.get('service', 'unknown')}")
            print(f"   Info Hash: {result['info_hash']}")
            print(f"   Name: {result['name']}")
            print(f"   Files: {result['total_files']}")
            
            for i, file_info in enumerate(result["files"][:3]):  # Show first 3 files
                print(f"   File {i+1}: {file_info['name']} ({file_info['size']} bytes)")
                print(f"           URL: {file_info['download_url']}")
                
            return True
        else:
            print(f"FAILED: Conversion failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"ERROR: Exception during conversion: {e}")
        return False

async def test_rclone_command_generation():
    """Test RClone command generation without execution"""
    print("\n" + "="*50)
    print("TESTING RCLONE COMMAND GENERATION")
    print("="*50)
    
    from core.rclone_manager import RcloneManager
    
    rclone_manager = RcloneManager()
    
    print(f"RClone path: {rclone_manager.rclone_path}")
    
    # Test URL
    test_url = "https://httpbin.org/json"
    test_folder = "test_folder_id"
    test_filename = "test_file.json"
    
    # Simulate the command generation logic
    import re
    
    # Clean filename
    clean_filename = re.sub(r'[<>:"/\\|?*]', '_', test_filename)
    
    # Generate command
    rclone_cmd = [
        rclone_manager.rclone_path,
        "copyurl",
        test_url,
        f"gdrive:{test_folder}/{clean_filename}",
        "--progress",
        "--stats", "1s",
        "--transfers", "1",
        "--retries", "3",
        "--low-level-retries", "10",
        "--timeout", "300s",
        "--contimeout", "60s",
        "--verbose"
    ]
    
    print("Generated RClone command:")
    print(" ".join(rclone_cmd))
    print("\nCommand looks correct!")
    return True

async def main():
    """Run simple tests"""
    print("AutoUploadBot Download Fixes - Simple Test")
    print("="*50)
    
    # Test torrent conversion
    torrent_success = await test_torrent_conversion()
    
    # Test command generation
    command_success = await test_rclone_command_generation()
    
    print("\n" + "="*50)
    print("SIMPLE TEST RESULTS")
    print("="*50)
    print(f"Torrent Conversion: {'PASS' if torrent_success else 'FAIL'}")
    print(f"RClone Commands: {'PASS' if command_success else 'FAIL'}")
    
    if torrent_success and command_success:
        print("\nSUCCESS: Core fixes are working!")
        print("Next step: Fix Google Drive service account permissions")
    else:
        print("\nFAILED: Some fixes need more work")

if __name__ == "__main__":
    asyncio.run(main())
