# Aria2 Manager adapted from mirror-leech-telegram-bot
import asyncio
import logging
from typing import Optional, Dict, Any, Callable
from pathlib import Path
import base64

from aioaria2 import Aria2WebsocketClient
from aiohttp.client_exceptions import ClientError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from config import Config

LOGGER = logging.getLogger(__name__)


def wrap_with_retry(obj, max_retries=3):
    """Wrap aria2 client methods with retry logic"""
    for attr_name in dir(obj):
        if attr_name.startswith("_"):
            continue

        attr = getattr(obj, attr_name)
        if asyncio.iscoroutinefunction(attr):
            retry_policy = retry(
                stop=stop_after_attempt(max_retries),
                wait=wait_exponential(multiplier=1, min=1, max=5),
                retry=retry_if_exception_type(
                    (ClientError, asyncio.TimeoutError, RuntimeError)
                ),
            )
            wrapped = retry_policy(attr)
            setattr(obj, attr_name, wrapped)
    return obj


class Aria2Manager:
    """Aria2 Manager following mirror-leech-telegram-bot pattern"""
    
    def __init__(self):
        self.aria2 = None
        self.downloads: Dict[str, Dict] = {}
        
    async def initialize(self):
        """Initialize Aria2 WebSocket client"""
        try:
            aria2_url = Config.get_aria2_url()
            self.aria2 = await Aria2WebsocketClient.new(aria2_url)
            self.aria2 = wrap_with_retry(self.aria2)
            LOGGER.info(f"Aria2 client initialized: {aria2_url}")
        except Exception as e:
            LOGGER.error(f"Failed to initialize Aria2: {e}")
            raise e

    async def close(self):
        """Close Aria2 connection"""
        if self.aria2:
            await self.aria2.close()

    async def add_download(
        self,
        url: str,
        gdrive_folder_id: str,
        filename: Optional[str] = None,
        headers: Optional[Dict] = None,
        progress_callback: Optional[Callable] = None
    ) -> str:
        """Add download directly to Google Drive using rclone (server-to-server) - FULL RCLONE IMPLEMENTATION"""

        # Prepare aria2 options for direct Google Drive upload via rclone
        a2c_opt = {
            "dir": f"gdrive:{gdrive_folder_id}",  # Direct Google Drive upload via rclone
            "file-allocation": "none",
            "continue": "true",
            "max-connection-per-server": "4",
            "split": "4"
        }

        if filename:
            a2c_opt["out"] = filename

        if headers:
            header_list = [f"{k}: {v}" for k, v in headers.items()]
            a2c_opt["header"] = header_list

        try:
            if url.startswith("magnet:"):
                # Handle magnet links
                gid = await self.aria2.addUri(uris=[url], options=a2c_opt)
            elif Path(url).exists():
                # Handle torrent files
                with open(url, "rb") as tf:
                    torrent = tf.read()
                encoded = base64.b64encode(torrent).decode()
                params = [encoded, [], a2c_opt]
                gid = await self.aria2.jsonrpc("addTorrent", params)
            else:
                # Handle direct URLs
                gid = await self.aria2.addUri(uris=[url], options=a2c_opt)

            # Store download info
            self.downloads[gid] = {
                "url": url,
                "gdrive_folder_id": gdrive_folder_id,
                "filename": filename,
                "progress_callback": progress_callback,
                "status": "active"
            }

            LOGGER.info(f"Added download: {url} -> {gid} (rclone: gdrive:{gdrive_folder_id})")
            return gid

        except Exception as e:
            LOGGER.error(f"Aria2 Download Error: {e}")
            raise e

    async def add_magnet(self, magnet_uri: str) -> str:
        """Add magnet link download"""
        try:
            # Check if Aria2 is initialized
            if not self.aria2:
                raise Exception("Aria2 client not initialized")

            # Use aria2 to add magnet directly
            a2c_opt = {
                "file-allocation": "none",
                "continue": "true",
                "max-connection-per-server": "4",
                "split": "4"
            }

            gid = await self.aria2.addUri(uris=[magnet_uri], options=a2c_opt)

            # Store download info
            self.downloads[gid] = {
                "url": magnet_uri,
                "status": "active",
                "type": "magnet"
            }

            LOGGER.info(f"Added magnet download: {magnet_uri} -> {gid}")
            return gid

        except Exception as e:
            LOGGER.error(f"Aria2 Magnet Error: {e}")
            raise e

    async def get_download_info(self, gid: str) -> Optional[Dict]:
        """Get download information by GID"""
        try:
            # Check if Aria2 is initialized
            if not self.aria2:
                LOGGER.warning("Aria2 client not initialized")
                return None

            # Get download status from aria2
            status = await self.aria2.tellStatus(gid)
            return status
        except Exception as e:
            LOGGER.error(f"Failed to get download info for {gid}: {e}")
            return None



    async def get_download_status(self, gid: str) -> Optional[Dict]:
        """Get download status"""
        try:
            status = await self.aria2.tellStatus(gid)
            return status
        except Exception as e:
            LOGGER.error(f"Error getting download status for {gid}: {e}")
            return None

    async def get_download_progress(self, gid: str) -> Dict[str, Any]:
        """Get download progress information"""
        status = await self.get_download_status(gid)
        if not status:
            return {}

        try:
            completed = int(status.get("completedLength", "0"))
            total = int(status.get("totalLength", "0"))
            speed = int(status.get("downloadSpeed", "0"))
            
            progress_percent = (completed / total * 100) if total > 0 else 0
            eta = (total - completed) / speed if speed > 0 else 0
            
            return {
                "gid": gid,
                "status": status.get("status", "unknown"),
                "progress": round(progress_percent, 2),
                "completed_length": completed,
                "total_length": total,
                "download_speed": speed,
                "eta": eta,
                "filename": self._get_filename_from_status(status)
            }
        except Exception as e:
            LOGGER.error(f"Error calculating progress for {gid}: {e}")
            return {}

    def _get_filename_from_status(self, status: Dict) -> str:
        """Extract filename from aria2 status"""
        try:
            if status.get("bittorrent"):
                return status["bittorrent"]["info"]["name"]
            elif status.get("files") and len(status["files"]) > 0:
                file_path = status["files"][0]["path"]
                return Path(file_path).name
            else:
                return "Unknown"
        except:
            return "Unknown"

    async def pause_download(self, gid: str) -> bool:
        """Pause download"""
        try:
            await self.aria2.pause(gid)
            if gid in self.downloads:
                self.downloads[gid]["status"] = "paused"
            return True
        except Exception as e:
            LOGGER.error(f"Error pausing download {gid}: {e}")
            return False

    async def resume_download(self, gid: str) -> bool:
        """Resume download"""
        try:
            await self.aria2.unpause(gid)
            if gid in self.downloads:
                self.downloads[gid]["status"] = "active"
            return True
        except Exception as e:
            LOGGER.error(f"Error resuming download {gid}: {e}")
            return False

    async def remove_download(self, gid: str, force: bool = False) -> bool:
        """Remove download"""
        try:
            if force:
                await self.aria2.forceRemove(gid)
            else:
                await self.aria2.remove(gid)
            
            if gid in self.downloads:
                del self.downloads[gid]
            return True
        except Exception as e:
            LOGGER.error(f"Error removing download {gid}: {e}")
            return False

    async def get_active_downloads(self) -> list[Dict]:
        """Get all active downloads"""
        try:
            active = await self.aria2.tellActive()
            waiting = await self.aria2.tellWaiting(0, 100)
            stopped = await self.aria2.tellStopped(0, 100)

            all_downloads = []
            for download_list in [active, waiting, stopped]:
                for download in download_list:
                    progress_info = await self.get_download_progress(download["gid"])
                    all_downloads.append(progress_info)

            return all_downloads
        except Exception as e:
            LOGGER.error(f"Error getting active downloads: {e}")
            return []
