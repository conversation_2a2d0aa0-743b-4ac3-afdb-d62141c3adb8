import asyncio
import logging
from api.video_hosts import VideoHostManager

logging.basicConfig(level=logging.INFO)

DRIVE_LINK = "https://drive.google.com/file/d/1IIWullTqoM7r5LWwMyYMQCXa2-AOgaNO/view?usp=sharing"
FILENAME = "drive_remote_test_newlink.mkv"

async def main():
    mgr = VideoHostManager()
    await mgr.initialize()
    try:
        # Run uploads sequentially with polling
        results = {}
        for host in ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']:
            print(f"Uploading to {host}...")
            res = await mgr.upload_to_host(host, DRIVE_LINK, FILENAME)
            print(f"Result for {host}: {res}\n")
            results[host] = res
        print("All done")
    finally:
        await mgr.close()

if __name__ == '__main__':
    asyncio.run(main())
