import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Download, Upload, Activity, RefreshCw } from "lucide-react";
import { apiService, DownloadStatus } from "@/services/api";

const ProgressMonitor = () => {
  const [downloads, setDownloads] = useState<DownloadStatus[]>([]);
  const [uploads, setUploads] = useState<any[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [wsConnected, setWsConnected] = useState(false);

  // Load data on component mount
  useEffect(() => {
    loadData();

    // Set up WebSocket connection for real-time updates
    const ws = new WebSocket('ws://localhost:8006/ws');

    ws.onopen = () => {
      setWsConnected(true);
      console.log('WebSocket connected');
    };

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'download_progress') {
        loadData(); // Refresh data when progress updates
      }
    };

    ws.onclose = () => {
      setWsConnected(false);
      console.log('WebSocket disconnected');
    };

    // Set up periodic refresh as fallback
    const interval = setInterval(loadData, 5000);

    return () => {
      ws.close();
      clearInterval(interval);
    };
  }, []);

  const loadData = async () => {
    try {
      const response = await apiService.getDownloadsStatus();
      setDownloads(response.downloads || []);

      // TODO: Load upload status when API is available
      // const uploadResponse = await apiService.getUploadsStatus();
      // setUploads(uploadResponse.uploads || []);
    } catch (error) {
      console.error("Failed to load progress data:", error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await loadData();
    setIsRefreshing(false);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatSpeed = (bytesPerSec: number): string => {
    return formatFileSize(bytesPerSec) + "/s";
  };

  const activeDownloads = downloads.filter(d => d.status === 'active');
  const activeUploads = uploads.filter(u => u.status === 'active');

  const stats = {
    activeDownloads: activeDownloads.length,
    activeUploads: activeUploads.length,
    totalActive: activeDownloads.length + activeUploads.length
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold font-mono mb-2">PROGRESS MONITOR</h2>
        <p className="text-muted-foreground font-mono">REAL-TIME MONITORING • SPEED TRACKING • ETA CALCULATIONS</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Download className="w-10 h-10 mx-auto mb-4 text-primary" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.activeDownloads}</div>
            <div className="text-sm font-mono text-muted-foreground">ACTIVE DOWNLOADS</div>
            <div className="w-full bg-muted brutal-border h-3 mt-3">
              <div className="bg-primary h-full" style={{ width: '65%' }} />
            </div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Upload className="w-10 h-10 mx-auto mb-4 text-accent" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.activeUploads}</div>
            <div className="text-sm font-mono text-muted-foreground">ACTIVE UPLOADS</div>
            <div className="w-full bg-muted brutal-border h-3 mt-3">
              <div className="bg-accent h-full" style={{ width: '78%' }} />
            </div>
          </CardContent>
        </Card>

        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Activity className="w-10 h-10 mx-auto mb-4 text-purple" />
            <div className="text-4xl font-bold font-mono mb-2">{stats.totalActive}</div>
            <div className="text-sm font-mono text-muted-foreground">TOTAL ACTIVE</div>
            <Button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="brutal-button mt-3"
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`w-3 h-3 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              REFRESH
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Active Downloads */}
      <Card className="brutal-card">
        <CardHeader className="bg-primary text-primary-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">ACTIVE DOWNLOADS</CardTitle>
        </CardHeader>
        <CardContent className="p-4">
          {activeDownloads.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground font-mono">
              NO ACTIVE DOWNLOADS
            </div>
          ) : (
            activeDownloads.map((download) => (
              <div key={download.gid} className="brutal-card p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="bg-primary text-primary-foreground px-2 py-1 text-xs font-bold brutal-border">
                    DOWNLOADING
                  </div>
                  <span className="font-mono font-bold">{download.filename}</span>
                </div>
                <div className="text-right font-mono text-sm">
                  {download.download_speed > 0 && (
                    <div className="text-primary font-bold">{formatSpeed(download.download_speed)}</div>
                  )}
                  {download.eta !== "unknown" && (
                    <div className="text-muted-foreground">ETA: {download.eta}</div>
                  )}
                </div>
              </div>
              <div className="text-sm text-muted-foreground font-mono mb-2">
                Size: {formatFileSize(download.total_length)} • Downloaded: {formatFileSize(download.completed_length)}
              </div>
              <div className="flex justify-between text-xs font-mono mb-1">
                <span>Progress: {download.progress.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-muted brutal-border h-4">
                <div
                  className="bg-primary h-full transition-all duration-300"
                  style={{ width: `${Math.min(download.progress, 100)}%` }}
                />
              </div>
            </div>
            ))
          )}
        </CardContent>
      </Card>

      {/* Active Uploads */}
      <Card className="brutal-card">
        <CardHeader className="bg-accent text-accent-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg">ACTIVE UPLOADS</CardTitle>
        </CardHeader>
        <CardContent className="p-4 space-y-4">
          {activeUploads.map((upload) => (
            <div key={upload.id} className="brutal-card p-4">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-3">
                  <div className="bg-accent text-accent-foreground px-2 py-1 text-xs font-bold brutal-border">
                    UPLOADING
                  </div>
                  <div className="bg-orange text-orange-foreground px-2 py-1 text-xs font-bold brutal-border">
                    {upload.platform}
                  </div>
                </div>
                <div className="text-right font-mono text-sm">
                  <div className="text-accent font-bold">{upload.speed}</div>
                  <div className="text-muted-foreground">ETA: {upload.eta}</div>
                </div>
              </div>
              <div className="font-mono font-bold mb-1">{upload.name}</div>
              <div className="text-sm text-muted-foreground font-mono mb-2">
                Size: {upload.size} • Source: {upload.drive} drive
              </div>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-mono">{upload.platform}</span>
                  <span className="text-sm font-mono">{upload.progress}%</span>
                </div>
                <div className="w-full bg-muted brutal-border h-3">
                  <div 
                    className="bg-accent h-full transition-all duration-300" 
                    style={{ width: `${upload.progress}%` }}
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
};

export default ProgressMonitor;