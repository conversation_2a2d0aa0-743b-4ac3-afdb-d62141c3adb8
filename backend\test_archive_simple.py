# Simple archive extraction test - Windows compatible
import os
import sys
import tempfile
import zipfile
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)

def test_dependencies():
    """Test archive extraction dependencies"""
    print("Testing Archive Extraction Dependencies...")
    print("=" * 50)
    
    deps = [
        ("zipfile", "Built-in ZIP support"),
        ("py7zr", "7-Zip archive support"),
        ("rarfile", "RAR archive support"),
        ("tarfile", "TAR archive support"),
        ("gzip", "GZ compression support")
    ]
    
    failed = []
    
    for dep, desc in deps:
        try:
            __import__(dep)
            print(f"OK: {dep} - {desc}")
        except ImportError as e:
            print(f"FAIL: {dep} - {desc} - ERROR: {e}")
            failed.append(dep)
    
    if failed:
        print(f"\nCRITICAL ERROR: Missing dependencies: {failed}")
        return False
    else:
        print("\nAll archive extraction dependencies available!")
        return True

def test_zip_extraction():
    """Test ZIP file extraction"""
    print("\nTesting ZIP Extraction...")
    
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test files
            test_files = [
                ("video1.mp4", b"fake video content 1"),
                ("video2.mkv", b"fake video content 2"),
                ("readme.txt", b"this is a readme file")
            ]
            
            # Create files
            for filename, content in test_files:
                filepath = os.path.join(temp_dir, filename)
                with open(filepath, 'wb') as f:
                    f.write(content)
            
            # Create ZIP file
            zip_path = os.path.join(temp_dir, "test.zip")
            with zipfile.ZipFile(zip_path, 'w') as zip_ref:
                for filename, _ in test_files:
                    file_path = os.path.join(temp_dir, filename)
                    zip_ref.write(file_path, filename)
            
            # Extract ZIP
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            # Check extracted files
            extracted = os.listdir(extract_dir)
            video_files = [f for f in extracted if f.endswith(('.mp4', '.mkv', '.avi'))]
            
            print(f"Extracted {len(extracted)} files")
            print(f"Found {len(video_files)} video files: {video_files}")
            
            if len(video_files) >= 2:
                print("ZIP extraction test PASSED!")
                return True
            else:
                print("ZIP extraction test FAILED!")
                return False
                
    except Exception as e:
        print(f"ZIP extraction test FAILED: {e}")
        return False

def test_py7zr():
    """Test py7zr functionality"""
    print("\nTesting py7zr (7-Zip)...")
    
    try:
        import py7zr
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create test file
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test content")
            
            # Create 7z archive
            archive_path = os.path.join(temp_dir, "test.7z")
            with py7zr.SevenZipFile(archive_path, 'w') as archive:
                archive.write(test_file, "test.txt")
            
            # Extract 7z archive
            extract_dir = os.path.join(temp_dir, "extracted")
            os.makedirs(extract_dir)
            
            with py7zr.SevenZipFile(archive_path, 'r') as archive:
                archive.extractall(extract_dir)
            
            # Verify extraction
            extracted_file = os.path.join(extract_dir, "test.txt")
            if os.path.exists(extracted_file):
                print("py7zr (7-Zip) test PASSED!")
                return True
            else:
                print("py7zr (7-Zip) test FAILED!")
                return False
                
    except Exception as e:
        print(f"py7zr (7-Zip) test FAILED: {e}")
        return False

def test_rarfile():
    """Test rarfile functionality"""
    print("\nTesting rarfile (RAR)...")
    
    try:
        import rarfile
        print("rarfile module imported successfully!")
        
        if rarfile.UNRAR_TOOL:
            print(f"RAR extraction tool found: {rarfile.UNRAR_TOOL}")
        else:
            print("RAR extraction tool not found - RAR files may not extract")
            print("This is OK for now, other formats will work")
        
        return True
        
    except Exception as e:
        print(f"rarfile test FAILED: {e}")
        return False

def main():
    """Main test function"""
    print("ARCHIVE EXTRACTION CORE FEATURE TEST")
    print("=" * 50)
    
    tests = [
        ("Dependencies Check", test_dependencies),
        ("ZIP Extraction", test_zip_extraction),
        ("7-Zip Support", test_py7zr),
        ("RAR Support", test_rarfile)
    ]
    
    passed = 0
    failed = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * 30)
        try:
            if test_func():
                passed += 1
                print(f"RESULT: {test_name} PASSED")
            else:
                failed.append(test_name)
                print(f"RESULT: {test_name} FAILED")
        except Exception as e:
            failed.append(test_name)
            print(f"RESULT: {test_name} FAILED with exception: {e}")
    
    print("\n" + "="*50)
    print("ARCHIVE EXTRACTION TEST RESULTS")
    print("="*50)
    print(f"Passed: {passed}/{len(tests)}")
    
    if failed:
        print(f"Failed: {failed}")
        print("\nCRITICAL: Archive extraction has issues!")
        return False
    else:
        print("ALL ARCHIVE EXTRACTION TESTS PASSED!")
        print("Archive extraction CORE FEATURE is working correctly!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
