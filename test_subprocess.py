#!/usr/bin/env python3
"""
Test subprocess functionality directly
"""
import asyncio
import platform
import subprocess
from pathlib import Path

async def test_subprocess():
    """Test if subprocess works with ProactorEventLoop"""
    print("Testing subprocess functionality...")
    print(f"Platform: {platform.system()}")
    print(f"Event Loop: {type(asyncio.get_event_loop())}")
    
    # Test simple command
    try:
        if platform.system() == "Windows":
            # Test Windows subprocess
            process = await asyncio.create_subprocess_exec(
                "cmd", "/c", "echo", "Hello World",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        else:
            # Test Unix subprocess
            process = await asyncio.create_subprocess_exec(
                "echo", "Hello World",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
        
        stdout, stderr = await process.communicate()
        print(f"Return code: {process.returncode}")
        print(f"Stdout: {stdout.decode()}")
        print(f"Stderr: {stderr.decode()}")
        
        if process.returncode == 0:
            print("✅ Subprocess test PASSED")
            return True
        else:
            print("❌ Subprocess test FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Subprocess test FAILED with error: {e}")
        return False

async def test_rclone_command():
    """Test rclone command specifically"""
    print("\nTesting rclone command...")
    
    rclone_path = Path("backend/rclone/rclone-v1.70.3-windows-amd64/rclone.exe")
    
    if not rclone_path.exists():
        print(f"❌ RClone not found at: {rclone_path}")
        return False
    
    try:
        # Test rclone version command
        process = await asyncio.create_subprocess_exec(
            str(rclone_path), "version",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=str(rclone_path.parent)
        )
        
        stdout, stderr = await process.communicate()
        print(f"Return code: {process.returncode}")
        print(f"Stdout: {stdout.decode()[:200]}...")  # First 200 chars
        
        if process.returncode == 0:
            print("✅ RClone test PASSED")
            return True
        else:
            print("❌ RClone test FAILED")
            print(f"Stderr: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ RClone test FAILED with error: {e}")
        return False

async def main():
    """Run all tests"""
    print("AutoUploadBot Subprocess Testing")
    print("=" * 40)
    
    # Set ProactorEventLoop for Windows
    if platform.system() == "Windows":
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        print("Set Windows ProactorEventLoop policy")
    
    # Run tests
    test1 = await test_subprocess()
    test2 = await test_rclone_command()
    
    print("\n" + "=" * 40)
    print("TEST RESULTS:")
    print(f"Basic Subprocess: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"RClone Command: {'✅ PASS' if test2 else '❌ FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
