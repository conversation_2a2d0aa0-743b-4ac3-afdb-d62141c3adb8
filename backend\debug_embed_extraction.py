#!/usr/bin/env python3
"""
Comprehensive debug script for embed code extraction issues
This script tests each component individually to identify root causes
"""

import asyncio
import logging
import sys
import json
from datetime import datetime, timedelta
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager
from config import Config

# Configure detailed logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

async def debug_filemoon_api():
    """Debug FileMoon API responses in detail"""
    print("\n🌙 DEBUGGING FILEMOON API")
    print("=" * 60)
    
    video_manager = VideoHostManager()
    await video_manager.initialize()
    
    try:
        api_key = Config.FILEMOON_API_KEY
        base_url = video_manager.base_urls['filemoon']
        
        print(f"API Key: {api_key[:10]}..." if api_key else "❌ NO API KEY")
        print(f"Base URL: {base_url}")
        
        # Test raw API call
        url = f"{base_url}/api/file/list"
        params = {
            'key': api_key,
            'per_page': 5,
            'page': 1
        }
        
        print(f"\n📡 Raw API Request:")
        print(f"URL: {url}")
        print(f"Params: {params}")
        
        async with video_manager.session.get(url, params=params) as response:
            print(f"Status: {response.status}")
            
            if response.status == 200:
                data = await response.json()
                print(f"\n📄 Raw API Response:")
                print(json.dumps(data, indent=2)[:1000] + "..." if len(str(data)) > 1000 else json.dumps(data, indent=2))
                
                # Test file extraction logic
                print(f"\n🔍 Testing File Extraction Logic:")
                files_on_page = []
                
                if isinstance(data, dict):
                    if data.get('status') == 200 and 'result' in data:
                        result = data['result']
                        if isinstance(result, dict) and 'files' in result:
                            files_on_page = result['files']
                            print(f"✅ Found files in result.files: {len(files_on_page)}")
                        elif isinstance(result, list):
                            files_on_page = result
                            print(f"✅ Found files in result (list): {len(files_on_page)}")
                    elif 'files' in data:
                        files_on_page = data['files']
                        print(f"✅ Found files in data.files: {len(files_on_page)}")
                elif isinstance(data, list):
                    files_on_page = data
                    print(f"✅ Found files in data (list): {len(files_on_page)}")
                
                # Test file code extraction
                print(f"\n🏷️ Testing File Code Extraction:")
                for i, file_info in enumerate(files_on_page[:3]):
                    print(f"\nFile {i+1}:")
                    print(f"  Raw file_info: {file_info}")
                    
                    file_code = (
                        file_info.get('file_code') or 
                        file_info.get('filecode') or 
                        file_info.get('code') or 
                        file_info.get('id') or ''
                    )
                    
                    filename = (
                        file_info.get('name') or 
                        file_info.get('title') or 
                        file_info.get('filename') or ''
                    )
                    
                    upload_date = (
                        file_info.get('uploaded') or 
                        file_info.get('uploaded_at') or 
                        file_info.get('created_at') or 
                        file_info.get('date') or ''
                    )
                    
                    print(f"  Extracted file_code: '{file_code}'")
                    print(f"  Extracted filename: '{filename}'")
                    print(f"  Extracted upload_date: '{upload_date}'")
                    
                    if not file_code or file_code == 'code':
                        print(f"  ❌ INVALID FILE CODE: '{file_code}'")
                    else:
                        print(f"  ✅ Valid file code")
                        
                        # Generate embed code
                        embed_code = f'<iframe src="https://filemoon.to/e/{file_code}/{filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
                        print(f"  Generated embed: {embed_code[:100]}...")
            else:
                error_text = await response.text()
                print(f"❌ API Error: {error_text}")
                
    except Exception as e:
        print(f"❌ FileMoon debug failed: {e}")
    finally:
        await video_manager.close()

async def debug_p2p_hosts():
    """Debug P2P hosts API responses"""
    print("\n🌐 DEBUGGING P2P HOSTS APIs")
    print("=" * 60)
    
    video_manager = VideoHostManager()
    await video_manager.initialize()
    
    try:
        hosts = ['streamp2p', 'rpmshare', 'upnshare']
        
        for host in hosts:
            print(f"\n🔍 Testing {host.upper()}:")
            print("-" * 40)
            
            api_key = video_manager.api_keys.get(host)
            if not api_key:
                print(f"❌ No API key for {host}")
                continue
                
            print(f"API Key: {api_key[:10]}..." if api_key else "❌ NO API KEY")
            
            url = video_manager._build_p2p_api_url(host, 'video/manage')
            headers = {
                'api-token': api_key,
                'Content-Type': 'application/json',
                'User-Agent': 'AutoUploadBot/1.0'
            }
            
            params = {
                'perPage': 5,
                'page': 1
            }
            
            print(f"URL: {url}")
            print(f"Headers: {headers}")
            print(f"Params: {params}")
            
            try:
                async with video_manager.session.get(url, headers=headers, params=params) as response:
                    print(f"Status: {response.status}")
                    
                    if response.status == 200:
                        data = await response.json()
                        print(f"Raw Response: {json.dumps(data, indent=2)[:500]}...")
                        
                        # Test video extraction
                        videos_on_page = []
                        if isinstance(data, dict):
                            for key in ['data', 'videos', 'result']:
                                if key in data and isinstance(data[key], list):
                                    videos_on_page = data[key]
                                    print(f"✅ Found videos in {key}: {len(videos_on_page)}")
                                    break
                        elif isinstance(data, list):
                            videos_on_page = data
                            print(f"✅ Found videos in root list: {len(videos_on_page)}")
                        
                        # Test file ID extraction
                        for i, video in enumerate(videos_on_page[:2]):
                            print(f"\nVideo {i+1}:")
                            print(f"  Raw video: {video}")
                            
                            file_id = (
                                video.get('id') or video.get('video_id') or 
                                video.get('_id') or video.get('videoId') or ''
                            )
                            
                            filename = (
                                video.get('name') or video.get('title') or 
                                video.get('filename') or ''
                            )
                            
                            print(f"  Extracted file_id: '{file_id}'")
                            print(f"  Extracted filename: '{filename}'")
                            
                            if not file_id:
                                print(f"  ❌ INVALID FILE ID")
                            else:
                                print(f"  ✅ Valid file ID")
                    else:
                        error_text = await response.text()
                        print(f"❌ API Error: {error_text}")
                        
            except Exception as e:
                print(f"❌ {host} API test failed: {e}")
                
    except Exception as e:
        print(f"❌ P2P debug failed: {e}")
    finally:
        await video_manager.close()

async def debug_time_filtering():
    """Debug time filtering logic"""
    print("\n⏰ DEBUGGING TIME FILTERING")
    print("=" * 60)
    
    # Test date parsing
    test_dates = [
        "2025-01-22 15:30:00",
        "2025-01-22T15:30:00Z",
        "2025-01-22",
        "22/01/2025",
        "01/22/2025",
        "1642861800",  # Unix timestamp
        "",  # Empty date
        None  # No date
    ]
    
    cutoff_time = datetime.now() - timedelta(hours=48)
    print(f"Cutoff time (48h ago): {cutoff_time}")
    
    video_manager = VideoHostManager()
    
    for date_str in test_dates:
        print(f"\nTesting date: '{date_str}'")
        
        is_recent = video_manager._is_file_recent_by_date(date_str, cutoff_time)
        print(f"  Is recent: {is_recent}")
        
        # Try parsing with different formats
        if date_str:
            date_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%d",
                "%d/%m/%Y",
                "%m/%d/%Y"
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_str, fmt)
                    is_after_cutoff = parsed_date >= cutoff_time
                    print(f"    Format '{fmt}': {parsed_date} -> Recent: {is_after_cutoff}")
                    break
                except ValueError:
                    continue

async def debug_duplicate_detection():
    """Debug duplicate detection logic"""
    print("\n🔄 DEBUGGING DUPLICATE DETECTION")
    print("=" * 60)
    
    # Simulate files from different hosts
    test_files = [
        # Same file on multiple hosts
        {'filename': 'Movie.2025.1080p.mkv', 'file_code': 'abc123', 'host': 'streamp2p'},
        {'filename': 'Movie.2025.1080p.mkv', 'file_code': 'def456', 'host': 'rpmshare'},
        {'filename': 'Movie.2025.1080p.mkv', 'file_code': 'ghi789', 'host': 'filemoon'},
        
        # Different files
        {'filename': 'Show.S01E01.1080p.mkv', 'file_code': 'xyz123', 'host': 'streamp2p'},
        {'filename': 'Another.Movie.2025.mkv', 'file_code': 'uvw456', 'host': 'upnshare'},
        
        # Same file with slightly different names
        {'filename': 'Movie.2025.1080p.mkv', 'file_code': 'abc789', 'host': 'upnshare'},
        {'filename': 'Movie 2025 1080p.mkv', 'file_code': 'xyz999', 'host': 'rpmshare'},
    ]
    
    print("Test files:")
    for i, file_info in enumerate(test_files):
        print(f"  {i+1}. {file_info['filename']} ({file_info['host']}) - {file_info['file_code']}")
    
    # Test current deduplication logic
    print(f"\n🧪 Testing Current Deduplication Logic:")
    
    files_by_name = {}
    for file_info in test_files:
        filename = file_info['filename']
        
        if filename not in files_by_name:
            files_by_name[filename] = {
                'filename': filename,
                'embed_codes': {},
                'hosts': set()
            }
        
        host = file_info['host']
        file_code = file_info['file_code']
        
        if host not in files_by_name[filename]['hosts']:
            files_by_name[filename]['embed_codes'][host] = f"embed_code_for_{file_code}"
            files_by_name[filename]['hosts'].add(host)
    
    print(f"Deduplicated files:")
    for filename, data in files_by_name.items():
        hosts = list(data['hosts'])
        print(f"  '{filename}' -> Hosts: {hosts} ({len(hosts)} copies)")

async def main():
    """Main debug function"""
    print("🐞 COMPREHENSIVE EMBED EXTRACTION DEBUG")
    print("=" * 80)
    
    # Run all debug tests
    await debug_filemoon_api()
    await debug_p2p_hosts()
    await debug_time_filtering()
    await debug_duplicate_detection()
    
    print("\n✅ DEBUG COMPLETE")
    print("Check the output above for specific issues with each component")

if __name__ == "__main__":
    asyncio.run(main())