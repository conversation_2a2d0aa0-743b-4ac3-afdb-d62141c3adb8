#!/usr/bin/env python3
"""
COMPREHENSIVE VERIFICATION OF EMBED CODE EXTRACTION FIXES

This script verifies that all the reported issues have been fixed:
1. Time range filtering now works correctly  
2. Empty file codes are eliminated
3. Duplicate entries are properly removed
4. Template codes are replaced with real codes
5. FileMoon API parsing works correctly
"""

import asyncio
import sys
import csv
import tempfile
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

async def comprehensive_verification():
    """Run comprehensive verification of all fixes"""
    print("🔍 COMPREHENSIVE VERIFICATION OF EMBED CODE EXTRACTION FIXES")
    print("=" * 80)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Test 1: Time range filtering
        print("\n📅 TEST 1: TIME RANGE FILTERING")
        print("-" * 40)
        
        # Test 48 hours
        result_48h = await vm.extract_embed_codes(48)
        print(f"✅ 48 hours: Found {len(result_48h)} files")
        
        # Test all time (should be more)
        result_all = await vm.extract_embed_codes(999999)
        print(f"✅ All time: Found {len(result_all)} files")
        
        if len(result_all) >= len(result_48h):
            print("✅ Time filtering working correctly (all time >= 48h)")
        else:
            print("❌ Time filtering issue detected")
        
        # Test 2: File code validation  
        print("\n🔍 TEST 2: FILE CODE VALIDATION")
        print("-" * 40)
        
        empty_codes_found = 0
        invalid_codes_found = 0
        
        for file_data in result_48h:
            filename = file_data.get('filename', '')
            embed_codes = file_data.get('embed_codes', {})
            
            for host, embed_code in embed_codes.items():
                # Check for empty file codes in embed URLs
                if f'#{""}"' in embed_code or f'/e//{filename}' in embed_code:
                    empty_codes_found += 1
                    print(f"❌ Empty file code found in {host}: {embed_code[:100]}...")
                
                # Check for template/invalid codes
                if 'mock_id' in embed_code or 'template' in embed_code:
                    invalid_codes_found += 1
                    print(f"❌ Template code found in {host}: {embed_code[:100]}...")
        
        if empty_codes_found == 0:
            print("✅ No empty file codes found")
        else:
            print(f"❌ Found {empty_codes_found} empty file codes")
            
        if invalid_codes_found == 0:
            print("✅ No template/mock codes found")
        else:
            print(f"❌ Found {invalid_codes_found} template codes")
        
        # Test 3: Duplicate detection
        print("\n🔄 TEST 3: DUPLICATE DETECTION")
        print("-" * 40)
        
        # Count files by normalized name to check for duplicates
        normalized_names = {}
        for file_data in result_48h:
            filename = file_data.get('filename', '')
            normalized = vm._normalize_filename(filename)
            
            if normalized not in normalized_names:
                normalized_names[normalized] = []
            normalized_names[normalized].append(filename)
        
        duplicates_found = sum(1 for files in normalized_names.values() if len(files) > 1)
        
        if duplicates_found == 0:
            print("✅ No duplicate entries found")
        else:
            print(f"❌ Found {duplicates_found} duplicate file groups:")
            for normalized, files in normalized_names.items():
                if len(files) > 1:
                    print(f"  - {files}")
        
        # Test 4: FileMoon specific validation
        print("\n🌙 TEST 4: FILEMOON SPECIFIC VALIDATION")
        print("-" * 40)
        
        filemoon_files = [f for f in result_48h if 'filemoon' in f.get('embed_codes', {})]
        print(f"Files with FileMoon embed codes: {len(filemoon_files)}")
        
        filemoon_issues = 0
        for file_data in filemoon_files:
            filemoon_embed = file_data['embed_codes']['filemoon']
            filename = file_data['filename']
            
            # Check for missing file code in FileMoon URL
            if f'/e//{filename}' in filemoon_embed:
                filemoon_issues += 1
                print(f"❌ Missing FileMoon file code: {filemoon_embed[:100]}...")
        
        if filemoon_issues == 0:
            print("✅ All FileMoon embed codes have valid file codes")
        else:
            print(f"❌ Found {filemoon_issues} FileMoon files with missing codes")
        
        # Test 5: CSV generation verification
        print("\n📄 TEST 5: CSV GENERATION")
        print("-" * 40)
        
        # Generate CSV content
        csv_content = []
        for file_data in result_48h:
            filename = file_data.get('filename', '')
            embed_codes = file_data.get('embed_codes', {})
            
            # Format embed codes as specified
            embed_text = '\n\n'.join([
                embed_codes.get('streamp2p', ''),
                embed_codes.get('rpmshare', ''),
                embed_codes.get('upnshare', ''),
                embed_codes.get('filemoon', '')
            ]).strip()
            
            if embed_text:
                csv_content.append([filename, embed_text])
        
        print(f"✅ Generated CSV with {len(csv_content)} rows")
        
        # Test 6: Overall summary
        print("\n📊 OVERALL VERIFICATION SUMMARY")
        print("-" * 40)
        
        all_tests_passed = (
            len(result_all) >= len(result_48h) and  # Time filtering works
            empty_codes_found == 0 and              # No empty codes
            invalid_codes_found == 0 and            # No template codes
            duplicates_found == 0 and               # No duplicates
            filemoon_issues == 0                    # FileMoon works
        )
        
        if all_tests_passed:
            print("🎉 ALL TESTS PASSED! The embed code extraction is working correctly.")
            print(f"✅ Time filtering: Working")
            print(f"✅ File code validation: All valid")
            print(f"✅ Duplicate removal: Working")
            print(f"✅ FileMoon parsing: Working")
            print(f"✅ CSV generation: Working")
        else:
            print("❌ SOME ISSUES REMAIN:")
            if len(result_all) < len(result_48h):
                print("  - Time filtering not working correctly")
            if empty_codes_found > 0:
                print(f"  - {empty_codes_found} empty file codes found")
            if invalid_codes_found > 0:
                print(f"  - {invalid_codes_found} template codes found")
            if duplicates_found > 0:
                print(f"  - {duplicates_found} duplicate entries found")
            if filemoon_issues > 0:
                print(f"  - {filemoon_issues} FileMoon parsing issues")
        
        return all_tests_passed
        
    finally:
        await vm.close()

async def main():
    """Main verification function"""
    success = await comprehensive_verification()
    
    print(f"\n{'='*80}")
    if success:
        print("🎯 VERIFICATION COMPLETE: ALL ISSUES HAVE BEEN FIXED!")
        print("The embed code extraction is now working correctly with:")
        print("  ✅ Proper 48-hour time filtering")
        print("  ✅ Valid file codes in all embed URLs") 
        print("  ✅ No duplicate entries")
        print("  ✅ Real embed codes (no templates)")
        print("  ✅ Correct FileMoon API parsing")
    else:
        print("❌ VERIFICATION FAILED: Some issues still need to be addressed")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)