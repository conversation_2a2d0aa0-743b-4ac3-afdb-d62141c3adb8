#!/usr/bin/env python3
"""
Test script for AutoUploadBot API endpoints
"""
import requests
import json
import time

BASE_URL = "http://localhost:8001"

def test_url_download():
    """Test URL download functionality"""
    print("=== TESTING URL DOWNLOAD ===")
    
    url = f"{BASE_URL}/api/download/url"
    payload = {
        "urls": ["https://httpbin.org/json"]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_magnet_download():
    """Test magnet download functionality"""
    print("\n=== TESTING MAGNET DOWNLOAD ===")
    
    url = f"{BASE_URL}/api/download/magnet"
    # Small test magnet link
    payload = {
        "magnets": ["magnet:?xt=urn:btih:032BC5E2962DAE736DA70BDB9A6739510D4C15E2&dn=test"]
    }
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_clear_queue():
    """Test clear queue functionality"""
    print("\n=== TESTING CLEAR QUEUE ===")
    
    url = f"{BASE_URL}/api/queue/clear"
    payload = {"type": "all"}
    
    try:
        response = requests.post(url, json=payload)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

def test_download_status():
    """Test download status endpoint"""
    print("\n=== TESTING DOWNLOAD STATUS ===")
    
    url = f"{BASE_URL}/api/downloads/status"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False

if __name__ == "__main__":
    print("AutoUploadBot API Testing")
    print("=" * 40)
    
    # Test all endpoints
    tests = [
        ("Download Status", test_download_status),
        ("URL Download", test_url_download),
        ("Magnet Download", test_magnet_download),
        ("Clear Queue", test_clear_queue),
    ]
    
    results = {}
    for test_name, test_func in tests:
        results[test_name] = test_func()
        time.sleep(2)  # Wait between tests
    
    print("\n" + "=" * 40)
    print("TEST RESULTS:")
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
