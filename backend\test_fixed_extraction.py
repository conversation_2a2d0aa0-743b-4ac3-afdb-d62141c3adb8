#!/usr/bin/env python3
"""
Test the fixed embed code extraction
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

async def test_embed_extraction():
    """Test the embed code extraction with 48-hour filter"""
    print("🧪 Testing embed extraction with 48-hour filter...")
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        result = await vm.extract_embed_codes(48)
        print(f"✅ Found {len(result)} files with embed codes")
        
        # Show first few results
        for i, file_data in enumerate(result[:3]):
            filename = file_data.get('filename', 'Unknown')
            embed_codes = file_data.get('embed_codes', {})
            print(f"\n📁 File {i+1}: {filename}")
            
            for host, embed_code in embed_codes.items():
                # Check if embed code contains actual file code (not empty)
                if f'#{""}"' in embed_code or f'/e//{filename}' in embed_code:
                    print(f"  ❌ {host}: EMPTY FILE CODE in embed")
                else:
                    print(f"  ✅ {host}: Valid embed code")
        
        return len(result)
        
    finally:
        await vm.close()

if __name__ == "__main__":
    result_count = asyncio.run(test_embed_extraction())
    print(f"\n🎯 Total files extracted: {result_count}")