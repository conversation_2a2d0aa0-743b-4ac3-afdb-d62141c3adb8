# Google Drive Direct Operations Manager (Server-to-Server Only)
import asyncio
import logging
import tempfile
import os
import tarfile
import gzip
import shutil
from typing import Dict, List, Optional, Any
from pathlib import Path
import zipfile
import rarfile
import py7zr

from core.gdrive_helper import GoogleDriveHelper
from config import Config
from utils.file_utils import is_video_file, get_readable_file_size

LOGGER = logging.getLogger(__name__)


class GoogleDriveDirectManager(GoogleDriveHelper):
    """
    Google Drive Direct Manager for server-to-server operations
    No local machine storage involved - everything happens in cloud
    """
    
    def __init__(self):
        super().__init__()
        self.service = self.authorize()

    async def download_and_extract_archive(
        self, 
        archive_file_id: str, 
        source_folder_id: str,
        dest_folder_id: str
    ) -> Dict[str, Any]:
        """
        Download archive from Google Drive, extract video files, 
        upload to secondary drive, cleanup - all server-to-server
        """
        try:
            LOGGER.info(f"Starting server-to-server archive extraction: {archive_file_id}")
            
            # Get file metadata
            file_metadata = self.service.files().get(
                fileId=archive_file_id,
                fields="name,size,mimeType"
            ).execute()
            
            filename = file_metadata.get('name', 'unknown')
            file_size = int(file_metadata.get('size', 0))
            
            LOGGER.info(f"Archive file: {filename} ({get_readable_file_size(file_size)})")
            
            # Use temporary processing (minimal local involvement)
            with tempfile.TemporaryDirectory() as temp_dir:
                # Download archive to temporary location
                archive_path = os.path.join(temp_dir, filename)
                await self._download_file_from_gdrive(archive_file_id, archive_path)
                
                # Extract archive
                extract_dir = os.path.join(temp_dir, "extracted")
                os.makedirs(extract_dir, exist_ok=True)
                
                extracted_files = await self._extract_archive_local(archive_path, extract_dir)
                
                # Filter video files
                video_files = [f for f in extracted_files if is_video_file(f)]
                
                LOGGER.info(f"Found {len(video_files)} video files in archive")
                
                # Upload video files to secondary Google Drive
                upload_results = []
                for video_file in video_files:
                    try:
                        video_filename = os.path.basename(video_file)
                        link = await self._upload_file_to_gdrive(
                            video_file, 
                            video_filename, 
                            dest_folder_id
                        )
                        upload_results.append({
                            "filename": video_filename,
                            "link": link,
                            "status": "success"
                        })
                        LOGGER.info(f"Uploaded video: {video_filename}")
                    except Exception as e:
                        upload_results.append({
                            "filename": os.path.basename(video_file),
                            "error": str(e),
                            "status": "failed"
                        })
                        LOGGER.error(f"Failed to upload {video_file}: {e}")
                
                # Delete original archive from source folder
                await self._delete_file_from_gdrive(archive_file_id)
                
                return {
                    "status": "success",
                    "archive_filename": filename,
                    "extracted_videos": len(video_files),
                    "upload_results": upload_results
                }
                
        except Exception as e:
            LOGGER.error(f"Archive extraction failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    async def _download_file_from_gdrive(self, file_id: str, local_path: str):
        """Download file from Google Drive to temporary location"""
        try:
            request = self.service.files().get_media(fileId=file_id)
            
            with open(local_path, 'wb') as f:
                downloader = request
                done = False
                while done is False:
                    status, done = downloader.next_chunk()
                    if status:
                        LOGGER.info(f"Download progress: {int(status.progress() * 100)}%")
                        
        except Exception as e:
            LOGGER.error(f"Failed to download file {file_id}: {e}")
            raise e

    async def _extract_archive_local(self, archive_path: str, extract_dir: str) -> List[str]:
        """Extract archive locally and return list of extracted files - MANDATORY CORE FEATURE"""
        extracted_files = []

        try:
            archive_lower = archive_path.lower()

            # ZIP files (including deflate64)
            if archive_lower.endswith(('.zip', '.zipx')):
                LOGGER.info(f"Extracting ZIP archive: {archive_path}")
                try:
                    with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                        zip_ref.extractall(extract_dir)
                except zipfile.BadZipFile:
                    # Try with py7zr for problematic ZIP files
                    LOGGER.info("Standard ZIP extraction failed, trying py7zr...")
                    with py7zr.SevenZipFile(archive_path, mode='r') as z:
                        z.extractall(extract_dir)

            # RAR files
            elif archive_lower.endswith(('.rar', '.r00', '.r01')):
                LOGGER.info(f"Extracting RAR archive: {archive_path}")
                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_dir)

            # 7-Zip files
            elif archive_lower.endswith(('.7z', '.7zip')):
                LOGGER.info(f"Extracting 7-Zip archive: {archive_path}")
                with py7zr.SevenZipFile(archive_path, mode='r') as z:
                    z.extractall(extract_dir)

            # TAR files
            elif archive_lower.endswith(('.tar', '.tar.gz', '.tgz', '.tar.bz2', '.tbz2')):
                LOGGER.info(f"Extracting TAR archive: {archive_path}")
                import tarfile
                with tarfile.open(archive_path, 'r:*') as tar_ref:
                    tar_ref.extractall(extract_dir)

            # GZ files
            elif archive_lower.endswith('.gz') and not archive_lower.endswith('.tar.gz'):
                LOGGER.info(f"Extracting GZ archive: {archive_path}")
                import gzip
                import shutil
                with gzip.open(archive_path, 'rb') as f_in:
                    output_file = os.path.join(extract_dir, os.path.basename(archive_path)[:-3])
                    with open(output_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)

            else:
                # Try py7zr as fallback for unknown formats
                LOGGER.info(f"Unknown format, trying py7zr fallback: {archive_path}")
                try:
                    with py7zr.SevenZipFile(archive_path, mode='r') as z:
                        z.extractall(extract_dir)
                except:
                    raise Exception(f"Unsupported archive format: {archive_path}")

            # Get all extracted files
            for root, dirs, files in os.walk(extract_dir):
                for file in files:
                    extracted_files.append(os.path.join(root, file))

            LOGGER.info(f"✅ Successfully extracted {len(extracted_files)} files from archive")

            if not extracted_files:
                raise Exception("No files were extracted from the archive")

            return extracted_files

        except Exception as e:
            LOGGER.error(f"❌ CRITICAL: Archive extraction failed for {archive_path}: {e}")
            raise Exception(f"Archive extraction FAILED - this is a CORE FEATURE: {e}")

    async def _upload_file_to_gdrive(self, file_path: str, filename: str, folder_id: str) -> str:
        """Upload file to Google Drive"""
        try:
            from googleapiclient.http import MediaFileUpload
            
            file_metadata = {
                'name': filename,
                'parents': [folder_id]
            }
            
            media = MediaFileUpload(
                file_path,
                resumable=True,
                chunksize=Config.CHUNK_SIZE
            )
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id',
                supportsAllDrives=True
            ).execute()
            
            file_id = file.get('id')
            
            # Set permissions if not team drive
            if not Config.IS_TEAM_DRIVE:
                self.set_permission(file_id)
            
            return self.G_DRIVE_BASE_DOWNLOAD_URL.format(file_id)
            
        except Exception as e:
            LOGGER.error(f"Failed to upload file {filename}: {e}")
            raise e

    async def _delete_file_from_gdrive(self, file_id: str):
        """Delete file from Google Drive"""
        try:
            self.service.files().delete(fileId=file_id).execute()
            LOGGER.info(f"Deleted file from Google Drive: {file_id}")
        except Exception as e:
            LOGGER.error(f"Failed to delete file {file_id}: {e}")

    async def get_shareable_link_for_upload(self, file_id: str) -> str:
        """Get shareable link for video hosting platform upload"""
        try:
            # Make file publicly accessible
            permission = {
                'type': 'anyone',
                'role': 'reader'
            }
            
            self.service.permissions().create(
                fileId=file_id,
                body=permission
            ).execute()
            
            # Get direct download link
            file = self.service.files().get(
                fileId=file_id,
                fields='webContentLink'
            ).execute()
            
            return file.get('webContentLink')
            
        except Exception as e:
            LOGGER.error(f"Failed to get shareable link for {file_id}: {e}")
            raise e

    async def list_files_in_folder(self, folder_id: str) -> List[Dict]:
        """List all files in a Google Drive folder"""
        try:
            query = f"'{folder_id}' in parents and trashed=false"
            results = self.service.files().list(
                q=query,
                fields="files(id,name,size,mimeType,createdTime,webViewLink)",
                pageSize=1000
            ).execute()
            
            files = results.get('files', [])
            
            # Add readable file sizes
            for file in files:
                if 'size' in file:
                    file['readable_size'] = get_readable_file_size(int(file['size']))
                else:
                    file['readable_size'] = 'Unknown'
            
            return files
            
        except Exception as e:
            LOGGER.error(f"Failed to list files in folder {folder_id}: {e}")
            return []

    async def bulk_delete_files(self, file_ids: List[str]) -> Dict[str, bool]:
        """Delete multiple files from Google Drive"""
        results = {}
        
        for file_id in file_ids:
            try:
                await self._delete_file_from_gdrive(file_id)
                results[file_id] = True
            except Exception as e:
                LOGGER.error(f"Failed to delete file {file_id}: {e}")
                results[file_id] = False
        
        return results
