import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import { Play, RefreshCw, Zap, Upload, Trash2, FolderOpen, CheckCircle, Clock, AlertCircle, Download } from "lucide-react";
import { apiService } from "@/services/api";
import { useToast } from "@/hooks/use-toast";

interface GDriveFile {
  id: string;
  name: string;
  size: string;
  mimeType: string;
  createdTime: string;
  shareUrl?: string;
}

interface UploadProgress {
  filename: string;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  progress: {
    streamp2p: number;
    rpmshare: number;
    upnshare: number;
    filemoon: number;
  };
  embedCodes?: {
    streamp2p: string;
    rpmshare: string;
    upnshare: string;
    filemoon: string;
  };
}

interface ShareableLink {
  filename: string;
  shareUrl: string;
  selected: boolean;
}

const AutoVideoUploader = () => {
  const [selectedDrive, setSelectedDrive] = useState<'primary' | 'secondary' | 'both'>('primary');
  const [files, setFiles] = useState<GDriveFile[]>([]);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [isScanning, setIsScanning] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadQueue, setUploadQueue] = useState<UploadProgress[]>([]);
  const [completedUploads, setCompletedUploads] = useState<UploadProgress[]>([]);
  const [extractedLinks, setExtractedLinks] = useState<ShareableLink[]>([]);
  const [selectAllFilesChecked, setSelectAllFilesChecked] = useState(false);
  const [selectAllLinksChecked, setSelectAllLinksChecked] = useState(false);
  const { toast } = useToast();

  // Scan Google Drive for video files
  const scanDriveForFiles = async () => {
    setIsScanning(true);
    try {
      let allFiles: GDriveFile[] = [];

      if (selectedDrive === 'primary' || selectedDrive === 'both') {
        const primaryResponse = await apiService.listGDriveFiles('primary');
        const primaryVideoFiles = primaryResponse.files.filter(file =>
          file.mimeType.includes('video') ||
          file.name.match(/\.(mp4|mkv|avi|mov|wmv|flv|webm|m4v)$/i)
        );
        allFiles = [...allFiles, ...primaryVideoFiles];
      }

      if (selectedDrive === 'secondary' || selectedDrive === 'both') {
        const secondaryResponse = await apiService.listGDriveFiles('secondary');
        const secondaryVideoFiles = secondaryResponse.files.filter(file =>
          file.mimeType.includes('video') ||
          file.name.match(/\.(mp4|mkv|avi|mov|wmv|flv|webm|m4v)$/i)
        );
        allFiles = [...allFiles, ...secondaryVideoFiles];
      }

      setFiles(allFiles);
      toast({
        title: "Scan Complete",
        description: `Found ${allFiles.length} video files`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to scan Google Drive",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  };

  // Select all files
  const selectAllFiles = () => {
    if (files.length > 0) {
      const allFileIds = files.map(file => file.id);
      setSelectedFiles(allFileIds);
      setSelectAllFilesChecked(true);
    }
  };

  // Clear all file selections
  const clearAllFileSelections = () => {
    setSelectedFiles([]);
    setSelectAllFilesChecked(false);
  };

  // Handle select all files checkbox
  const handleSelectAllFiles = (checked: boolean) => {
    if (checked) {
      selectAllFiles();
    } else {
      clearAllFileSelections();
    }
  };

  // Handle individual file selection
  const handleFileSelection = (fileId: string, checked: boolean) => {
    if (checked) {
      setSelectedFiles(prev => [...prev, fileId]);
    } else {
      setSelectedFiles(prev => prev.filter(id => id !== fileId));
    }
    
    // Update select all checkbox state
    const newSelectedCount = checked 
      ? selectedFiles.length + 1 
      : selectedFiles.length - 1;
    
    setSelectAllFilesChecked(newSelectedCount === files.length && files.length > 0);
  };

  // Start upload process
  const startUpload = async () => {
    if (selectedFiles.length === 0) {
      toast({
        title: "No Files Selected",
        description: "Please select at least one file to upload",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);
    const selectedFileObjects = files.filter(file => selectedFiles.includes(file.id));

    // Initialize upload queue
    const initialQueue: UploadProgress[] = selectedFileObjects.map(file => ({
      filename: file.name,
      status: 'pending',
      progress: {
        streamp2p: 0,
        rpmshare: 0,
        upnshare: 0,
        filemoon: 0
      }
    }));

    setUploadQueue(initialQueue);

    // Process uploads sequentially (one file at a time)
    for (let i = 0; i < selectedFileObjects.length; i++) {
      const file = selectedFileObjects[i];
      await uploadSingleFile(file, i);
    }

    setIsUploading(false);

    // Play completion sound
    apiService.playCompletionSound();

    toast({
      title: "Upload Complete",
      description: `Successfully uploaded ${selectedFileObjects.length} files to all 4 hosts`,
    });
  };

  // Upload single file to all 4 hosts
  const uploadSingleFile = async (file: GDriveFile, index: number) => {
    // Update status to uploading
    setUploadQueue(prev => prev.map((item, i) =>
      i === index ? { ...item, status: 'uploading' } : item
    ));

    try {
      // Generate shareable link for the file
      const shareUrl = `https://drive.google.com/file/d/${file.id}/view?usp=sharing`;

      // Upload to all 4 hosts using the new manual upload endpoint
      const response = await apiService.manualUploadToHosts([shareUrl], [file.name]);

      if (response.status === 'success' && response.results && response.results.length > 0) {
        const result = response.results[0];

        if (result.status === 'success') {
          // Update progress to 100% for all hosts
          setUploadQueue(prev => prev.map((item, i) =>
            i === index ? {
              ...item,
              status: 'completed',
              progress: {
                streamp2p: 100,
                rpmshare: 100,
                upnshare: 100,
                filemoon: 100
              },
              embedCodes: result.embed_codes
            } : item
          ));

          // Move to completed uploads
          const completedUpload = uploadQueue[index];
          if (completedUpload) {
            setCompletedUploads(prev => [...prev, {
              ...completedUpload,
              status: 'completed',
              embedCodes: result.embed_codes
            }]);
          }
        } else {
          throw new Error(result.error || 'Upload failed');
        }
      } else {
        throw new Error(response.message || 'Upload failed');
      }
    } catch (error) {
      // Update status to failed
      setUploadQueue(prev => prev.map((item, i) =>
        i === index ? { ...item, status: 'failed' } : item
      ));

      toast({
        title: "Upload Failed",
        description: `Failed to upload ${file.name}`,
        variant: "destructive",
      });
    }
  };

  // Clear completed uploads queue
  const clearQueue = () => {
    setCompletedUploads([]);
    setUploadQueue([]);
    setSelectedFiles([]);
    toast({
      title: "Queue Cleared",
      description: "All completed jobs have been cleared",
    });
  };

  // Format file size
  const formatFileSize = (bytes: string): string => {
    const size = parseInt(bytes);
    if (size === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle select all shareable links
  const handleSelectAllLinks = (checked: boolean) => {
    setSelectAllLinksChecked(checked);
    setExtractedLinks(prev => prev.map(link => ({
      ...link,
      selected: checked
    })));
  };

  // Handle individual link selection
  const handleLinkSelection = (index: number, checked: boolean) => {
    setExtractedLinks(prev => {
      const updated = [...prev];
      updated[index] = { ...updated[index], selected: checked };
      return updated;
    });
    
    // Update select all checkbox state
    const selectedLinksCount = extractedLinks.filter((link, i) => 
      i === index ? checked : link.selected
    ).length;
    
    const newSelectAllState = selectedLinksCount === extractedLinks.length && extractedLinks.length > 0;
    setSelectAllLinksChecked(newSelectAllState);
  };

  // Copy selected shareable links
  const copySelectedLinks = () => {
    const selectedLinks = extractedLinks.filter(link => link.selected);
    
    if (selectedLinks.length === 0) {
      toast({
        title: "No Links Selected",
        description: "Please select at least one link to copy",
        variant: "destructive",
      });
      return;
    }
    
    const linksText = selectedLinks.map(link => link.shareUrl).join('\n');
    
    navigator.clipboard.writeText(linksText).then(() => {
      toast({
        title: "Copied!",
        description: `Copied ${selectedLinks.length} shareable links to clipboard`,
      });
    }).catch(err => {
      toast({
        title: "Copy Failed",
        description: "Failed to copy links to clipboard",
        variant: "destructive",
      });
    });
  };

  // Clear selected links
  const clearSelectedLinks = () => {
    setExtractedLinks(prev => prev.map(link => ({ ...link, selected: false })));
    setSelectAllLinksChecked(false);
  };

  // Export shared links to CSV and display in UI
  const exportSharedLinksCSV = () => {
    if (selectedFiles.length === 0) {
      toast({
        title: "No Files Selected",
        description: "Please select files to export shared links",
        variant: "destructive",
      });
      return;
    }

    const selectedFileObjects = files.filter(file => selectedFiles.includes(file.id));

    // Create CSV content and UI data
    let csvContent = "Filename,Shared Link\n";
    const linksData: ShareableLink[] = [];

    selectedFileObjects.forEach(file => {
      const shareUrl = `https://drive.google.com/file/d/${file.id}/view?usp=sharing`;
      csvContent += `"${file.name}","${shareUrl}"\n`;
      linksData.push({ filename: file.name, shareUrl, selected: false });
    });

    // Update UI state to display the links
    setExtractedLinks(linksData);

    // Create and download file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `shared_links_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "CSV Exported & Links Displayed",
      description: `Exported shared links for ${selectedFileObjects.length} files and displayed in UI`,
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold font-mono mb-2">AUTO UPLOADER</h2>
        <p className="text-muted-foreground font-mono">MANUAL DRIVE SELECTION • FILE SELECTION • UPLOAD TO 4 HOSTS</p>
      </div>

      {/* Drive Selection and Scan */}
      <Card className="brutal-card">
        <CardHeader className="bg-primary text-primary-foreground brutal-border-b">
          <CardTitle className="font-mono text-lg flex items-center gap-2">
            <FolderOpen className="w-5 h-5" />
            GOOGLE DRIVE SELECTION
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="font-mono font-bold text-sm mb-2 block">SELECT DRIVE:</label>
                <Select value={selectedDrive} onValueChange={(value: 'primary' | 'secondary' | 'both') => setSelectedDrive(value)}>
                  <SelectTrigger className="brutal-button">
                    <SelectValue placeholder="Choose Google Drive" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary Google Drive</SelectItem>
                    <SelectItem value="secondary">Secondary Google Drive</SelectItem>
                    <SelectItem value="both">Both Google Drives</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-mono font-bold">Video Files Found:</span>
                <Badge variant="secondary" className="font-mono">
                  {files.length}
                </Badge>
              </div>

              <div className="flex items-center justify-between">
                <span className="font-mono font-bold">Files Selected:</span>
                <Badge variant="default" className="font-mono">
                  {selectedFiles.length}
                </Badge>
              </div>
            </div>

            <div className="space-y-3">
              <Button
                onClick={scanDriveForFiles}
                disabled={isScanning}
                className="brutal-button w-full bg-accent text-accent-foreground hover:bg-accent/90"
              >
                {isScanning ? (
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Zap className="w-4 h-4 mr-2" />
                )}
                {isScanning ? "SCANNING..." : "SCAN DRIVES"}
              </Button>

              <Button
                onClick={selectAllFiles}
                disabled={files.length === 0}
                className="brutal-button w-full"
                variant="outline"
              >
                <CheckCircle className="w-4 h-4 mr-2" />
                SELECT ALL
              </Button>

              <Button
                onClick={clearAllFileSelections}
                disabled={selectedFiles.length === 0}
                className="brutal-button w-full"
                variant="outline"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                CLEAR SELECTION
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File List with Select All Checkbox */}
      {files.length > 0 && (
        <Card className="brutal-card">
          <CardHeader className="bg-secondary text-secondary-foreground brutal-border-b">
            <CardTitle className="font-mono text-lg">VIDEO FILES</CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            {/* Select All Checkbox */}
            <div className="flex items-center space-x-3 p-3 border rounded-lg mb-4">
              <Checkbox
                checked={selectAllFilesChecked}
                onCheckedChange={(checked) => handleSelectAllFiles(checked as boolean)}
              />
              <div className="flex-1 min-w-0">
                <div className="font-mono font-bold text-sm">Select All Files</div>
                <div className="text-xs text-muted-foreground">
                  {selectedFiles.length} of {files.length} files selected
                </div>
              </div>
            </div>
            
            {/* File List */}
            <div className="max-h-96 overflow-y-auto space-y-2">
              {files.map((file) => (
                <div key={file.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                  <Checkbox
                    checked={selectedFiles.includes(file.id)}
                    onCheckedChange={(checked) => handleFileSelection(file.id, checked as boolean)}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="font-mono font-bold text-sm truncate">{file.name}</div>
                    <div className="text-xs text-muted-foreground">
                      Size: {formatFileSize(file.size)} • Created: {new Date(file.createdTime).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload and Share Links Section */}
      {selectedFiles.length > 0 && (
        <Card className="brutal-card">
          <CardHeader className="bg-accent text-accent-foreground brutal-border-b">
            <CardTitle className="font-mono text-lg flex items-center gap-2">
              <Upload className="w-5 h-5" />
              ACTIONS
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button
                  onClick={startUpload}
                  disabled={isUploading}
                  className="brutal-button bg-primary text-primary-foreground hover:bg-primary/90"
                  size="lg"
                >
                  {isUploading ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="w-4 h-4 mr-2" />
                  )}
                  {isUploading ? "UPLOADING..." : `UPLOAD ${selectedFiles.length} FILES`}
                </Button>

                <Button
                  onClick={exportSharedLinksCSV}
                  className="brutal-button bg-accent text-accent-foreground hover:bg-accent/90"
                  size="lg"
                >
                  <Download className="w-4 h-4 mr-2" />
                  EXPORT SHARED LINKS CSV
                </Button>
              </div>

              <div className="text-center text-sm text-muted-foreground font-mono">
                Upload files to: StreamP2P • RPMShare • UPnShare • Filemoon<br/>
                Or export Google Drive shared links as CSV
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Progress */}
      {uploadQueue.length > 0 && (
        <Card className="brutal-card">
          <CardHeader className="bg-orange-600 text-white brutal-border-b">
            <CardTitle className="font-mono text-lg flex items-center gap-2">
              <Clock className="w-5 h-5" />
              UPLOAD PROGRESS
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="space-y-4">
              {uploadQueue.map((upload, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-mono font-bold text-sm truncate">{upload.filename}</span>
                    <Badge
                      variant={upload.status === 'completed' ? 'default' : upload.status === 'failed' ? 'destructive' : 'secondary'}
                      className="font-mono"
                    >
                      {upload.status.toUpperCase()}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <div className="space-y-1">
                      <div className="text-xs font-mono">StreamP2P</div>
                      <Progress value={upload.progress.streamp2p} className="h-2" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs font-mono">RPMShare</div>
                      <Progress value={upload.progress.rpmshare} className="h-2" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs font-mono">UPnShare</div>
                      <Progress value={upload.progress.upnshare} className="h-2" />
                    </div>
                    <div className="space-y-1">
                      <div className="text-xs font-mono">Filemoon</div>
                      <Progress value={upload.progress.filemoon} className="h-2" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Extracted Shareable Links Display with Select All */}
      {extractedLinks.length > 0 && (
        <Card className="brutal-card">
          <CardHeader className="bg-blue-600 text-white brutal-border-b">
            <CardTitle className="font-mono flex items-center justify-between">
              <span className="flex items-center">
                <Download className="w-5 h-5 mr-2" />
                EXTRACTED SHAREABLE LINKS ({extractedLinks.length})
              </span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={selectAllLinksChecked}
                  onCheckedChange={(checked) => handleSelectAllLinks(checked as boolean)}
                />
                <span className="text-sm font-mono">Select All</span>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {extractedLinks.map((link, index) => (
                <div key={index} className="border border-border rounded p-3 bg-muted/50">
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={link.selected}
                        onCheckedChange={(checked) => handleLinkSelection(index, checked as boolean)}
                      />
                      <div className="font-mono text-sm font-semibold text-foreground truncate">
                        {link.filename}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="text"
                        value={link.shareUrl}
                        readOnly
                        className="flex-1 px-2 py-1 text-xs font-mono bg-background border border-border rounded"
                        onClick={(e) => (e.target as HTMLInputElement).select()}
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          navigator.clipboard.writeText(link.shareUrl);
                          toast({
                            title: "Copied!",
                            description: "Share URL copied to clipboard",
                          });
                        }}
                        className="text-xs"
                      >
                        COPY
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 flex justify-between items-center">
              <div className="text-sm text-muted-foreground font-mono">
                Select links and use buttons below to copy or clear
              </div>
              <div className="flex space-x-2">
                <Button
                  onClick={copySelectedLinks}
                  variant="default"
                  size="sm"
                  className="brutal-button"
                >
                  COPY SELECTED ({extractedLinks.filter(link => link.selected).length})
                </Button>
                <Button
                  onClick={clearSelectedLinks}
                  variant="outline"
                  size="sm"
                  className="brutal-button"
                >
                  CLEAR SELECTION
                </Button>
                <Button
                  onClick={() => setExtractedLinks([])}
                  variant="outline"
                  size="sm"
                  className="brutal-button"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  CLEAR ALL
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Clear Queue */}
      {(completedUploads.length > 0 || uploadQueue.length > 0) && (
        <Card className="brutal-card">
          <CardContent className="p-6 text-center">
            <Button
              onClick={clearQueue}
              className="brutal-button bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              CLEAR QUEUE
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AutoVideoUploader;
