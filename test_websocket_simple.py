#!/usr/bin/env python3
"""
Simple WebSocket test using different approach
"""
import asyncio
import aiohttp
import json

async def test_websocket_aiohttp():
    """Test WebSocket using aiohttp"""
    print("Testing WebSocket with aiohttp...")
    
    try:
        session = aiohttp.ClientSession()
        
        # Connect to WebSocket
        async with session.ws_connect('ws://localhost:8001/ws') as ws:
            print("PASS: WebSocket connected successfully")
            
            # Listen for a few messages
            for i in range(3):
                try:
                    msg = await asyncio.wait_for(ws.receive(), timeout=5.0)
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = json.loads(msg.data)
                        print(f"Message {i+1}: {data.get('type', 'unknown')}")
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        print(f"Message {i+1}: WebSocket error")
                        break
                    elif msg.type == aiohttp.WSMsgType.CLOSE:
                        print(f"Message {i+1}: WebSocket closed")
                        break
                except asyncio.TimeoutError:
                    print(f"Message {i+1}: Timeout")
                except Exception as e:
                    print(f"Message {i+1}: Error - {e}")
            
            print("PASS: WebSocket communication working")
            await session.close()
            return True
            
    except Exception as e:
        print(f"FAIL: WebSocket test error: {e}")
        try:
            await session.close()
        except:
            pass
        return False

async def main():
    """Run WebSocket test"""
    print("Testing WebSocket Connection with aiohttp")
    print("=" * 40)
    
    result = await test_websocket_aiohttp()
    
    print("=" * 40)
    print(f"WebSocket Test: {'PASS' if result else 'FAIL'}")

if __name__ == "__main__":
    asyncio.run(main())
