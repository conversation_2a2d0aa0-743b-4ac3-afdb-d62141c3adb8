import asyncio, sys
sys.path.append(r'g:/My Websites/Catalogue-Website/automatic-upload-bot/automatic-upload-bot/backend')
from backend.api.video_hosts import VideoHostManager

async def run():
    mgr = VideoHostManager()
    await mgr.initialize()
    try:
        files = await mgr._list_filemoon_files(48)
        print('count', len(files))
        if files:
            import json
            print(json.dumps(files[:5], indent=2))
    finally:
        await mgr.close()

asyncio.run(run())
