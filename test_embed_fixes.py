#!/usr/bin/env python3
"""
Test script to verify the embed code extraction fixes
This script will test the fixed functionality comprehensively
"""

import asyncio
import logging
import sys
import os
from pathlib import Path
import requests
import json
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from backend.api.video_hosts import VideoHostManager
from backend.config import Config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_api_endpoint(hours=48):
    """Test the API endpoint directly"""
    print(f"🌐 Testing API endpoint for {hours} hours...")
    
    try:
        response = requests.post(
            "http://localhost:8001/api/embed/extract",
            headers={"Content-Type": "application/json"},
            json={"hours": hours},
            timeout=120
        )
        
        if response.status_code == 200:
            data = response.json()
            embed_data = data.get('embed_data', [])
            
            print(f"✅ API Success: Retrieved {len(embed_data)} files")
            
            # Analyze the results
            hosts_count = {'streamp2p': 0, 'rpmshare': 0, 'upnshare': 0, 'filemoon': 0}
            empty_codes = 0
            
            for item in embed_data:
                embed_codes = item.get('embed_codes', {})
                for host, code in embed_codes.items():
                    if host in hosts_count:
                        hosts_count[host] += 1
                        # Check for empty file codes in embed URLs
                        if '/#}' in code or '/e//' in code or '#{' in code:
                            empty_codes += 1
                            print(f"⚠️ Empty/invalid code detected in {host}: {code[:100]}...")
            
            print(f"📊 Host distribution: {hosts_count}")
            print(f"❌ Files with empty codes: {empty_codes}")
            
            # Show sample results
            for i, item in enumerate(embed_data[:3]):
                print(f"\n📁 Sample {i+1}: {item.get('filename', 'Unknown')}")
                embed_codes = item.get('embed_codes', {})
                for host, code in embed_codes.items():
                    if 'filemoon' in host and '/e//' in code:
                        print(f"  ❌ {host}: INVALID - {code[:60]}...")
                    elif '/#}' in code or '#{' in code:
                        print(f"  ❌ {host}: EMPTY CODE - {code[:60]}...")
                    else:
                        print(f"  ✅ {host}: {code[:60]}...")
            
            return len(embed_data), empty_codes == 0
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text[:500]}")
            return 0, False
            
    except Exception as e:
        print(f"❌ API Test Error: {e}")
        return 0, False

async def test_direct_manager():
    """Test the VideoHostManager directly"""
    print("\n🔧 Testing VideoHostManager directly...")
    
    try:
        video_manager = VideoHostManager()
        await video_manager.initialize()
        
        # Test individual hosts
        for host in ['streamp2p', 'rpmshare', 'upnshare', 'filemoon']:
            if not video_manager.api_keys.get(host):
                print(f"⚠️ Skipping {host} - No API key")
                continue
                
            print(f"\n🌐 Testing {host}...")
            try:
                files = await video_manager.list_recent_uploads(host, 48)
                print(f"✅ {host}: Found {len(files)} files")
                
                # Check file code quality
                valid_codes = 0
                for file_info in files[:5]:  # Check first 5
                    file_code = file_info.get('file_code', '')
                    filename = file_info.get('filename', '')
                    
                    if video_manager._is_valid_file_code(file_code, host):
                        valid_codes += 1
                        print(f"  ✅ {filename[:40]}... -> {file_code}")
                    else:
                        print(f"  ❌ {filename[:40]}... -> INVALID: '{file_code}'")
                
                print(f"📊 {host}: {valid_codes}/{min(len(files), 5)} files have valid codes")
                
            except Exception as e:
                print(f"❌ {host} failed: {e}")
        
        # Test full embed extraction
        print(f"\n🎯 Testing full embed extraction...")
        embed_data = await video_manager.extract_embed_codes(48)
        print(f"✅ Full extraction: {len(embed_data)} files with embed codes")
        
        await video_manager.close()
        return len(embed_data)
        
    except Exception as e:
        print(f"❌ Manager test error: {e}")
        return 0

def test_time_filtering():
    """Test time filtering functionality"""
    print("\n⏰ Testing time filtering...")
    
    try:
        # Test 48 hours
        response_48h = requests.post(
            "http://localhost:8001/api/embed/extract",
            json={"hours": 48},
            timeout=60
        )
        
        # Test all time
        response_all = requests.post(
            "http://localhost:8001/api/embed/extract", 
            json={"hours": 999999},
            timeout=60
        )
        
        if response_48h.status_code == 200 and response_all.status_code == 200:
            files_48h = len(response_48h.json().get('embed_data', []))
            files_all = len(response_all.json().get('embed_data', []))
            
            print(f"📊 48 hours: {files_48h} files")
            print(f"📊 All time: {files_all} files")
            
            if files_all >= files_48h:
                print("✅ Time filtering working correctly (all time >= 48h)")
                return True
            else:
                print("❌ Time filtering issue (all time < 48h)")
                return False
        else:
            print("❌ Time filtering test failed - API errors")
            return False
            
    except Exception as e:
        print(f"❌ Time filtering test error: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 COMPREHENSIVE EMBED CODE EXTRACTION TESTING")
    print("=" * 60)
    
    # Test 1: API endpoint with 48 hours
    files_48h, codes_valid = test_api_endpoint(48)
    
    # Test 2: API endpoint with all time
    files_all, _ = test_api_endpoint(999999)
    
    # Test 3: Time filtering
    time_filter_ok = test_time_filtering()
    
    # Test 4: Direct manager testing
    manager_files = await test_direct_manager()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Files found (48h): {files_48h}")
    print(f"✅ Files found (all): {files_all}")
    print(f"✅ Manager files: {manager_files}")
    print(f"✅ Valid embed codes: {codes_valid}")
    print(f"✅ Time filtering: {time_filter_ok}")
    
    if all([files_48h > 0, codes_valid, time_filter_ok, manager_files > 0]):
        print("\n🎉 ALL TESTS PASSED - EMBED EXTRACTION FIXED!")
        return True
    else:
        print("\n❌ SOME TESTS FAILED - NEED MORE FIXES")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)