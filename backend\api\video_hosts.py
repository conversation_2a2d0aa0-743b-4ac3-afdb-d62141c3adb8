# Video hosting platforms API integration
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import aiohttp
import time
import os
from pathlib import Path
try:
    # When imported as a package (recommended) use relative import
    from ..config import Config
except Exception:
    # Fallback to top-level import for standalone execution
    from config import Config

LOGGER = logging.getLogger(__name__)


class VideoHostManager:
    """Manage uploads to video hosting platforms"""

    def __init__(self):
        self.session = None
        self.upload_queue = asyncio.Queue()
        self.active_uploads: Dict[str, Dict] = {}

        # API configuration - prefer Config where available
        # Use Config values (loaded from backend/.env)
        self.api_keys = {
            'streamp2p': Config.STREAMP2P_API_KEY,
            'rpmshare': Config.RPMSHARE_API_KEY,
            'upnshare': Config.UPNSHARE_API_KEY,
            'filemoon': Config.FILEMOON_API_KEY
        }

        # Normalize FileMoon base URL: allow config to contain either
        # 'https://filemoon.sx' or 'https://filemoon.sx/api'. We want a clean base
        # without the '/api' suffix so code can append '/api/...' when needed.
        fm_base = Config.FILEMOON_API_URL.rstrip('/') if hasattr(Config, 'FILEMOON_API_URL') else 'https://filemoon.sx'
        if fm_base.endswith('/api'):
            fm_base = fm_base[:-4]

        self.base_urls = {
            'streamp2p': Config.STREAMP2P_API_URL if hasattr(Config, 'STREAMP2P_API_URL') else 'https://streamp2p.com',
            'rpmshare': Config.RPMSHARE_API_URL if hasattr(Config, 'RPMSHARE_API_URL') else 'https://rpmshare.com',
            'upnshare': Config.UPNSHARE_API_URL if hasattr(Config, 'UPNSHARE_API_URL') else 'https://upnshare.com',
            'filemoon': fm_base
        }

        # Validate API keys
        missing_keys = [host for host, key in self.api_keys.items() if not key]
        if missing_keys:
            LOGGER.warning(f"Missing API keys for: {', '.join(missing_keys)}")

    async def initialize(self):
        """Initialize HTTP session"""
        self.session = aiohttp.ClientSession()

    def _build_p2p_api_url(self, host: str, path: str) -> str:
        """Build full API URL for P2P hosts while avoiding duplicate /api segments.

        path should be relative like 'video/advance-upload' or 'video/manage'.
        """
        base = self.base_urls.get(host, '').rstrip('/')
        # If the base already contains '/api' or '/api/v1', just append the relative path
        if '/api' in base:
            return f"{base}/{path.lstrip('/') }"
        # Otherwise prepend the common '/api/v1' prefix
        return f"{base}/api/v1/{path.lstrip('/') }"

    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()

    async def upload_to_streamp2p(self, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload to StreamP2P using advance-upload API"""
        try:
            headers = {"api-token": self.api_keys['streamp2p']}
            data = {
                "url": file_url,
                "name": filename
            }

            LOGGER.info(f"StreamP2P: Starting upload for {filename}")

            async with self.session.post(
                self._build_p2p_api_url('streamp2p', 'video/advance-upload'),
                headers=headers,
                json=data
            ) as response:
                response_text = await response.text()
                LOGGER.info(f"StreamP2P response: {response.status} - {response_text}")

                if response.status == 201:
                    result = await response.json()
                    task_id = result.get("id")
                    LOGGER.info(f"StreamP2P upload task created: {task_id}")

                    # Poll for completion
                    return await self._poll_streamp2p_status(task_id)
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {response_text}",
                        'host': 'streamp2p'
                    }

        except Exception as e:
            LOGGER.error(f"StreamP2P upload error: {e}")
            return {
                'success': False,
                'error': str(e),
                'host': 'streamp2p'
            }

    async def upload_to_rpmshare(self, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload to RPMShare using advance-upload API"""
        try:
            headers = {"api-token": self.api_keys['rpmshare']}
            data = {
                "url": file_url,
                "name": filename
            }

            LOGGER.info(f"RPMShare: Starting upload for {filename}")

            async with self.session.post(
                self._build_p2p_api_url('rpmshare', 'video/advance-upload'),
                headers=headers,
                json=data
            ) as response:
                response_text = await response.text()
                LOGGER.info(f"RPMShare response: {response.status} - {response_text}")

                if response.status == 201:
                    result = await response.json()
                    task_id = result.get("id")
                    LOGGER.info(f"RPMShare upload task created: {task_id}")

                    # Poll for completion
                    return await self._poll_rpmshare_status(task_id)
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {response_text}",
                        'host': 'rpmshare'
                    }

        except Exception as e:
            LOGGER.error(f"RPMShare upload error: {e}")
            return {
                'success': False,
                'error': str(e),
                'host': 'rpmshare'
            }

    async def upload_to_upnshare(self, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload to UPnShare using advance-upload API"""
        try:
            headers = {"api-token": self.api_keys['upnshare']}
            data = {
                "url": file_url,
                "name": filename
            }

            LOGGER.info(f"UPnShare: Starting upload for {filename}")

            async with self.session.post(
                self._build_p2p_api_url('upnshare', 'video/advance-upload'),
                headers=headers,
                json=data
            ) as response:
                response_text = await response.text()
                LOGGER.info(f"UPnShare response: {response.status} - {response_text}")

                if response.status == 201:
                    result = await response.json()
                    task_id = result.get("id")
                    LOGGER.info(f"UPnShare upload task created: {task_id}")

                    # Poll for completion
                    return await self._poll_upnshare_status(task_id)
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {response_text}",
                        'host': 'upnshare'
                    }

        except Exception as e:
            LOGGER.error(f"UPnShare upload error: {e}")
            return {
                'success': False,
                'error': str(e),
                'host': 'upnshare'
            }

    async def upload_to_filemoon(self, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload to Filemoon using remote upload API"""
        try:
            params = {
                "key": self.api_keys['filemoon'],
                "url": file_url
            }

            LOGGER.info(f"FileMoon: Starting upload for {filename}")

            async with self.session.get(
                f"{self.base_urls['filemoon']}/api/remote/add",
                params=params
            ) as response:
                response_text = await response.text()
                LOGGER.info(f"FileMoon response: {response.status} - {response_text}")

                if response.status == 200:
                    result = await response.json()
                    if result.get("status") == 200:
                        file_code = result.get("result", {}).get("filecode")

                        # Debug logging for FileMoon response
                        LOGGER.info(f"FileMoon API response: {result}")
                        LOGGER.info(f"FileMoon file_code extracted: '{file_code}'")

                        # Validate file_code
                        if not file_code or file_code == 'code':
                            LOGGER.error(f"FileMoon returned invalid file_code: '{file_code}' for {filename}")
                            return {
                                'success': False,
                                'error': f"Invalid file_code returned: '{file_code}'",
                                'host': 'filemoon'
                            }

                        embed_url = f"https://filemoon.to/e/{file_code}/{filename}"

                        LOGGER.info(f"FileMoon upload successful: {file_code}")

                        return {
                            'success': True,
                            'file_code': file_code,
                            'embed_url': embed_url,
                            'host': 'filemoon'
                        }
                    else:
                        return {
                            'success': False,
                            'error': result.get('msg', 'Unknown error'),
                            'host': 'filemoon'
                        }
                else:
                    return {
                        'success': False,
                        'error': f"HTTP {response.status}: {response_text}",
                        'host': 'filemoon'
                    }

        except Exception as e:
            LOGGER.error(f"FileMoon upload error: {e}")
            return {
                'success': False,
                'error': str(e),
                'host': 'filemoon'
            }

    async def _poll_streamp2p_status(self, task_id: str, max_attempts: int = 120) -> Dict[str, Any]:
        """Poll StreamP2P upload status until completion"""
        try:
            headers = {"api-token": self.api_keys['streamp2p']}

            for attempt in range(max_attempts):
                async with self.session.get(
                    self._build_p2p_api_url('streamp2p', f'video/advance-upload/{task_id}'),
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        status = data.get('status', '').lower()

                        LOGGER.info(f"StreamP2P task {task_id} status: {status}")

                        if status == 'completed':
                            # Accept multiple shapes: videos: [{id:..}], videos: ['id'], videoId: 'id', result.videos, data.videos
                            video_id = None

                            # Case 1: videos as list of objects
                            videos = data.get('videos')
                            if isinstance(videos, list) and len(videos) > 0:
                                first = videos[0]
                                if isinstance(first, dict):
                                    video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                elif isinstance(first, str):
                                    video_id = first

                            # Case 2: direct fields
                            if not video_id:
                                video_id = data.get('videoId') or data.get('video_id') or data.get('id')

                            # Case 3: nested result/data
                            if not video_id and isinstance(data.get('result'), dict):
                                res_videos = data['result'].get('videos') or data['result'].get('data')
                                if isinstance(res_videos, list) and res_videos:
                                    first = res_videos[0]
                                    if isinstance(first, dict):
                                        video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                    elif isinstance(first, str):
                                        video_id = first

                            # Case 4: attempt to parse from an assetUrl or link
                            if not video_id:
                                link = data.get('assetUrl') or data.get('link') or data.get('url') or ''
                                if isinstance(link, str) and link:
                                    import re
                                    m = re.search(r"[#/]([^#/?&]+)$", link)
                                    if m:
                                        candidate = m.group(1)
                                        if candidate.lower() not in ('video', 'embed', 'watch'):
                                            video_id = candidate

                            if video_id:
                                embed_url = f"https://streamdb.p2pstream.online/#{video_id}"
                                return {
                                    'success': True,
                                    'file_code': video_id,
                                    'embed_url': embed_url,
                                    'host': 'streamp2p'
                                }

                            # No id found even though task completed — log full response for diagnosis
                            LOGGER.warning(f"StreamP2P task {task_id} completed but no video id found. Response: {data}")
                            return {
                                'success': False,
                                'error': 'No video id found in completed task (see logs)',
                                'host': 'streamp2p',
                                'raw_response': data
                            }

                        elif status in ['failed', 'error']:
                            error_msg = data.get('error', 'Upload failed')
                            return {
                                'success': False,
                                'error': f"Upload failed: {error_msg}",
                                'host': 'streamp2p'
                            }

                        # Still processing, wait and retry (5s)
                        await asyncio.sleep(5)
                    else:
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {await response.text()}",
                            'host': 'streamp2p'
                        }

            # Timeout
            return {
                'success': False,
                'error': 'Upload timeout - task did not complete in time',
                'host': 'streamp2p'
            }

        except Exception as e:
            LOGGER.error(f"StreamP2P status polling failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'host': 'streamp2p'
            }

    async def _poll_rpmshare_status(self, task_id: str, max_attempts: int = 120) -> Dict[str, Any]:
        """Poll RPMShare upload status until completion"""
        try:
            headers = {"api-token": self.api_keys['rpmshare']}

            for attempt in range(max_attempts):
                async with self.session.get(
                    self._build_p2p_api_url('rpmshare', f'video/advance-upload/{task_id}'),
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        status = data.get('status', '').lower()

                        LOGGER.info(f"RPMShare task {task_id} status: {status}")

                        if status == 'completed':
                            # Accept multiple shapes similar to StreamP2P
                            video_id = None

                            videos = data.get('videos')
                            if isinstance(videos, list) and len(videos) > 0:
                                first = videos[0]
                                if isinstance(first, dict):
                                    video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                elif isinstance(first, str):
                                    video_id = first

                            if not video_id:
                                video_id = data.get('videoId') or data.get('video_id') or data.get('id')

                            if not video_id and isinstance(data.get('result'), dict):
                                res_videos = data['result'].get('videos') or data['result'].get('data')
                                if isinstance(res_videos, list) and res_videos:
                                    first = res_videos[0]
                                    if isinstance(first, dict):
                                        video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                    elif isinstance(first, str):
                                        video_id = first

                            if not video_id:
                                link = data.get('assetUrl') or data.get('link') or data.get('url') or ''
                                if isinstance(link, str) and link:
                                    import re
                                    m = re.search(r"[#/]([^#/?&]+)$", link)
                                    if m:
                                        candidate = m.group(1)
                                        if candidate.lower() not in ('video', 'embed', 'watch'):
                                            video_id = candidate

                            if video_id:
                                embed_url = f"https://streamdb.rpmstream.online/#{video_id}"
                                return {
                                    'success': True,
                                    'file_code': video_id,
                                    'embed_url': embed_url,
                                    'host': 'rpmshare'
                                }

                            LOGGER.warning(f"RPMShare task {task_id} completed but no video id found. Response: {data}")
                            return {
                                'success': False,
                                'error': 'No video id found in completed task (see logs)',
                                'host': 'rpmshare',
                                'raw_response': data
                            }

                        elif status in ['failed', 'error']:
                            error_msg = data.get('error', 'Upload failed')
                            return {
                                'success': False,
                                'error': f"Upload failed: {error_msg}",
                                'host': 'rpmshare'
                            }

                        # Still processing, wait and retry
                        await asyncio.sleep(5)
                    else:
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {await response.text()}",
                            'host': 'rpmshare'
                        }

            # Timeout
            return {
                'success': False,
                'error': 'Upload timeout - task did not complete in time',
                'host': 'rpmshare'
            }

        except Exception as e:
            LOGGER.error(f"RPMShare status polling failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'host': 'rpmshare'
            }

    async def _poll_upnshare_status(self, task_id: str, max_attempts: int = 120) -> Dict[str, Any]:
        """Poll UPnShare upload status until completion"""
        try:
            headers = {"api-token": self.api_keys['upnshare']}

            for attempt in range(max_attempts):
                async with self.session.get(
                    self._build_p2p_api_url('upnshare', f'video/advance-upload/{task_id}'),
                    headers=headers
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        status = data.get('status', '').lower()

                        LOGGER.info(f"UPnShare task {task_id} status: {status}")

                        if status == 'completed':
                            # Accept multiple response shapes
                            video_id = None

                            videos = data.get('videos')
                            if isinstance(videos, list) and len(videos) > 0:
                                first = videos[0]
                                if isinstance(first, dict):
                                    video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                elif isinstance(first, str):
                                    video_id = first

                            if not video_id:
                                video_id = data.get('videoId') or data.get('video_id') or data.get('id')

                            if not video_id and isinstance(data.get('result'), dict):
                                res_videos = data['result'].get('videos') or data['result'].get('data')
                                if isinstance(res_videos, list) and res_videos:
                                    first = res_videos[0]
                                    if isinstance(first, dict):
                                        video_id = first.get('id') or first.get('_id') or first.get('video_id')
                                    elif isinstance(first, str):
                                        video_id = first

                            if not video_id:
                                link = data.get('assetUrl') or data.get('link') or data.get('url') or ''
                                if isinstance(link, str) and link:
                                    import re
                                    m = re.search(r"[#/]([^#/?&]+)$", link)
                                    if m:
                                        candidate = m.group(1)
                                        if candidate.lower() not in ('video', 'embed', 'watch'):
                                            video_id = candidate

                            if video_id:
                                embed_url = f"https://streamdb.upns.online/#{video_id}"

                                return {
                                    'success': True,
                                    'file_code': video_id,
                                    'embed_url': embed_url,
                                    'host': 'upnshare'
                                }

                            LOGGER.warning(f"UPnShare task {task_id} completed but no video id found. Response: {data}")
                            return {
                                'success': False,
                                'error': 'No video id found in completed task (see logs)',
                                'host': 'upnshare',
                                'raw_response': data
                            }

                        elif status in ['failed', 'error']:
                            error_msg = data.get('error', 'Upload failed')
                            return {
                                'success': False,
                                'error': f"Upload failed: {error_msg}",
                                'host': 'upnshare'
                            }

                        # Still processing, wait and retry
                        await asyncio.sleep(5)
                    else:
                        return {
                            'success': False,
                            'error': f"HTTP {response.status}: {await response.text()}",
                            'host': 'upnshare'
                        }

            # Timeout
            return {
                'success': False,
                'error': 'Upload timeout - task did not complete in time',
                'host': 'upnshare'
            }

        except Exception as e:
            LOGGER.error(f"UPnShare status polling failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'host': 'upnshare'
            }
        except Exception as e:
            LOGGER.error(f"UPnShare status check error: {e}")
            return {"status": "error", "message": str(e)}

    async def check_filemoon_status(self, file_code: str) -> Dict[str, Any]:
        """Check Filemoon upload status"""
        try:
            params = {
                "key": Config.FILEMOON_API_KEY,
                "file_code": file_code
            }
            
            async with self.session.get(
                f"{Config.FILEMOON_API_URL}/remote/status",
                params=params
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result
                else:
                    return {"status": "error", "message": f"HTTP {response.status}"}
                    
        except Exception as e:
            LOGGER.error(f"Filemoon status check error: {e}")
            return {"status": "error", "message": str(e)}

    async def upload_to_host(self, host: str, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload a file to a specific video host using remote URL"""
        try:
            if host not in self.api_keys:
                raise ValueError(f"Unsupported host: {host}")

            if not self.api_keys[host]:
                raise ValueError(f"Missing API key for {host}")

            LOGGER.info(f"Starting upload to {host}: {filename}")

            if host == 'filemoon':
                return await self.upload_to_filemoon(file_url, filename)
            elif host == 'streamp2p':
                return await self.upload_to_streamp2p(file_url, filename)
            elif host == 'rpmshare':
                return await self.upload_to_rpmshare(file_url, filename)
            elif host == 'upnshare':
                return await self.upload_to_upnshare(file_url, filename)
            else:
                raise ValueError(f"Upload method not implemented for {host}")

        except Exception as e:
            LOGGER.error(f"Upload to {host} failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'host': host
            }

    async def list_recent_uploads(self, host: str, hours: int = 48) -> List[Dict[str, Any]]:
        """List recent uploads from a host with proper error handling"""
        try:
            # Ensure session is initialized
            if not self.session:
                LOGGER.warning(f"Session not initialized for {host}, initializing now")
                await self.initialize()
                
            LOGGER.info(f"Listing recent uploads from {host} for last {hours} hours")
            
            if host == 'filemoon':
                return await self._list_filemoon_files(hours)
            elif host in ['streamp2p', 'rpmshare', 'upnshare']:
                return await self._list_p2p_files(host, hours)
            else:
                LOGGER.error(f"Unsupported host: {host}")
                return []

        except Exception as e:
            LOGGER.error(f"List files from {host} failed: {str(e)}")
            return []

    async def _list_filemoon_files(self, hours: int) -> List[Dict[str, Any]]:
        """List recent FileMoon files with PROPER TIME FILTERING and validation"""
        try:
            api_key = self.api_keys['filemoon']
            all_files = []
            page = 1
            per_page = 50
            max_pages = 20  # Increased to get more files

            # Calculate cutoff time for filtering
            cutoff_time = datetime.now() - timedelta(hours=hours)
            LOGGER.info(f"FileMoon: Starting file listing for last {hours} hours (since {cutoff_time})")

            while page <= max_pages:
                url = f"{self.base_urls['filemoon']}/api/file/list"
                params = {
                    'key': api_key,
                    'per_page': per_page,
                    'page': page
                }

                try:
                    if page > 1:
                        await asyncio.sleep(1)

                    async with self.session.get(url, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # Extract files from FileMoon response
                            files_on_page = []
                            if isinstance(data, dict) and 'result' in data:
                                result = data['result']
                                if isinstance(result, dict) and 'files' in result:
                                    files_on_page = result['files']

                            if not files_on_page:
                                LOGGER.info(f"FileMoon: No more files on page {page}")
                                break

                            files_added_this_page = 0
                            for file_info in files_on_page:
                                if not isinstance(file_info, dict):
                                    continue

                                # Extract file code - FileMoon uses 'file_code'
                                file_code = file_info.get('file_code', '').strip()

                                # Strict validation for file codes
                                if not file_code or len(file_code) < 3:
                                    LOGGER.warning(f"FileMoon: Invalid file_code '{file_code}'")
                                    continue

                                # Extract filename - FileMoon uses 'title'
                                filename = file_info.get('title', '').strip()
                                if not filename:
                                    LOGGER.warning(f"FileMoon: Empty filename for file_code {file_code}")
                                    continue

                                # Get upload date and apply time filtering
                                upload_date = file_info.get('uploaded', '')
                                
                                # Apply time filtering if not "all time" request
                                if hours < 999999:  # Not "all time"
                                    if not self._is_file_recent_by_date(upload_date, cutoff_time):
                                        LOGGER.debug(f"FileMoon: Skipping old file {filename}")
                                        continue

                                all_files.append({
                                    'filename': filename,
                                    'file_code': file_code,
                                    'upload_date': upload_date,
                                    'host': 'filemoon'
                                })
                                files_added_this_page += 1

                            LOGGER.info(f"FileMoon page {page}: Added {files_added_this_page}/{len(files_on_page)} files")

                            if len(files_on_page) < per_page:
                                break
                            page += 1
                            
                        elif response.status == 429:
                            LOGGER.warning(f"FileMoon: Rate limited, waiting 5 seconds")
                            await asyncio.sleep(5)
                            continue
                        else:
                            error_text = await response.text()
                            LOGGER.error(f"FileMoon API error {response.status}: {error_text}")
                            break
                            
                except Exception as e:
                    LOGGER.error(f"FileMoon: Error on page {page}: {e}")
                    break

            LOGGER.info(f"FileMoon: Retrieved {len(all_files)} files (time filtered)")
            return all_files

        except Exception as e:
            LOGGER.error(f"FileMoon file list failed: {str(e)}")
            return []

    async def _list_p2p_files(self, host: str, hours: int) -> List[Dict[str, Any]]:
        """List recent P2P host files with PROPER TIME FILTERING and validation"""
        try:
            api_key = self.api_keys[host]
            url = self._build_p2p_api_url(host, 'video/manage')
            headers = {
                'api-token': api_key,
                'Content-Type': 'application/json',
                'User-Agent': 'AutoUploadBot/1.0'
            }

            all_files = []
            page = 1
            per_page = 50
            max_pages = 20

            # Calculate cutoff time for filtering
            cutoff_time = datetime.now() - timedelta(hours=hours)
            LOGGER.info(f"{host}: Starting file listing for last {hours} hours (since {cutoff_time})")

            while page <= max_pages:
                params = {
                    'perPage': per_page,
                    'page': page
                }

                try:
                    if page > 1:
                        await asyncio.sleep(2)

                    async with self.session.get(url, headers=headers, params=params, timeout=aiohttp.ClientTimeout(total=30)) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # Extract videos from P2P response - they use 'data' key
                            videos_on_page = []
                            if isinstance(data, dict) and 'data' in data:
                                videos_on_page = data['data']

                            if not videos_on_page:
                                LOGGER.info(f"{host}: No more videos on page {page}")
                                break

                            videos_added_this_page = 0
                            for video in videos_on_page:
                                if not isinstance(video, dict):
                                    continue

                                # Extract file ID - P2P hosts use 'id'
                                file_id = video.get('id', '').strip()
                                
                                # Strict validation for file IDs
                                if not file_id or len(file_id) < 3:
                                    LOGGER.warning(f"{host}: Invalid file_id '{file_id}'")
                                    continue

                                # Extract filename - P2P hosts use 'name'
                                filename = video.get('name', '').strip()
                                if not filename:
                                    LOGGER.warning(f"{host}: Empty filename for file_id {file_id}")
                                    continue

                                # Get upload date and apply time filtering
                                upload_date = video.get('createdAt', '')
                                
                                # Apply time filtering if not "all time" request
                                if hours < 999999:  # Not "all time"
                                    if not self._is_file_recent_by_date(upload_date, cutoff_time):
                                        LOGGER.debug(f"{host}: Skipping old file {filename}")
                                        continue

                                all_files.append({
                                    'filename': filename,
                                    'file_code': file_id,
                                    'upload_date': upload_date,
                                    'host': host
                                })
                                videos_added_this_page += 1

                            LOGGER.info(f"{host} page {page}: Added {videos_added_this_page}/{len(videos_on_page)} videos")

                            if len(videos_on_page) < per_page:
                                break
                            page += 1
                            
                        elif response.status == 429:
                            LOGGER.warning(f"{host}: Rate limited, waiting 5 seconds")
                            await asyncio.sleep(5)
                            continue
                        else:
                            error_text = await response.text()
                            LOGGER.error(f"{host}: API error {response.status}: {error_text}")
                            break
                            
                except Exception as e:
                    LOGGER.error(f"{host}: Error on page {page}: {e}")
                    break

            LOGGER.info(f"{host}: Retrieved {len(all_files)} files (time filtered)")
            return all_files

        except Exception as e:
            LOGGER.error(f"{host} file list failed: {str(e)}")
            return []

    async def extract_embed_codes(self, hours: int = 48) -> List[Dict[str, Any]]:
        """Extract REAL embed codes from all hosts with PROPER time filtering and duplicate prevention"""
        try:
            LOGGER.info(f"🚀 Starting COMPREHENSIVE embed code extraction for last {hours} hours")
            all_files = []
            
            # Get files from all hosts with individual error handling
            for host in self.api_keys.keys():
                if not self.api_keys[host]:
                    LOGGER.warning(f"⚠️ Skipping {host} - no API key configured")
                    continue
                    
                try:
                    LOGGER.info(f"📡 Fetching files from {host} with TIME FILTER: {hours} hours...")
                    host_files = await self.list_recent_uploads(host, hours)
                    LOGGER.info(f"✅ {host}: Found {len(host_files)} files after time filtering")
                    all_files.extend(host_files)
                    
                    # Rate limiting between hosts
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    LOGGER.error(f"❌ Failed to get files from {host}: {e}")
                    continue

            LOGGER.info(f"📊 Total files collected from all hosts: {len(all_files)}")

            # Advanced duplicate prevention and embed code generation
            embed_data = []
            files_by_normalized_name = {}  # Use normalized filename as key
            
            for file_info in all_files:
                filename = file_info.get('filename', '').strip()
                if not filename:
                    LOGGER.warning("Skipping file with no filename")
                    continue
                
                # Normalize filename for better duplicate detection
                normalized_name = self._normalize_filename(filename)
                
                # Initialize entry for this normalized filename
                if normalized_name not in files_by_normalized_name:
                    files_by_normalized_name[normalized_name] = {
                        'filename': filename,  # Use original filename for display
                        'upload_date': file_info.get('upload_date', ''),
                        'embed_codes': {},
                        'hosts_processed': set()
                    }

                host = file_info['host']
                file_code = file_info.get('file_code', '').strip()

                # Comprehensive file code validation
                if not self._is_valid_file_code(file_code, host):
                    LOGGER.warning(f"❌ {host}: Invalid file_code '{file_code}' for {filename}, SKIPPING")
                    continue

                # Prevent duplicate entries for same host
                if host not in files_by_normalized_name[normalized_name]['hosts_processed']:
                    embed_code = self._generate_embed_code(host, file_code, filename)
                    files_by_normalized_name[normalized_name]['embed_codes'][host] = embed_code
                    files_by_normalized_name[normalized_name]['hosts_processed'].add(host)
                    LOGGER.info(f"✅ Generated embed code for '{filename}' on {host} with code: {file_code}")
                else:
                    LOGGER.info(f"⚠️ Duplicate detected: '{filename}' already processed for {host}")

            # Convert to final format
            for file_data in files_by_normalized_name.values():
                # Remove internal tracking
                file_data.pop('hosts_processed', None)
                
                # Only include files with valid embed codes
                if file_data['embed_codes']:
                    embed_data.append(file_data)
                else:
                    LOGGER.warning(f"⚠️ Excluding '{file_data['filename']}' - no valid embed codes")

            LOGGER.info(f"🎯 FINAL RESULT: Generated embed codes for {len(embed_data)} unique files")
            return embed_data

        except Exception as e:
            LOGGER.error(f"💥 Extract embed codes FAILED: {str(e)}")
            return []

    def _normalize_filename(self, filename: str) -> str:
        """Intelligent filename normalization with HTML entity handling"""
        import re
        import html
        import unicodedata
        
        # Convert to lowercase for case-insensitive comparison
        name = filename.lower().strip()
        
        # STEP 1: Decode HTML entities (FileMoon issue: &#40; &#41; &#58; etc.)
        name = html.unescape(name)
        
        # STEP 2: Handle additional HTML/URL encoding issues
        html_replacements = {
            '&#40;': '(',
            '&#41;': ')',
            '&#58;': ':',
            '&#45;': '-',
            '&#46;': '.',
            '&#32;': ' ',
            '&#39;': "'",
            '&#34;': '"',
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&apos;': "'",
            '%20': ' ',
            '%28': '(',
            '%29': ')',
            '%2D': '-',
            '%2E': '.'
        }
        
        for encoded, decoded in html_replacements.items():
            name = name.replace(encoded, decoded)
        
        # STEP 3: Normalize Unicode characters
        name = unicodedata.normalize('NFKD', name)
        
        # STEP 4: Remove ALL video file extensions (including double extensions)
        video_extensions = ['.mkv.mp4', '.avi.mp4', '.mov.mp4', '.wmv.mp4', '.flv.mp4', '.webm.mp4',
                           '.mkv', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg']
        
        # Sort by length (longest first) to handle double extensions correctly
        video_extensions.sort(key=len, reverse=True)
        
        for ext in video_extensions:
            if name.endswith(ext):
                name = name[:-len(ext)]
                break  # Only remove one extension
        
        # STEP 5: Intelligent content normalization
        # Remove release group tags (usually at the end after a dash)
        name = re.sub(r'-[a-zA-Z0-9]+$', '', name)
        
        # Remove bracketed and parentheses content (but preserve movie years)
        # Keep years in parentheses like (2025), (2024) etc.
        name = re.sub(r'\[.*?\]', '', name)  # Remove all bracketed content
        name = re.sub(r'\((?!\d{4}\)).*?\)', '', name)  # Remove parentheses except years
        
        # STEP 6: Normalize quality and technical indicators
        quality_patterns = [
            r'\b(1080p|720p|480p|4k|2160p|webdl|web-dl|webrip|bluray|dvdrip|hdtv|hdts)\b',
            r'\b(x264|x265|hevc|h264|h265|avc|10bit|8bit)\b',
            r'\b(hindi|english|tamil|telugu|dual\s*audio|esub|msub)\b',
            r'\b(5\.1|2\.0|stereo|mono|aac|ac3|dts)\b'
        ]
        
        for pattern in quality_patterns:
            name = re.sub(pattern, '', name, flags=re.IGNORECASE)
        
        # STEP 7: Remove hosting site suffixes
        site_patterns = [
            r'\b(hdhub4u|moviesmod|vegamovies|bollyflix|cinevood|moviesdrives)\b',
            r'\b(ms|com|in|is|cv|cafe|tube|gift|net|org)\b'
        ]
        
        for pattern in site_patterns:
            name = re.sub(pattern, '', name, flags=re.IGNORECASE)
        
        # STEP 8: Normalize separators and whitespace
        name = re.sub(r'[\s\-_\.]+', '', name)
        
        # STEP 9: Remove common duplicate indicators
        name = re.sub(r'\b(proper|repack|v2|v3|directors?cut|extended)\b', '', name, flags=re.IGNORECASE)
        
        return name.strip()
    
    def _intelligent_filename_match(self, filename1: str, filename2: str) -> bool:
        """Advanced filename matching for detecting duplicates across platforms"""
        # Normalize both filenames
        norm1 = self._normalize_filename(filename1)
        norm2 = self._normalize_filename(filename2)
        
        # Exact match after normalization
        if norm1 == norm2:
            return True
        
        # Fuzzy matching for slight variations
        import difflib
        similarity = difflib.SequenceMatcher(None, norm1, norm2).ratio()
        
        # Consider files as duplicates if >90% similar after normalization
        if similarity >= 0.9:
            return True
        
        return False
        
    def _is_valid_file_code(self, file_code: str, host: str) -> bool:
        """Comprehensive file code validation"""
        if not file_code or not isinstance(file_code, str):
            return False
            
        file_code = file_code.strip()
        
        # Check for invalid values
        invalid_codes = ['', 'code', 'null', 'undefined', 'none', '#']
        if file_code.lower() in invalid_codes:
            return False
            
        # Host-specific validation
        if host == 'filemoon':
            # FileMoon codes should be alphanumeric, typically 8-12 characters
            return len(file_code) >= 4 and file_code.isalnum()
        elif host in ['streamp2p', 'rpmshare', 'upnshare']:
            # P2P host codes are usually 5-10 characters, alphanumeric
            return len(file_code) >= 3 and file_code.replace('-', '').replace('_', '').isalnum()
            
        # Generic validation - at least 3 characters, not purely numeric
        return len(file_code) >= 3

    def _generate_embed_url_for_extraction(self, host: str, file_code: str, filename: str) -> str:
        """Generate embed URL for extraction based on host"""
        if host == 'filemoon':
            return f"https://filemoon.to/e/{file_code}/{filename}"
        elif host == 'streamp2p':
            return f"https://streamdb.p2pstream.online/#{file_code}"
        elif host == 'rpmshare':
            return f"https://streamdb.rpmstream.online/#{file_code}"
        elif host == 'upnshare':
            return f"https://streamdb.upns.online/#{file_code}"
        else:
            return f"https://{host}.com/embed/{file_code}"

    async def check_duplicate_files(self, hours: int = 24) -> List[Dict[str, Any]]:  
        """Check for REAL duplicate files within each host (multiple copies of same file on single platform)"""
        try:
            LOGGER.info(f"🔍 Checking for REAL duplicates within each host for last {hours} hours")
            all_duplicates = []

            # Check each host individually for internal duplicates
            for host in self.api_keys.keys():
                if not self.api_keys[host]:
                    LOGGER.warning(f"⚠️ Skipping {host} - no API key configured")
                    continue
                    
                try:
                    LOGGER.info(f"🔍 Scanning {host} for internal duplicates...")
                    host_files = await self.list_recent_uploads(host, hours)
                    
                    if not host_files:
                        LOGGER.info(f"✅ {host}: No files found")
                        continue
                    
                    LOGGER.info(f"📄 {host}: Analyzing {len(host_files)} files for duplicates")
                    
                    # Group files by normalized filename within this host
                    files_by_normalized_name = {}
                    
                    for file_info in host_files:
                        filename = file_info.get('filename', '').strip()
                        if not filename:
                            continue
                            
                        # Use the intelligent normalization from embed extractor
                        normalized_name = self._normalize_filename(filename)
                        
                        if normalized_name not in files_by_normalized_name:
                            files_by_normalized_name[normalized_name] = []
                            
                        files_by_normalized_name[normalized_name].append({
                            'filename': filename,
                            'file_code': file_info.get('file_code', ''),
                            'upload_date': file_info.get('upload_date', ''),
                            'host': host
                        })
                    
                    # Find groups with multiple files (real duplicates within this host)
                    host_duplicates_found = 0
                    for normalized_name, file_group in files_by_normalized_name.items():
                        if len(file_group) > 1:
                            host_duplicates_found += 1
                            
                            # Create duplicate entry for this group
                            duplicate_entry = {
                                'filename': file_group[0]['filename'],  # Use first file's name as display
                                'platforms': [host] * len(file_group),  # All instances are on this host
                                'file_ids': [f['file_code'] for f in file_group],
                                'count': len(file_group),
                                'host': host,  # Which host has the duplicates
                                'normalized_name': normalized_name,
                                'duplicate_files': file_group,  # All duplicate files info
                                'upload_dates': [f['upload_date'] for f in file_group]
                            }
                            
                            all_duplicates.append(duplicate_entry)
                            
                            LOGGER.info(f"🔄 {host}: Found duplicate group '{normalized_name}' with {len(file_group)} copies")
                            for f in file_group:
                                LOGGER.info(f"    - {f['filename']} ({f['file_code']})")
                    
                    LOGGER.info(f"📊 {host}: Found {host_duplicates_found} duplicate groups")
                    
                except Exception as e:
                    LOGGER.error(f"❌ Error checking duplicates in {host}: {e}")
                    continue
            
            LOGGER.info(f"🎯 TOTAL REAL DUPLICATES FOUND: {len(all_duplicates)} duplicate groups across all hosts")
            
            # Sort by host and filename for consistent results
            all_duplicates.sort(key=lambda x: (x['host'], x['filename']))
            
            return all_duplicates

        except Exception as e:
            LOGGER.error(f"💥 Check duplicate files FAILED: {str(e)}")
            return []

    async def upload_file_to_all_hosts(self, file_url: str, filename: str) -> Dict[str, Optional[str]]:
        """Upload file to all video hosting platforms sequentially"""
        results = {}
        
        # Sequential uploads as per user preference
        platforms = [
            ("streamp2p", self.upload_to_streamp2p),
            ("rpmshare", self.upload_to_rpmshare),
            ("upnshare", self.upload_to_upnshare),
            ("filemoon", self.upload_to_filemoon),
        ]
        
        for platform_name, upload_func in platforms:
            try:
                LOGGER.info(f"Starting upload to {platform_name}: {filename}")
                task_id = await upload_func(file_url, filename)
                results[platform_name] = task_id
                
                # Small delay between uploads to prevent rate limiting
                await asyncio.sleep(2)
                
            except Exception as e:
                LOGGER.error(f"Error uploading to {platform_name}: {e}")
                results[platform_name] = None
        
        return results

    async def get_embed_codes(self, upload_results: Dict[str, str], filename: str) -> Dict[str, str]:
        """Generate embed codes for uploaded files"""
        embed_codes = {}
        
        # StreamP2P embed code
        if upload_results.get("streamp2p"):
            video_id = upload_results["streamp2p"]
            embed_codes["streamp2p"] = f'<iframe src="https://streamdb.p2pstream.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        
        # RPMShare embed code
        if upload_results.get("rpmshare"):
            video_id = upload_results["rpmshare"]
            embed_codes["rpmshare"] = f'<iframe src="https://streamdb.rpmstream.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        
        # UPnShare embed code
        if upload_results.get("upnshare"):
            video_id = upload_results["upnshare"]
            embed_codes["upnshare"] = f'<iframe src="https://streamdb.upns.online/#{video_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        
        # Filemoon embed code - EXACT format as specified by user
        if upload_results.get("filemoon"):
            file_code = upload_results["filemoon"]
            embed_codes["filemoon"] = f'<iframe src="https://filemoon.to/e/{file_code}/{filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
        
        return embed_codes

    async def upload_to_all_hosts(self, file_url: str, filename: str) -> Dict[str, Any]:
        """Upload file to all video hosting platforms and return results"""
        try:
            LOGGER.info(f"Starting uploads for {filename} to all hosts")
            LOGGER.info(f"File URL: {file_url}")

            # For now, simulate successful uploads to test the functionality
            # This will be replaced with actual API calls once the video hosting APIs are properly configured

            # Simulate upload delay
            import asyncio
            await asyncio.sleep(2)  # Simulate upload time

            # Mock successful upload results
            upload_results = {
                "streamp2p": f"mock_id_{filename[:8]}",
                "rpmshare": f"mock_id_{filename[:8]}_rpm",
                "upnshare": f"mock_id_{filename[:8]}_upn",
                "filemoon": f"mock_id_{filename[:8]}_fm"
            }

            # Generate embed codes using the mock IDs
            embed_codes = await self.get_embed_codes(upload_results, filename)

            LOGGER.info(f"Mock upload completed for {filename}")

            return {
                "status": "success",
                "upload_results": upload_results,
                "embed_codes": embed_codes,
                "filename": filename
            }

        except Exception as e:
            LOGGER.error(f"Upload to all hosts error: {e}")
            raise e

    async def get_platform_files(self, platform: str, time_range: str) -> List[Dict[str, Any]]:
        """Get files from a specific platform"""
        try:
            files = []

            if platform == "streamp2p":
                headers = {"Authorization": f"Bearer {Config.STREAMP2P_API_KEY}"}
                async with self.session.get(f"{Config.STREAMP2P_API_URL}/video/list", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        # support different shapes
                        if isinstance(data, dict):
                            files = data.get("videos") or data.get('data') or data.get('result') or []
                        elif isinstance(data, list):
                            files = data

            elif platform == "rpmshare":
                headers = {"Authorization": f"Bearer {Config.RPMSHARE_API_KEY}"}
                async with self.session.get(f"{Config.RPMSHARE_API_URL}/video/list", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict):
                            files = data.get("videos") or data.get('data') or data.get('result') or []
                        elif isinstance(data, list):
                            files = data

            elif platform == "upnshare":
                headers = {"Authorization": f"Bearer {Config.UPNSHARE_API_KEY}"}
                async with self.session.get(f"{Config.UPNSHARE_API_URL}/video/list", headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        if isinstance(data, dict):
                            files = data.get("videos") or data.get('data') or data.get('result') or []
                        elif isinstance(data, list):
                            files = data

            elif platform == "filemoon":
                params = {"key": Config.FILEMOON_API_KEY}
                async with self.session.get(f"{Config.FILEMOON_API_URL}/file/list", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("status") == 200:
                            files = data.get("result", {}).get("files", [])

            # Filter by time range if needed
            if time_range == "24h":
                from datetime import datetime, timedelta
                cutoff_time = datetime.now() - timedelta(hours=24)
                files = [f for f in files if self._is_file_recent(f, cutoff_time)]

            return files

        except Exception as e:
            LOGGER.error(f"Error getting files from {platform}: {e}")
            return []

    def _is_file_recent(self, file_data: Dict, cutoff_time) -> bool:
        """Check if file is recent based on upload time"""
        try:
            # This would need to be implemented based on each platform's date format
            # For now, return True to include all files
            return True
        except:
            return True

    def _is_series_file(self, filename: str) -> bool:
        """Check if filename appears to be a series file (should not be considered duplicate)"""
        import re

        # Patterns for series files
        series_patterns = [
            r'[Ss]\d+[Ee]\d+',  # S01E01, s01e01
            r'[Ss]eason\s*\d+',  # Season 1, season 1
            r'[Ee]pisode\s*\d+', # Episode 1, episode 1
            r'\b\d+x\d+\b',      # 1x01
            r'[Pp]art\s*\d+',    # Part 1, part 1
        ]

        for pattern in series_patterns:
            if re.search(pattern, filename, re.IGNORECASE):
                return True

        return False

    async def check_duplicates(self, time_range: str) -> List[Dict[str, Any]]:
        """Check for duplicate files across platforms"""
        try:
            LOGGER.info(f"Checking duplicates for time range: {time_range}")

            # Get files from all platforms
            platforms = ["streamp2p", "rpmshare", "upnshare", "filemoon"]
            all_files = {}

            for platform in platforms:
                files = await self.get_platform_files(platform, time_range)
                all_files[platform] = files

            # Find duplicates by comparing filenames and sizes
            duplicates = []
            file_map = {}

            # Build a map of files by normalized filename
            for platform, files in all_files.items():
                for file_data in files:
                    filename = file_data.get("name", file_data.get("filename", ""))
                    file_size = file_data.get("size", 0)

                    # Skip series files as they shouldn't be considered duplicates
                    if self._is_series_file(filename):
                        continue

                    # Normalize filename for comparison (remove extensions, lowercase)
                    normalized_name = filename.lower().rsplit('.', 1)[0] if '.' in filename else filename.lower()

                    key = f"{normalized_name}_{file_size}"

                    if key not in file_map:
                        file_map[key] = []

                    file_map[key].append({
                        "platform": platform,
                        "filename": filename,
                        "file_id": file_data.get("id", file_data.get("file_code", "")),
                        "size": file_size,
                        "upload_date": file_data.get("created_at", file_data.get("upload_date", "")),
                        "embed_code": self._generate_embed_code(platform, file_data.get("id", file_data.get("file_code", "")), filename)
                    })

            # Find actual duplicates (files that appear on multiple platforms)
            for key, file_list in file_map.items():
                if len(file_list) > 1:
                    duplicates.append({
                        "filename": file_list[0]["filename"],
                        "size": file_list[0]["size"],
                        "platforms": [f["platform"] for f in file_list],
                        "file_ids": [f["file_id"] for f in file_list],
                        "upload_dates": [f["upload_date"] for f in file_list],
                        "embed_codes": {f["platform"]: f["embed_code"] for f in file_list}
                    })

            LOGGER.info(f"Found {len(duplicates)} duplicate file groups")
            return duplicates

        except Exception as e:
            LOGGER.error(f"Check duplicates error: {e}")
            raise e

    def _generate_embed_code(self, platform: str, file_id: str, filename: str) -> str:
        """Generate embed code for a specific platform and file using EXACT user-specified formats"""
        if platform == "streamp2p":
            # Sample: <iframe src="https://streamdb.p2pstream.online/#8wwqu" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>
            return f'<iframe src="https://streamdb.p2pstream.online/#{file_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        elif platform == "rpmshare":
            # Sample: <iframe src="https://streamdb.rpmstream.online/#6syts" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>
            return f'<iframe src="https://streamdb.rpmstream.online/#{file_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        elif platform == "upnshare":
            # Sample: <iframe src="https://streamdb.upns.online/#ipcmgm" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>
            return f'<iframe src="https://streamdb.upns.online/#{file_id}" width="100%" height="100%" frameborder="0" allowfullscreen></iframe>'
        elif platform == "filemoon":
            # Sample: <iframe src="https://filemoon.to/e/6gsmq8ziegir/28_Years_Later_2025_1080p_AMZN_WEB-DL_DDP5_1_H_264-Vegamovies_is" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>
            return f'<iframe src="https://filemoon.to/e/{file_id}/{filename}" frameborder="0" marginwidth="0" marginheight="0" scrolling="no" width="640" height="360" allowfullscreen></iframe>'
        else:
            LOGGER.warning(f"Unknown platform for embed code generation: {platform}")
            return ""

    async def extract_embed_codes_mock_deprecated(self, hours: int) -> List[Dict[str, Any]]:
        """Deprecated mock function (kept for backward-compatibility). Use the real implementation above."""
        return await self.extract_embed_codes(hours)

    def _is_file_recent_by_date(self, upload_date: str, cutoff_time) -> bool:
        """Check if file is recent based on upload date string with comprehensive date parsing"""
        try:
            if not upload_date:
                LOGGER.debug("No upload date provided, including file")
                return True  # Include files without date info

            upload_date = str(upload_date).strip()
            if not upload_date:
                return True

            # Try to parse various date formats
            date_formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%dT%H:%M:%S",
                "%Y-%m-%dT%H:%M:%SZ",
                "%Y-%m-%dT%H:%M:%S.%fZ",
                "%Y-%m-%d",
                "%d/%m/%Y",
                "%m/%d/%Y",
                "%d-%m-%Y",
                "%m-%d-%Y",
                "%Y/%m/%d",
                "%d.%m.%Y",
                "%Y.%m.%d"
            ]

            for fmt in date_formats:
                try:
                    file_date = datetime.strptime(upload_date, fmt)
                    is_recent = file_date >= cutoff_time
                    LOGGER.debug(f"Parsed date '{upload_date}' as {file_date}, recent: {is_recent}")
                    return is_recent
                except ValueError:
                    continue

            # Try parsing as Unix timestamp
            try:
                if upload_date.isdigit():
                    timestamp = int(upload_date)
                    # Handle both seconds and milliseconds timestamps
                    if timestamp > 1000000000000:  # Milliseconds
                        timestamp = timestamp / 1000
                    file_date = datetime.fromtimestamp(timestamp)
                    is_recent = file_date >= cutoff_time
                    LOGGER.debug(f"Parsed timestamp '{upload_date}' as {file_date}, recent: {is_recent}")
                    return is_recent
            except (ValueError, OSError):
                pass

            # If we can't parse the date, include the file but log it
            LOGGER.warning(f"Could not parse upload date '{upload_date}', including file")
            return True

        except Exception as e:
            LOGGER.error(f"Error checking file date '{upload_date}': {e}")
            return True

    async def generate_embed_csv(self) -> str:
        """Generate CSV file with embed codes"""
        try:
            import csv
            import tempfile
            from datetime import datetime

            # Get embed data
            embed_data = await self.extract_embed_codes(48)

            # Create temporary CSV file
            csv_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)

            with csv_file as f:
                writer = csv.writer(f)
                writer.writerow(['Filename', 'Embed Codes'])

                for item in embed_data:
                    filename = item.get('filename', 'Unknown')
                    embed_codes = item.get('embed_codes', {})

                    # Format embed codes as specified
                    embed_text = '\n\n'.join([
                        embed_codes.get('streamp2p', ''),
                        embed_codes.get('rpmshare', ''),
                        embed_codes.get('upnshare', ''),
                        embed_codes.get('filemoon', '')
                    ])

                    writer.writerow([filename, embed_text])

            return csv_file.name

        except Exception as e:
            LOGGER.error(f"Generate embed CSV error: {e}")
            raise e
