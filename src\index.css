@tailwind base;
@tailwind components;
@tailwind utilities;

/* Neo-Brutalism Design System - Bold, harsh, high contrast
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Neo-Brutalism Base Colors - Stark contrasts */
    --background: 0 0% 98%;
    --foreground: 0 0% 0%;
    
    /* Brutal Cards - Pure white with harsh borders */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    
    /* Bold Primary - Electric Blue */
    --primary: 217 100% 50%;
    --primary-foreground: 0 0% 100%;
    
    /* Harsh Secondary - Yellow accent */
    --secondary: 51 100% 50%;
    --secondary-foreground: 0 0% 0%;
    
    /* Muted areas - Light gray */
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 20%;
    
    /* Accent - Neon Green */
    --accent: 120 100% 40%;
    --accent-foreground: 0 0% 100%;
    
    /* Destructive - Bold Red */
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;
    
    /* Borders - Always black and thick */
    --border: 0 0% 0%;
    --input: 0 0% 100%;
    --ring: 217 100% 50%;

    /* Neo-Brutalism specific colors */
    --purple: 260 100% 50%;
    --purple-foreground: 0 0% 100%;
    --orange: 25 100% 50%;
    --orange-foreground: 0 0% 0%;
    
    --radius: 0rem; /* No rounded corners for brutalism */
    
    /* Brutal shadows */
    --shadow-brutal: 4px 4px 0px 0px hsl(0 0% 0%);
    --shadow-brutal-lg: 8px 8px 0px 0px hsl(0 0% 0%);
  }

  .dark {
    /* Dark mode - inverted brutalism */
    --background: 0 0% 8%;
    --foreground: 0 0% 100%;
    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;
    --primary: 217 100% 60%;
    --primary-foreground: 0 0% 0%;
    --secondary: 51 100% 60%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 80%;
    --accent: 120 100% 50%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 100% 60%;
    --destructive-foreground: 0 0% 0%;
    --border: 0 0% 100%;
    --input: 0 0% 0%;
    --ring: 217 100% 60%;
    --purple: 260 100% 60%;
    --purple-foreground: 0 0% 0%;
    --orange: 25 100% 60%;
    --orange-foreground: 0 0% 0%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-mono;
  }
}

@layer utilities {
  /* Neo-Brutalism utility classes */
  .brutal-border {
    @apply border-4 border-black;
  }
  
  .brutal-shadow {
    box-shadow: var(--shadow-brutal);
  }
  
  .brutal-shadow-lg {
    box-shadow: var(--shadow-brutal-lg);
  }
  
  .brutal-card {
    @apply bg-card brutal-border brutal-shadow p-4;
  }
  
  .brutal-button {
    @apply brutal-border brutal-shadow font-bold uppercase tracking-wide transition-all duration-75;
  }
  
  .brutal-button:hover {
    @apply translate-x-1 translate-y-1;
    box-shadow: 2px 2px 0px 0px hsl(0 0% 0%);
  }
  
  .brutal-button:active {
    @apply translate-x-1 translate-y-1;
    box-shadow: 1px 1px 0px 0px hsl(0 0% 0%);
  }
}