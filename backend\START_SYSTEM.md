# 🚀 START AUTOUPLOADBOT SYSTEM

## ✅ FIXED ISSUES:
- Fixed RClone path configuration
- Fixed PowerShell batch file execution
- Created PowerShell-compatible startup script

---

## 📋 STEP-BY-STEP STARTUP PROCESS

### **Step 1: Start Aria2 (FIXED)**

**Option A: PowerShell Script (RECOMMENDED)**
```powershell
.\start_aria2.ps1
```

**Option B: Batch File**
```powershell
.\start_aria2.bat
```

**Expected Output:**
```
Starting Aria2 RPC server...
Starting Aria2 with config: aria2\aria2.conf
Aria2 will be available at: http://localhost:6800/jsonrpc
Keep this window open while using the bot!

[INFO] IPv4 RPC: listening on TCP port 6800
```

**✅ Keep this terminal open!**

---

### **Step 2: Start Backend Server**

**Open a NEW PowerShell terminal and run:**
```powershell
cd backend
python -m uvicorn main:app --host localhost --port 8001 --reload
```

**Expected Output:**
```
INFO:     Will watch for changes in these directories: ['G:\\...\\backend']
INFO:     Uvicorn running on http://localhost:8001 (Press CTRL+C to quit)
INFO:     Started reloader process
INFO:     Started server process
INFO:     Waiting for application startup.
Starting AutoUploadBot backend...
All managers initialized successfully
INFO:     Application startup complete.
```

---

### **Step 3: Start Frontend**

**Open a NEW PowerShell terminal and run:**
```powershell
cd ..
npm run dev
```

**Expected Output:**
```
> automatic-upload-bot@0.1.0 dev
> vite

  VITE v5.0.0  ready in 500 ms

  ➜  Local:   http://localhost:8080/
  ➜  Network: use --host to expose
```

---

## 🌐 ACCESS YOUR APPLICATION

**Once all three services are running:**

1. **Frontend**: http://localhost:8080
2. **Backend API**: http://localhost:8001
3. **API Documentation**: http://localhost:8001/docs
4. **Health Check**: http://localhost:8001/health

---

## 🧪 TEST THE SYSTEM

### **Test 1: Health Check**
Visit: http://localhost:8001/health

**Expected Response:**
```json
{
  "status": "healthy",
  "aria2_connected": true,
  "video_hosts_ready": true
}
```

### **Test 2: Frontend Interface**
Visit: http://localhost:8080

**You should see:**
- Upload Manager tab
- Progress Monitor tab
- Duplicate File Checker tab
- Embed Code Extract tab
- Clean up Google Drives tab

### **Test 3: Small Download Test**
1. Go to Upload Manager tab
2. Enter a small file URL (e.g., a small video file)
3. Click download
4. Check if it appears in your Google Drive Primary folder

---

## 🚨 TROUBLESHOOTING

### **Issue: Aria2 won't start**
**Solution:**
```powershell
# Check if port 6800 is in use
netstat -an | findstr :6800

# If in use, kill the process
taskkill /f /im aria2c.exe

# Try starting again
.\start_aria2.ps1
```

### **Issue: Backend server fails**
**Check:**
1. Is your service account file in `accounts/service_account.json`?
2. Are your folder IDs correct in `.env`?
3. Are your API keys correct in `.env`?

### **Issue: RClone connection fails**
**Solution:**
```powershell
# Test RClone manually
rclone\rclone-current-windows-amd64\rclone.exe lsd gdrive:

# If it fails, check your service account permissions
```

---

## ✅ VERIFICATION CHECKLIST

- [ ] **Aria2 running**: Shows "listening on TCP port 6800"
- [ ] **Backend running**: Shows "Application startup complete"
- [ ] **Frontend running**: Accessible at http://localhost:8080
- [ ] **Health check**: Returns "healthy" status
- [ ] **Google Drive**: Service account has access to both folders
- [ ] **API Keys**: All 4 video hosting API keys are valid

---

## 🎯 YOUR WORKFLOW IS NOW READY

**Upload Manager:**
- Enter URLs or magnet links
- Files download directly to Google Drive Primary
- Archives auto-extract to Google Drive Secondary
- Videos upload to all 4 hosting platforms

**Progress Monitor:**
- Real-time download/upload progress
- Speed and ETA tracking
- WebSocket updates every 3 seconds

**Duplicate File Checker:**
- Check for duplicates across platforms
- 24-hour and all-time scanning

**Embed Code Extract:**
- Extract embed codes from last 48 hours
- CSV export with proper formatting

**Clean up Google Drives:**
- Manage files in both drives
- Bulk deletion capabilities

---

## 🚀 READY TO USE!

Your AutoUploadBot is now fully functional with:
- ✅ Server-to-server operations only
- ✅ Direct Google Drive integration
- ✅ Archive extraction for all formats
- ✅ Sequential uploads to video hosts
- ✅ Real-time progress monitoring
- ✅ Embed code generation
- ✅ Drive cleanup capabilities

**Start with a small test file to verify everything works!**
