import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";
import {
  Archive,
  FolderOpen,
  Download,
  RefreshCw,
  FileVideo,
  HardDrive,
  Zap,
  CheckCircle,
  AlertCircle,
  Trash2,
  FolderX,
  Clock,
  ArrowRight
} from "lucide-react";
import { apiService, GDriveFile } from "@/services/api";

interface ExtractionJob {
  id: string;
  filename: string;
  status: 'pending' | 'extracting' | 'completed' | 'failed';
  progress: number;
  extracted_files: number;
  total_files: number;
  extracted_file_names?: string[];
  current_step?: string;
  temp_folder?: string;
  temp_cleaned?: boolean;
  error?: string;
}

const ZipExtractor = () => {
  const [zipFiles, setZipFiles] = useState<GDriveFile[]>([]);
  const [extractionJobs, setExtractionJobs] = useState<ExtractionJob[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadZipFiles();
    loadExtractionJobs();
  }, []);

  const loadZipFiles = async () => {
    setIsScanning(true);
    try {
      const response = await apiService.listGDriveFiles('primary');
      // Filter for zip files
      const zips = response.files.filter((file: GDriveFile) => 
        file.name.toLowerCase().endsWith('.zip') || 
        file.name.toLowerCase().endsWith('.rar') ||
        file.name.toLowerCase().endsWith('.7z')
      );
      setZipFiles(zips);
      
      toast({
        title: "Scan Complete",
        description: `Found ${zips.length} archive files in Primary Google Drive`,
      });
    } catch (error) {
      console.error("Failed to load zip files:", error);
      toast({
        title: "Error",
        description: "Failed to scan Primary Google Drive",
        variant: "destructive",
      });
    } finally {
      setIsScanning(false);
    }
  };

  const loadExtractionJobs = async () => {
    try {
      const response = await apiService.getExtractionStatus();
      setExtractionJobs(response.jobs || []);
    } catch (error) {
      console.error("Failed to load extraction jobs:", error);
    }
  };

  const handleExtractFile = async (file: GDriveFile) => {
    setIsLoading(true);
    try {
      const response = await apiService.extractArchive({
        file_id: file.id,
        filename: file.name,
        source_drive: 'primary',
        target_drive: 'secondary'
      });

      if (response.status === 'success') {
        toast({
          title: "Extraction Started",
          description: `Started extracting ${file.name} to Secondary Google Drive`,
        });
        
        // Add to extraction jobs
        const newJob: ExtractionJob = {
          id: response.job_id,
          filename: file.name,
          status: 'pending',
          progress: 0,
          extracted_files: 0,
          total_files: 0
        };
        setExtractionJobs(prev => [...prev, newJob]);
        
        // Start monitoring this job
        monitorExtractionJob(response.job_id);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to start extraction: ${error}`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExtractAll = async () => {
    setIsLoading(true);
    try {
      for (const file of zipFiles) {
        await handleExtractFile(file);
        // Small delay between extractions
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      toast({
        title: "Batch Extraction Started",
        description: `Started extracting ${zipFiles.length} archive files`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start batch extraction",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const monitorExtractionJob = async (jobId: string) => {
    const checkStatus = async () => {
      try {
        const response = await apiService.getExtractionJobStatus(jobId);

        setExtractionJobs(prev =>
          prev.map(job =>
            job.id === jobId
              ? { ...job, ...response }
              : job
          )
        );

        // Continue monitoring if still in progress
        if (response.status === 'extracting' || response.status === 'pending') {
          setTimeout(checkStatus, 2000);
        } else if (response.status === 'completed') {
          toast({
            title: "Extraction Complete",
            description: `Successfully extracted ${response.extracted_files} video files from ${response.filename}`,
          });
        } else if (response.status === 'failed') {
          toast({
            title: "Extraction Failed",
            description: response.error || "Unknown error occurred",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Failed to check extraction status:", error);
      }
    };

    checkStatus();
  };

  const handleCleanupTempFiles = async (jobId: string) => {
    try {
      const response = await fetch(`http://localhost:8006/api/extract/cleanup/${jobId}`, {
        method: 'POST',
      });

      if (response.ok) {
        const result = await response.json();

        // Update the job to mark temp as cleaned
        setExtractionJobs(prev =>
          prev.map(job =>
            job.id === jobId
              ? { ...job, temp_cleaned: true, temp_folder: '' }
              : job
          )
        );

        toast({
          title: "Cleanup Complete",
          description: result.message,
        });
      } else {
        throw new Error('Failed to cleanup temp files');
      }
    } catch (error) {
      toast({
        title: "Cleanup Failed",
        description: `Failed to cleanup temp files: ${error}`,
        variant: "destructive",
      });
    }
  };

  const handleCleanupAllCompleted = async () => {
    const completedJobs = extractionJobs.filter(job =>
      job.status === 'completed' && job.temp_folder && !job.temp_cleaned
    );

    if (completedJobs.length === 0) {
      toast({
        title: "No Cleanup Needed",
        description: "No completed jobs with temp files found",
      });
      return;
    }

    setIsLoading(true);
    try {
      for (const job of completedJobs) {
        await handleCleanupTempFiles(job.id);
        // Small delay between cleanups
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      toast({
        title: "Bulk Cleanup Complete",
        description: `Cleaned up temp files for ${completedJobs.length} jobs`,
      });
    } catch (error) {
      toast({
        title: "Bulk Cleanup Failed",
        description: "Some temp files may not have been cleaned",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearCompletedJobs = () => {
    setExtractionJobs(prev => prev.filter(job => job.status !== 'completed'));
    toast({
      title: "UI Cleaned",
      description: "Removed completed jobs from display",
    });
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB", "TB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'extracting':
        return <Zap className="w-4 h-4 text-yellow-500 animate-pulse" />;
      default:
        return <Archive className="w-4 h-4 text-blue-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'failed':
        return 'bg-red-500';
      case 'extracting':
        return 'bg-yellow-500';
      default:
        return 'bg-blue-500';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="brutal-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono">
            <Archive className="w-5 h-5 text-purple" />
            ZIP EXTRACTOR - PRIMARY TO SECONDARY GDRIVE
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-4 flex-wrap">
            <Button
              onClick={loadZipFiles}
              disabled={isScanning}
              className="brutal-button"
              variant="outline"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isScanning ? 'animate-spin' : ''}`} />
              {isScanning ? 'SCANNING...' : 'SCAN PRIMARY DRIVE'}
            </Button>

            <Button
              onClick={handleExtractAll}
              disabled={isLoading || zipFiles.length === 0}
              className="brutal-button"
            >
              <Zap className="w-4 h-4 mr-2" />
              EXTRACT ALL ({zipFiles.length})
            </Button>

            <Button
              onClick={handleCleanupAllCompleted}
              disabled={isLoading || extractionJobs.filter(j => j.status === 'completed' && j.temp_folder && !j.temp_cleaned).length === 0}
              className="brutal-button"
              variant="outline"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              CLEANUP ALL TEMP
            </Button>

            <Button
              onClick={handleClearCompletedJobs}
              disabled={extractionJobs.filter(j => j.status === 'completed').length === 0}
              className="brutal-button"
              variant="outline"
            >
              <FolderX className="w-4 h-4 mr-2" />
              CLEAR COMPLETED
            </Button>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold font-mono text-purple">{zipFiles.length}</div>
              <div className="text-sm font-mono text-muted-foreground">ARCHIVE FILES</div>
            </div>
            <div>
              <div className="text-2xl font-bold font-mono text-blue-500">{extractionJobs.filter(j => j.status === 'extracting').length}</div>
              <div className="text-sm font-mono text-muted-foreground">EXTRACTING</div>
            </div>
            <div>
              <div className="text-2xl font-bold font-mono text-green-500">{extractionJobs.filter(j => j.status === 'completed').length}</div>
              <div className="text-sm font-mono text-muted-foreground">COMPLETED</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Archive Files List */}
      <Card className="brutal-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono">
            <FolderOpen className="w-5 h-5 text-blue-500" />
            ARCHIVE FILES IN PRIMARY DRIVE
          </CardTitle>
        </CardHeader>
        <CardContent>
          {zipFiles.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Archive className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="font-mono">No archive files found. Click SCAN PRIMARY DRIVE to refresh.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {zipFiles.map((file) => (
                <div key={file.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Archive className="w-5 h-5 text-purple" />
                    <div>
                      <div className="font-mono font-medium">{file.name}</div>
                      <div className="text-sm text-muted-foreground font-mono">
                        {formatFileSize(parseInt(file.size))} • {new Date(file.createdTime).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleExtractFile(file)}
                    disabled={isLoading}
                    className="brutal-button"
                    size="sm"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    EXTRACT
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Extraction Jobs */}
      {extractionJobs.length > 0 && (
        <Card className="brutal-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 font-mono">
              <HardDrive className="w-5 h-5 text-green-500" />
              EXTRACTION PROGRESS
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {extractionJobs.map((job) => (
                <div key={job.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(job.status)}
                      <span className="font-mono font-medium">{job.filename}</span>
                      <Badge className={`${getStatusColor(job.status)} text-white font-mono`}>
                        {job.status.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-sm font-mono text-muted-foreground">
                        {job.extracted_files}/{job.total_files} files
                      </div>
                      {job.status === 'completed' && job.temp_folder && !job.temp_cleaned && (
                        <Button
                          onClick={() => handleCleanupTempFiles(job.id)}
                          size="sm"
                          variant="outline"
                          className="brutal-button text-xs"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          CLEANUP TEMP
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Current Step */}
                  {job.current_step && (
                    <div className="mb-2 flex items-center gap-2 text-sm font-mono text-blue-600">
                      <Clock className="w-4 h-4" />
                      {job.current_step}
                    </div>
                  )}

                  {/* Progress Bar */}
                  {job.status === 'extracting' && (
                    <div className="mb-3">
                      <Progress value={job.progress} className="h-2" />
                      <div className="text-xs font-mono text-muted-foreground mt-1">
                        {job.progress}% complete
                      </div>
                    </div>
                  )}

                  {/* Extracted Files List */}
                  {job.status === 'completed' && job.extracted_file_names && job.extracted_file_names.length > 0 && (
                    <div className="mt-3 p-3 bg-green-50 rounded border">
                      <div className="flex items-center gap-2 mb-2">
                        <FileVideo className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-mono font-medium text-green-800">
                          Extracted Video Files:
                        </span>
                      </div>
                      <div className="space-y-1">
                        {job.extracted_file_names.map((fileName, index) => (
                          <div key={index} className="flex items-center gap-2 text-xs font-mono text-green-700">
                            <ArrowRight className="w-3 h-3" />
                            {fileName}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Temp Folder Info */}
                  {job.temp_folder && !job.temp_cleaned && (
                    <div className="mt-3 p-3 bg-yellow-50 rounded border">
                      <div className="flex items-center gap-2 mb-1">
                        <FolderX className="w-4 h-4 text-yellow-600" />
                        <span className="text-sm font-mono font-medium text-yellow-800">
                          Temporary Files:
                        </span>
                      </div>
                      <div className="text-xs font-mono text-yellow-700">
                        Temp folder: {job.temp_folder}
                      </div>
                      <div className="text-xs font-mono text-yellow-600 mt-1">
                        ⚠️ Click "CLEANUP TEMP" to remove temporary files from Google Drive
                      </div>
                    </div>
                  )}

                  {/* Temp Cleaned Status */}
                  {job.temp_cleaned && (
                    <div className="mt-3 p-3 bg-green-50 rounded border">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-mono font-medium text-green-800">
                          Temporary files cleaned ✅
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Error Display */}
                  {job.error && (
                    <div className="mt-3 p-3 bg-red-50 rounded border">
                      <div className="flex items-center gap-2 mb-1">
                        <AlertCircle className="w-4 h-4 text-red-600" />
                        <span className="text-sm font-mono font-medium text-red-800">Error:</span>
                      </div>
                      <div className="text-sm font-mono text-red-700">{job.error}</div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ZipExtractor;
