import { File } from "lucide-react";

const Header = () => {
  return (
    <header className="bg-black text-white p-4 brutal-shadow-lg">
      <div className="container mx-auto flex items-center gap-4">
        <div className="bg-primary p-3 brutal-border">
          <File className="w-7 h-7 text-white" />
        </div>
        <div>
          <h1 className="text-2xl font-bold tracking-wider">FILE LEECH BOT</h1>
          <p className="text-sm font-mono opacity-90">DOWNLOAD • MIRROR • UPLOAD • REPEAT</p>
        </div>
        <div className="ml-auto">
          <div className="bg-accent px-4 py-2 brutal-border">
            <span className="text-white font-bold text-sm">● ONLINE</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;