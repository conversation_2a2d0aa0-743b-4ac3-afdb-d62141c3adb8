#!/usr/bin/env python3
"""
Analyze duplicate detection issues in embed code extraction
This script will identify exactly where duplicates are coming from
"""

import asyncio
import logging
import sys
import json
from pathlib import Path
from collections import defaultdict

# Add the backend directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def analyze_duplicates():
    """Analyze the exact duplicate detection issue"""
    print("🔍 ANALYZING DUPLICATE DETECTION ISSUES")
    print("=" * 60)
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        # Get all files from all hosts with 48-hour filter
        all_files = []
        
        for host in vm.api_keys.keys():
            if not vm.api_keys[host]:
                print(f"⚠️ Skipping {host} - no API key")
                continue
                
            print(f"\n📡 Getting files from {host}...")
            host_files = await vm.list_recent_uploads(host, 48)
            print(f"✅ {host}: {len(host_files)} files")
            
            # Show raw files for analysis
            for i, file_info in enumerate(host_files[:5]):  # Show first 5
                filename = file_info.get('filename', 'Unknown')
                file_code = file_info.get('file_code', 'No code')
                upload_date = file_info.get('upload_date', 'No date')
                print(f"  {i+1}. {filename} | {file_code} | {upload_date}")
            
            all_files.extend(host_files)
            await asyncio.sleep(1)
        
        print(f"\n📊 TOTAL RAW FILES: {len(all_files)}")
        
        # Analyze filename normalization
        print(f"\n🔤 ANALYZING FILENAME NORMALIZATION:")
        filename_groups = defaultdict(list)
        
        for file_info in all_files:
            filename = file_info.get('filename', '')
            normalized = vm._normalize_filename(filename)
            
            filename_groups[normalized].append({
                'original': filename,
                'host': file_info['host'],
                'file_code': file_info.get('file_code', ''),
                'upload_date': file_info.get('upload_date', '')
            })
        
        # Show groups with potential duplicates
        print(f"\nNormalized filename groups:")
        duplicate_groups = 0
        total_unique_files = 0
        
        for normalized, files in filename_groups.items():
            total_unique_files += 1
            if len(files) > 1:
                duplicate_groups += 1
                print(f"\n🔄 DUPLICATE GROUP {duplicate_groups}: '{normalized}'")
                for file_info in files:
                    print(f"  - {file_info['original']} ({file_info['host']}) -> {file_info['file_code']}")
            else:
                print(f"✅ Unique: '{normalized}' -> {files[0]['original']} ({files[0]['host']})")
        
        print(f"\n📈 ANALYSIS SUMMARY:")
        print(f"  Raw files collected: {len(all_files)}")
        print(f"  Unique normalized names: {total_unique_files}")
        print(f"  Duplicate groups found: {duplicate_groups}")
        
        # Now test the actual extraction logic
        print(f"\n🎯 TESTING ACTUAL EXTRACTION LOGIC:")
        embed_data = await vm.extract_embed_codes(48)
        print(f"✅ Final extraction result: {len(embed_data)} files")
        
        # Show the final results
        print(f"\n📋 FINAL EXTRACTED FILES:")
        for i, file_data in enumerate(embed_data):
            filename = file_data.get('filename', 'Unknown')
            embed_codes = file_data.get('embed_codes', {})
            hosts = list(embed_codes.keys())
            print(f"  {i+1}. {filename} -> Hosts: {hosts} ({len(hosts)} platforms)")
        
        # Check if we have the exact number the user expects
        expected_count = 8
        actual_count = len(embed_data)
        
        print(f"\n🎯 EXPECTATION CHECK:")
        print(f"  Expected unique files: {expected_count}")
        print(f"  Actual extracted files: {actual_count}")
        
        if actual_count > expected_count:
            print(f"❌ STILL {actual_count - expected_count} EXTRA FILES - DUPLICATES REMAIN")
            
            # Let's analyze what might be causing the extras
            print(f"\n🔍 ANALYZING POTENTIAL DUPLICATE CAUSES:")
            
            # Check for similar filenames that might be treated as different
            for i, file1 in enumerate(embed_data):
                for j, file2 in enumerate(embed_data[i+1:], i+1):
                    name1 = file1['filename'].lower()
                    name2 = file2['filename'].lower()
                    
                    # Remove extensions and common variations
                    base1 = name1.rsplit('.', 1)[0] if '.' in name1 else name1
                    base2 = name2.rsplit('.', 1)[0] if '.' in name2 else name2
                    
                    # Check if they're very similar
                    similarity = calculate_similarity(base1, base2)
                    if similarity > 0.8:  # Very similar
                        print(f"⚠️ POTENTIAL DUPLICATE PAIR:")
                        print(f"    File {i+1}: {file1['filename']}")
                        print(f"    File {j+1}: {file2['filename']}")
                        print(f"    Similarity: {similarity:.2f}")
                        print(f"    Normalized 1: '{vm._normalize_filename(file1['filename'])}'")
                        print(f"    Normalized 2: '{vm._normalize_filename(file2['filename'])}'")
        else:
            print(f"✅ FILE COUNT MATCHES EXPECTATION")
        
        return actual_count, expected_count
        
    finally:
        await vm.close()

def calculate_similarity(str1, str2):
    """Calculate simple string similarity"""
    if not str1 or not str2:
        return 0.0
    
    # Simple Jaccard similarity using character bigrams
    def get_bigrams(s):
        return set(s[i:i+2] for i in range(len(s) - 1))
    
    bigrams1 = get_bigrams(str1.lower())
    bigrams2 = get_bigrams(str2.lower())
    
    if not bigrams1 and not bigrams2:
        return 1.0
    if not bigrams1 or not bigrams2:
        return 0.0
    
    intersection = bigrams1.intersection(bigrams2)
    union = bigrams1.union(bigrams2)
    
    return len(intersection) / len(union) if union else 0.0

async def test_normalization():
    """Test the current normalization logic with sample filenames"""
    print("\n🧪 TESTING NORMALIZATION LOGIC:")
    print("=" * 40)
    
    # Test cases that might be causing issues
    test_files = [
        "Movie.2025.1080p.BluRay.x264.mkv",
        "Movie 2025 1080p BluRay x264.mkv", 
        "Movie.2025.1080p.BluRay.x264-GROUP.mkv",
        "Movie (2025) 1080p BluRay x264.mkv",
        "Movie[2025]1080p.BluRay.x264.mkv",
        "Show.S01E01.1080p.WEB-DL.mkv",
        "Show S01E01 1080p WEB-DL.mkv",
        "Show.S01E01.1080p.WEB-DL.x264-GROUP.mkv"
    ]
    
    vm = VideoHostManager()
    
    print("Testing normalization on sample filenames:")
    for filename in test_files:
        normalized = vm._normalize_filename(filename)
        print(f"  '{filename}' -> '{normalized}'")
    
    # Check for collisions
    normalized_map = {}
    for filename in test_files:
        normalized = vm._normalize_filename(filename)
        if normalized in normalized_map:
            print(f"⚠️ COLLISION: '{filename}' and '{normalized_map[normalized]}' both normalize to '{normalized}'")
        else:
            normalized_map[normalized] = filename

if __name__ == "__main__":
    asyncio.run(test_normalization())
    actual, expected = asyncio.run(analyze_duplicates())
    
    if actual > expected:
        print(f"\n❌ DUPLICATE ISSUE CONFIRMED: {actual - expected} extra files")
        sys.exit(1)
    else:
        print(f"\n✅ DUPLICATE DETECTION WORKING CORRECTLY")
        sys.exit(0)