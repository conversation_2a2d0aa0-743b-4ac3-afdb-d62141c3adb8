#!/usr/bin/env python3
"""
Frontend-Backend Integration Test
Tests if frontend can communicate with backend
"""
import asyncio
import aiohttp
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

FRONTEND_URL = "http://localhost:8080"
BACKEND_URL = "http://localhost:8001"

async def test_frontend_backend_integration():
    """Test frontend-backend integration"""
    async with aiohttp.ClientSession() as session:
        
        # Test 1: Frontend is accessible
        try:
            LOGGER.info("🌐 Testing Frontend Accessibility...")
            async with session.get(FRONTEND_URL, timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    LOGGER.info("✅ Frontend is accessible")
                else:
                    LOGGER.error(f"❌ Frontend not accessible: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ Frontend error: {e}")
            return False
        
        # Test 2: Backend API is accessible
        try:
            LOGGER.info("🔧 Testing Backend API Accessibility...")
            async with session.get(f"{BACKEND_URL}/", timeout=aiohttp.ClientTimeout(total=10)) as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ Backend API accessible: {data}")
                else:
                    LOGGER.error(f"❌ Backend API not accessible: {response.status}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ Backend API error: {e}")
            return False
        
        # Test 3: CORS is working (simulate frontend request to backend)
        try:
            LOGGER.info("🔗 Testing CORS Configuration...")
            headers = {
                'Origin': FRONTEND_URL,
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            }
            async with session.options(f"{BACKEND_URL}/api/stats", headers=headers, timeout=aiohttp.ClientTimeout(total=10)) as response:
                LOGGER.info(f"CORS preflight response: {response.status}")
                cors_headers = {k: v for k, v in response.headers.items() if 'access-control' in k.lower()}
                LOGGER.info(f"CORS headers: {cors_headers}")
                
                # Now test actual request
                async with session.get(f"{BACKEND_URL}/api/stats", headers={'Origin': FRONTEND_URL}, timeout=aiohttp.ClientTimeout(total=10)) as actual_response:
                    if actual_response.status == 200:
                        data = await actual_response.json()
                        LOGGER.info(f"✅ CORS working, got stats: {data}")
                    else:
                        LOGGER.error(f"❌ CORS request failed: {actual_response.status}")
                        return False
        except Exception as e:
            LOGGER.error(f"❌ CORS test error: {e}")
            return False
        
        # Test 4: Test a POST request (simulate frontend uploading)
        try:
            LOGGER.info("📤 Testing POST Request from Frontend...")
            headers = {
                'Origin': FRONTEND_URL,
                'Content-Type': 'application/json'
            }
            payload = {}  # Empty payload for duplicate check
            async with session.post(f"{BACKEND_URL}/api/duplicate/check", json=payload, headers=headers, timeout=aiohttp.ClientTimeout(total=15)) as response:
                if response.status == 200:
                    data = await response.json()
                    LOGGER.info(f"✅ POST request working: {data}")
                else:
                    error_text = await response.text()
                    LOGGER.error(f"❌ POST request failed: {response.status} - {error_text}")
                    return False
        except Exception as e:
            LOGGER.error(f"❌ POST request error: {e}")
            return False
        
        LOGGER.info("🎉 All frontend-backend integration tests passed!")
        return True

async def main():
    """Main test runner"""
    LOGGER.info("🚀 Starting Frontend-Backend Integration Test")
    
    success = await test_frontend_backend_integration()
    
    if success:
        LOGGER.info("✅ Frontend-Backend Integration: FULLY FUNCTIONAL!")
    else:
        LOGGER.error("❌ Frontend-Backend Integration: FAILED!")
    
    return success

if __name__ == "__main__":
    asyncio.run(main())
