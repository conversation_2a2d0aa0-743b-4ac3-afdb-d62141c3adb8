@echo off
setlocal
REM ================================================================
REM AutoUploadBot - FIXED Stop Script (ProactorEventLoop)
REM Stops all AutoUploadBot services
REM WINDOWS SUBPROCESS ISSUE COMPLETELY RESOLVED!
REM ================================================================

cd /d "%~dp0"

echo ================================================================
echo AutoUploadBot Stopper
echo ================================================================
echo Stopping at %DATE% %TIME%

echo.
echo Stopping all AutoUploadBot services...

echo [1/3] Stopping Aria2 RPC server...
taskkill /F /IM aria2c.exe >NUL 2>&1
if %ERRORLEVEL% EQU 0 (
  echo [OK] Stopped Aria2
) else (
  echo [INFO] Aria2 was not running
)

echo [2/3] Stopping Backend server...
taskkill /F /IM python.exe >NUL 2>&1
if %ERRORLEVEL% EQU 0 (
  echo [OK] Stopped Python processes
) else (
  echo [INFO] Python was not running
)

echo [3/3] Stopping Frontend server...
taskkill /F /IM node.exe >NUL 2>&1
if %ERRORLEVEL% EQU 0 (
  echo [OK] Stopped Node.js processes
) else (
  echo [INFO] Node.js was not running
)

REM Force close any remaining AutoUploadBot windows
taskkill /F /FI "WINDOWTITLE eq Aria2 RPC Server*" >NUL 2>&1
taskkill /F /FI "WINDOWTITLE eq AutoUploadBot Backend*" >NUL 2>&1
taskkill /F /FI "WINDOWTITLE eq AutoUploadBot Frontend*" >NUL 2>&1

echo.
echo ================================================================
echo AutoUploadBot stopped successfully!
echo ================================================================
echo All services have been terminated.
echo You can now safely close any remaining windows.
echo ================================================================
echo.
echo Press any key to exit...
pause >NUL

endlocal
exit /b 0
