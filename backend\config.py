# Configuration manager adapted from mirror-leech-telegram-bot
import os
from typing import Dict, List, Optional
from dotenv import load_dotenv
from pathlib import Path

# Ensure we load the .env from the backend folder (next to this file), not just CWD
ENV_PATH = Path(__file__).parent / ".env"
load_dotenv(dotenv_path=ENV_PATH)

class Config:
    """Configuration class following mirror-leech-telegram-bot pattern"""
    
    # Server Configuration
    HOST = os.getenv("HOST", "localhost")
    PORT = int(os.getenv("PORT", "8001"))  # Updated to current backend port
    
    # Google Drive Configuration
    GDRIVE_PRIMARY_FOLDER_ID = os.getenv("GDRIVE_PRIMARY_FOLDER_ID", "")
    GDRIVE_SECONDARY_FOLDER_ID = os.getenv("GDRIVE_SECONDARY_FOLDER_ID", "")
    USE_SERVICE_ACCOUNTS = os.getenv("USE_SERVICE_ACCOUNTS", "True").lower() == "true"
    IS_TEAM_DRIVE = os.getenv("IS_TEAM_DRIVE", "False").lower() == "true"
    
    # Video Hosting API Keys
    STREAMP2P_API_KEY = os.getenv("STREAMP2P_API_KEY", "")
    RPMSHARE_API_KEY = os.getenv("RPMSHARE_API_KEY", "")
    UPNSHARE_API_KEY = os.getenv("UPNSHARE_API_KEY", "")
    FILEMOON_API_KEY = os.getenv("FILEMOON_API_KEY", "")
    
    # Video Hosting API URLs
    STREAMP2P_API_URL = "https://streamp2p.com/api/v1"
    RPMSHARE_API_URL = "https://rpmshare.com/api/v1"
    UPNSHARE_API_URL = "https://upnshare.com/api/v1"
    # Use the official Filemoon API base (no trailing slash). Update if you use a proxy service.
    # The public Filemoon API endpoint used by the project is https://filemoon.sx/api
    FILEMOON_API_URL = os.getenv("FILEMOON_API_URL", "https://filemoon.sx/api")

    # Aria2 Configuration
    ARIA2_HOST = os.getenv("ARIA2_HOST", "127.0.0.1")  # Use IP instead of localhost for ProactorEventLoop compatibility
    ARIA2_PORT = int(os.getenv("ARIA2_PORT", "6800"))
    ARIA2_SECRET = os.getenv("ARIA2_SECRET", "")
    
    # Download Configuration (Server-to-server only)
    MAX_CONCURRENT_DOWNLOADS = int(os.getenv("MAX_CONCURRENT_DOWNLOADS", "3"))
    MAX_CONCURRENT_UPLOADS = int(os.getenv("MAX_CONCURRENT_UPLOADS", "1"))  # Sequential as per user preference
    
    # File Configuration
    EXCLUDED_EXTENSIONS = os.getenv("EXCLUDED_EXTENSIONS", ".txt,.nfo,.md").split(",")
    VIDEO_EXTENSIONS = [".mp4", ".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".m4v"]
    ARCHIVE_EXTENSIONS = [".zip", ".rar", ".7z", ".tar", ".gz", ".bz2"]
    
    # Upload Configuration
    CHUNK_SIZE = int(os.getenv("CHUNK_SIZE", "52428800"))  # 50MB chunks
    UPDATE_INTERVAL = int(os.getenv("UPDATE_INTERVAL", "3"))  # Progress update interval
    
    # Retry Configuration
    MAX_RETRIES = int(os.getenv("MAX_RETRIES", "3"))
    RETRY_DELAY = int(os.getenv("RETRY_DELAY", "5"))
    
    # Embed Code Configuration
    EMBED_EXTRACT_HOURS = int(os.getenv("EMBED_EXTRACT_HOURS", "48"))
    
    # Audio Notifications
    ENABLE_AUDIO_NOTIFICATIONS = os.getenv("ENABLE_AUDIO_NOTIFICATIONS", "True").lower() == "true"
    
    @classmethod
    def validate_config(cls) -> List[str]:
        """Validate required configuration"""
        errors = []
        
        if not cls.GDRIVE_PRIMARY_FOLDER_ID:
            errors.append("GDRIVE_PRIMARY_FOLDER_ID is required")
        if not cls.GDRIVE_SECONDARY_FOLDER_ID:
            errors.append("GDRIVE_SECONDARY_FOLDER_ID is required")
        
        api_keys = [
            ("STREAMP2P_API_KEY", cls.STREAMP2P_API_KEY),
            ("RPMSHARE_API_KEY", cls.RPMSHARE_API_KEY),
            ("UPNSHARE_API_KEY", cls.UPNSHARE_API_KEY),
            ("FILEMOON_API_KEY", cls.FILEMOON_API_KEY),
        ]
        
        for key_name, key_value in api_keys:
            if not key_value:
                errors.append(f"{key_name} is required")
        
        return errors
    
    @classmethod
    def get_aria2_url(cls) -> str:
        """Get Aria2 WebSocket URL"""
        return f"http://{cls.ARIA2_HOST}:{cls.ARIA2_PORT}/jsonrpc"
