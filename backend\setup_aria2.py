# Aria2 setup script for Windows
import subprocess
import sys
import os
import requests
import zipfile
from pathlib import Path

def download_aria2():
    """Download and setup aria2 for Windows"""
    print("Setting up aria2...")
    
    # Create aria2 directory
    aria2_dir = Path("aria2")
    aria2_dir.mkdir(exist_ok=True)
    
    # Download aria2 for Windows
    aria2_url = "https://github.com/aria2/aria2/releases/download/release-1.37.0/aria2-1.37.0-win-64bit-build1.zip"
    aria2_zip = aria2_dir / "aria2.zip"
    
    if not aria2_zip.exists():
        print("Downloading aria2...")
        response = requests.get(aria2_url)
        with open(aria2_zip, "wb") as f:
            f.write(response.content)
        print("Downloaded aria2")
    
    # Extract aria2
    aria2_exe_dir = aria2_dir / "aria2-1.37.0-win-64bit-build1"
    if not aria2_exe_dir.exists():
        print("Extracting aria2...")
        with zipfile.ZipFile(aria2_zip, 'r') as zip_ref:
            zip_ref.extractall(aria2_dir)
        print("Extracted aria2")
    
    return aria2_exe_dir / "aria2c.exe"

def create_aria2_config():
    """Create aria2 configuration file"""
    config_content = """# Aria2 Configuration for AutoUploadBot
# Basic Settings
dir=./downloads
file-allocation=none
continue=true
max-concurrent-downloads=3
max-connection-per-server=16
min-split-size=1M
split=16

# RPC Settings
enable-rpc=true
rpc-listen-all=false
rpc-listen-port=6800
rpc-allow-origin-all=true

# BitTorrent Settings
bt-enable-lpd=true
bt-enable-hook-after-hash-check=true
bt-max-peers=50
bt-request-peer-speed-limit=100K
bt-stop-timeout=0
seed-ratio=0
seed-time=0

# Advanced Settings
auto-file-renaming=false
parameterized-uri=true
enable-http-keep-alive=true
enable-http-pipelining=true
max-tries=3
retry-wait=3
timeout=60
connect-timeout=60

# Logging
log-level=info
console-log-level=info
"""
    
    with open("aria2/aria2.conf", "w") as f:
        f.write(config_content)
    
    print("Created aria2 configuration")

def create_start_script():
    """Create script to start aria2"""
    script_content = """@echo off
echo Starting Aria2 RPC server...
cd /d "%~dp0"
aria2\\aria2-1.37.0-win-64bit-build1\\aria2c.exe --conf-path=aria2\\aria2.conf
pause
"""
    
    with open("start_aria2.bat", "w") as f:
        f.write(script_content)
    
    print("Created start_aria2.bat script")

def main():
    """Main setup function"""
    print("AutoUploadBot Aria2 Setup")
    print("=" * 30)
    
    try:
        # Download and setup aria2
        aria2_exe = download_aria2()
        print(f"Aria2 executable: {aria2_exe}")
        
        # Create configuration
        create_aria2_config()
        
        # Create start script
        create_start_script()
        
        print("\nSetup completed successfully!")
        print("\nTo start aria2:")
        print("1. Run 'start_aria2.bat' in this directory")
        print("2. Keep the aria2 window open while using the bot")
        print("3. Aria2 will be available at http://localhost:6800/jsonrpc")
        
    except Exception as e:
        print(f"Setup failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
