#!/usr/bin/env python3
"""
Comprehensive verification script for video hosting platform uploads
Tests all 4 hosts: Filemoon, StreamP2P, RPMShare, UPnShare
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add backend to path
sys.path.insert(0, str(Path(__file__).parent))

from api.video_hosts import VideoHostManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
LOGGER = logging.getLogger(__name__)

async def test_video_host_uploads():
    """Test upload functionality for all 4 video hosting platforms"""
    print("=" * 80)
    print("🎥 COMPREHENSIVE VIDEO HOST UPLOAD VERIFICATION")
    print("=" * 80)
    
    # Test with a small sample video file
    test_file_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_360x240_1mb.mp4"
    test_filename = "test_video_upload.mp4"
    
    vm = VideoHostManager()
    await vm.initialize()
    
    try:
        results = {}
        
        # Test each host individually
        hosts = ['filemoon', 'streamp2p', 'rpmshare', 'upnshare']
        
        for host in hosts:
            print(f"\n🔍 Testing {host.upper()} upload...")
            
            try:
                # Check if API key is configured
                if not vm.api_keys.get(host):
                    print(f"⚠️  {host}: API key not configured - SKIPPING")
                    results[host] = {
                        'status': 'skipped',
                        'error': 'API key not configured'
                    }
                    continue
                
                print(f"🚀 {host}: Starting upload of {test_filename}")
                
                # Perform upload
                upload_result = await vm.upload_to_host(host, test_file_url, test_filename)
                
                if upload_result.get('success'):
                    file_code = upload_result.get('file_code', 'N/A')
                    embed_url = upload_result.get('embed_url', 'N/A')
                    
                    print(f"✅ {host}: Upload SUCCESS")
                    print(f"   File Code: {file_code}")
                    print(f"   Embed URL: {embed_url}")
                    
                    results[host] = {
                        'status': 'success',
                        'file_code': file_code,
                        'embed_url': embed_url
                    }
                else:
                    error = upload_result.get('error', 'Unknown error')
                    print(f"❌ {host}: Upload FAILED - {error}")
                    results[host] = {
                        'status': 'failed',
                        'error': error
                    }
                    
            except Exception as e:
                print(f"❌ {host}: Upload ERROR - {str(e)}")
                results[host] = {
                    'status': 'error',
                    'error': str(e)
                }
            
            # Small delay between hosts
            await asyncio.sleep(2)
        
        # Summary
        print("\n" + "=" * 80)
        print("📊 UPLOAD VERIFICATION SUMMARY")
        print("=" * 80)
        
        success_count = 0
        failed_count = 0
        skipped_count = 0
        
        for host, result in results.items():
            status = result['status']
            if status == 'success':
                success_count += 1
                print(f"✅ {host.upper()}: SUCCESS")
            elif status == 'failed':
                failed_count += 1
                print(f"❌ {host.upper()}: FAILED - {result['error']}")
            elif status == 'error':
                failed_count += 1
                print(f"💥 {host.upper()}: ERROR - {result['error']}")
            elif status == 'skipped':
                skipped_count += 1
                print(f"⚠️  {host.upper()}: SKIPPED - {result['error']}")
        
        print(f"\n📈 FINAL RESULTS:")
        print(f"   Success: {success_count}")
        print(f"   Failed:  {failed_count}")
        print(f"   Skipped: {skipped_count}")
        print(f"   Total:   {len(hosts)}")
        
        if success_count == len(hosts):
            print("\n🎉 ALL UPLOADS SUCCESSFUL!")
            return True
        elif success_count > 0:
            print(f"\n⚠️  PARTIAL SUCCESS: {success_count}/{len(hosts)} hosts working")
            return True
        else:
            print("\n❌ ALL UPLOADS FAILED!")
            return False
            
    except Exception as e:
        print(f"💥 CRITICAL ERROR: {str(e)}")
        return False
        
    finally:
        await vm.close()

if __name__ == "__main__":
    success = asyncio.run(test_video_host_uploads())
    sys.exit(0 if success else 1)